<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    3620 => 'Yettel Hungary',
    3630 => 'Magyar Telekom',
    36312000 => 'Netfone Telecom',
    36312001 => 'Netfone Telecom',
    3631310 => 'Vodafone',
    3631311 => 'Vodafone',
    3631312 => 'Vodafone',
    3631313 => 'Vodafone',
    3631314 => 'Vodafone',
    3631315 => 'Vodafone',
    3631316 => 'Vodafone',
    3631317 => 'Vodafone',
    3631318 => 'Vodafone',
    36313190 => 'Vodafone',
    36313191 => 'Vodafone',
    36313192 => 'Vodafone',
    36313193 => 'Vodafone',
    36313194 => 'Vodafone',
    36313195 => 'Vodafone',
    36313196 => 'Vodafone',
    36313197 => 'Vodafone',
    36313199 => 'Vodafone',
    3631320 => 'Vodafone',
    3631321 => 'Vodafone',
    3631322 => 'Vodafone',
    3631323 => 'Vodafone',
    3631324 => 'Vodafone',
    3631325 => 'Vodafone',
    3631326 => 'Vodafone',
    3631327 => 'Vodafone',
    3631328 => 'Vodafone',
    36313290 => 'Vodafone',
    36313291 => 'Vodafone',
    36313292 => 'Vodafone',
    3631330 => 'Vodafone',
    3631331 => 'Vodafone',
    3631332 => 'Vodafone',
    36313330 => 'Vidanet',
    36313331 => 'Vidanet',
    36313666 => 'Vodafone',
    36317000 => 'TARR',
    36317001 => 'TARR',
    36317002 => 'TARR',
    36317003 => 'TARR',
    36317004 => 'TARR',
    3631770 => 'UPC',
    3631771 => 'UPC',
    363178 => 'UPC',
    3631790 => 'UPC',
    36501 => 'DIGI',
    36502 => 'DIGI',
    36508 => 'MVM Net',
    36509 => 'MVM Net',
    3670 => 'Vodafone',
];
