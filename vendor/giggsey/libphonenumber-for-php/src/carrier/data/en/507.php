<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    507111 => 'Claro',
    507161 => 'Cable & Wireless',
    507218 => 'Telefónica Móviles',
    507219 => 'Telefónica Móviles',
    50760 => 'Digicel',
    50761 => 'Digicel',
    507616 => 'Telefónica Móviles',
    507617 => 'Claro',
    507618 => 'Claro',
    507619 => 'Telefónica Móviles',
    50762 => 'Claro',
    507630 => 'Claro',
    507631 => 'Claro',
    507632 => 'Claro',
    507633 => 'Cable & Wireless',
    507634 => 'Cable & Wireless',
    507635 => 'Telefónica Móviles',
    507636 => 'Telefónica Móviles',
    507637 => 'Cable & Wireless',
    507638 => 'Telefónica Móviles',
    507639 => 'Telefónica Móviles',
    50764 => 'Telefónica Móviles',
    50765 => 'Cable & Wireless',
    507656 => 'Telefónica Móviles',
    507657 => 'Telefónica Móviles',
    507658 => 'Telefónica Móviles',
    507659 => 'Telefónica Móviles',
    507660 => 'Telefónica Móviles',
    507661 => 'Telefónica Móviles',
    507662 => 'Telefónica Móviles',
    507663 => 'Telefónica Móviles',
    507664 => 'Telefónica Móviles',
    507665 => 'Cable & Wireless',
    507666 => 'Cable & Wireless',
    507667 => 'Cable & Wireless',
    507668 => 'Cable & Wireless',
    507669 => 'Cable & Wireless',
    50767 => 'Cable & Wireless',
    50768 => 'Telefónica Móviles',
    507680 => 'Cable & Wireless',
    507684 => 'Cable & Wireless',
    507687 => 'Cable & Wireless',
    507688 => 'Cable & Wireless',
    50769 => 'Cable & Wireless',
    507692 => 'Telefónica Móviles',
    507693 => 'Telefónica Móviles',
    507697 => 'Telefónica Móviles',
    50781 => 'Mobilphone',
    507872 => 'Cable & Wireless',
    507873 => 'Cable & Wireless',
];
