<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    25410 => 'Airtel',
    25411 => 'Safaricom',
    254120 => 'Telkom',
    254121 => 'Infura',
    254124 => 'Finserve',
    25413 => 'NRG Media Limited',
    25470 => 'Safaricom',
    25471 => 'Safaricom',
    25472 => 'Safaricom',
    25473 => 'Airtel',
    25474 => 'Safaricom',
    254744 => 'Homeland Media',
    254747 => 'JTL',
    25475 => 'Airtel',
    254757 => 'Safaricom',
    254758 => 'Safaricom',
    254759 => 'Safaricom',
    254760 => 'Mobile Pay',
    254761 => 'Airtel',
    254762 => 'Airtel',
    254763 => 'Finserve',
    254764 => 'Finserve',
    254765 => 'Finserve',
    254766 => 'Finserve',
    254767 => 'Sema Mobile',
    254768 => 'Safaricom',
    254769 => 'Safaricom',
    25477 => 'Telkom',
    25478 => 'Airtel',
    25479 => 'Safaricom',
];
