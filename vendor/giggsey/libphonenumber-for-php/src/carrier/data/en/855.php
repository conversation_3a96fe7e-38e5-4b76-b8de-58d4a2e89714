<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    85510 => 'Smart',
    85511 => 'Cellcard',
    85512 => 'Cellcard',
    85513 => 'qbmore/Cadcomms',
    85514 => 'Cellcard',
    85515 => 'Smart',
    85516 => 'Smart',
    85517 => 'Cellcard',
    85518 => 'Seatel',
    8553248 => 'Telecom Cambodia',
    8553348 => 'Telecom Cambodia',
    8553448 => 'Telecom Cambodia',
    8553548 => 'Telecom Cambodia',
    8553648 => 'Telecom Cambodia',
    8554248 => 'Telecom Cambodia',
    8554348 => 'Telecom Cambodia',
    8554448 => 'Telecom Cambodia',
    8555248 => 'Telecom Cambodia',
    8555348 => 'Telecom Cambodia',
    8555448 => 'Telecom Cambodia',
    8555548 => 'Telecom Cambodia',
    85560 => 'Metfone',
    8556248 => 'Telecom Cambodia',
    8556348 => 'Telecom Cambodia',
    8556448 => 'Telecom Cambodia',
    8556548 => 'Telecom Cambodia',
    85566 => 'Metfone',
    85567 => 'Metfone',
    85568 => 'Metfone',
    85569 => 'Smart',
    85570 => 'Smart',
    85571 => 'Metfone',
    8557248 => 'Metfone',
    8557348 => 'Metfone',
    8557448 => 'Metfone',
    8557548 => 'Metfone',
    85577 => 'Cellcard',
    85578 => 'Cellcard',
    85581 => 'Smart',
    85586 => 'Smart',
    85588 => 'Metfone',
    85589 => 'Cellcard',
    85590 => 'Metfone',
    85592 => 'Cellcard',
    85593 => 'Smart',
    85595 => 'Cellcard',
    85596 => 'Smart',
    85597 => 'Metfone',
    85598 => 'Smart',
    85599 => 'Cellcard',
];
