<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * <PERSON>ull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    64201 => 'Callplus',
    64202 => 'Callplus',
    64203 => 'Voyager Internet Limted',
    64204 => 'Sure (Guernsey) New Zealand Limited',
    64205 => 'Vodafone',
    64206 => 'Voyager Internet Limted',
    64208 => 'Vodafone',
    6421 => 'Vodafone',
    6422 => '2degrees',
    64260 => '2degrees',
    64261 => 'Spark',
    64262 => 'Spark',
    64263 => 'Spark',
    64264 => 'Spark',
    64266 => '2degrees',
    64268 => 'Spark',
    64269 => 'Spark',
    6427 => 'Spark',
    64280 => 'Vodafone',
    64281 => 'Sure (Guernsey) New Zealand Limited',
    642820 => 'Voxbone',
    642824 => 'Vodafone',
    642825 => 'Vodafone',
    642829 => 'Callplus',
    64284 => '2degrees',
    64287 => 'Link Telecom',
    642880 => 'Symbio Networks PTY',
    642881 => 'Symbio Networks PTY',
    642882 => 'Symbio Networks PTY',
    642885 => 'Vodafone',
    642886 => 'Vodafone',
    642887 => 'Callplus',
    642888 => 'Callplus',
    642889 => 'Callplus',
    642896 => 'Airnet New Zealand NOW',
    642899 => 'Devoli',
    6429 => 'Vodafone',
];
