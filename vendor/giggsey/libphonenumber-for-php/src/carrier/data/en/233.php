<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    23320 => 'Vodafone',
    23323 => 'airteltiGO',
    23324 => 'MTN',
    23325 => 'MTN',
    23326 => 'airteltiGO',
    23327 => 'airteltiGO',
    23328 => 'Expresso',
    23329 => 'National Security',
    23350 => 'Vodafone',
    23353 => 'MTN',
    23354 => 'MTN',
    23355 => 'MTN',
    23356 => 'airteltiGO',
    23357 => 'airteltiGO',
    23359 => 'MTN',
];
