<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * <PERSON>ull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    38160 => 'A1',
    38161 => 'A1',
    38162 => 'Telenor',
    38163 => 'Telenor',
    38164 => 'Telekom Srbija a.d.',
    38165 => 'Telekom Srbija a.d.',
    38166 => 'Telekom Srbija a.d.',
    381676 => 'GLOBALTEL',
    381677 => 'GLOBALTEL',
    381678 => 'Vectone Mobile',
    38168 => 'A1',
    38169 => 'Telenor',
];
