<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    97650 => 'Unitel',
    97655 => 'Unitel',
    97660 => 'ONDO',
    97666 => 'ONDO',
    97669 => 'Skytel',
    97672 => 'Lime',
    97680 => 'Unitel',
    97681 => 'ONDO',
    97683 => 'G-Mobile',
    97685 => 'Mobicom',
    97686 => 'Unitel',
    97688 => 'Unitel',
    97689 => 'Unitel',
    97690 => 'Skytel',
    97691 => 'Skytel',
    97692 => 'Skytel',
    97693 => 'G-Mobile',
    97694 => 'Mobicom',
    97695 => 'Mobicom',
    97696 => 'Skytel',
    97697 => 'G-Mobile',
    97698 => 'G-Mobile',
    97699 => 'Mobicom',
];
