<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    2710492 => 'Vodacom',
    2710493 => 'Vodacom',
    2710494 => 'Vodacom',
    2712492 => 'Vodacom',
    27134920 => 'Vodacom',
    27134921 => 'Vodacom',
    27134922 => 'Vodacom',
    27134925 => 'Vodacom',
    27144950 => 'Vodacom',
    27144952 => 'Vodacom',
    27144953 => 'Vodacom',
    27144955 => 'Vodacom',
    27154920 => 'Vodacom',
    27154950 => 'Vodacom',
    27154951 => 'Vodacom',
    27164920 => 'Vodacom',
    27174920 => 'Vodacom',
    27184920 => 'Vodacom',
    2719 => 'Telkom Mobile',
    2721492 => 'Vodacom',
    27224950 => 'Vodacom',
    27274950 => 'Vodacom',
    27284920 => 'Vodacom',
    2731492 => 'Vodacom',
    27324920 => 'Vodacom',
    27334920 => 'Vodacom',
    27344920 => 'Vodacom',
    27354920 => 'Vodacom',
    27364920 => 'Vodacom',
    27394920 => 'Vodacom',
    27404920 => 'Vodacom',
    2741492 => 'Vodacom',
    27424920 => 'Vodacom',
    27434920 => 'Vodacom',
    27434921 => 'Vodacom',
    27444920 => 'Vodacom',
    27444921 => 'Vodacom',
    27454920 => 'Vodacom',
    27464920 => 'Vodacom',
    27474950 => 'Vodacom',
    27484920 => 'Vodacom',
    27494920 => 'Vodacom',
    2751492 => 'Vodacom',
    27544950 => 'Vodacom',
    27564920 => 'Vodacom',
    27574920 => 'Vodacom',
    27584920 => 'Vodacom',
    27601 => 'Telkom Mobile',
    27602 => 'Telkom Mobile',
    27603 => 'MTN',
    27604 => 'MTN',
    27605 => 'MTN',
    27606 => 'Vodacom',
    27607 => 'Vodacom',
    27608 => 'Vodacom',
    27609 => 'Vodacom',
    2761 => 'Cell C',
    27614 => 'Telkom Mobile',
    2762 => 'Cell C',
    2763 => 'MTN',
    27636 => 'Vodacom',
    27637 => 'Vodacom',
    27640 => 'MTN',
    27641 => 'Cell C',
    27642 => 'Cell C',
    27643 => 'Cell C',
    27644 => 'Cell C',
    27645 => 'Cell C',
    27646 => 'Vodacom',
    27647 => 'Vodacom',
    27648 => 'Vodacom',
    27649 => 'Vodacom',
    27650 => 'Cell C',
    27651 => 'Cell C',
    27652 => 'Cell C',
    27653 => 'Cell C',
    27654 => 'Cell C',
    27655 => 'MTN',
    27656 => 'MTN',
    27657 => 'MTN',
    27658 => 'Telkom Mobile',
    27659 => 'Telkom Mobile',
    27660 => 'Vodacom',
    27661 => 'Vodacom',
    27662 => 'Vodacom',
    27663 => 'Vodacom',
    27664 => 'Vodacom',
    27665 => 'Vodacom',
    2767 => 'Telkom Mobile',
    27673 => 'Vodacom',
    27674 => 'Vodacom',
    27675 => 'Vodacom',
    2768 => 'Telkom Mobile',
    27686 => 'MTN',
    27687 => 'MTN',
    27688 => 'MTN',
    27689 => 'MTN',
    2771 => 'Vodacom',
    27710 => 'MTN',
    27717 => 'MTN',
    27718 => 'MTN',
    27719 => 'MTN',
    2772 => 'Vodacom',
    2773 => 'MTN',
    2774 => 'Cell C',
    2775 => 'Telkom Mobile',
    2776 => 'Vodacom',
    2778 => 'MTN',
    2779 => 'Vodacom',
    27810 => 'MTN',
    27811 => 'Telkom Mobile',
    27812 => 'Telkom Mobile',
    27813 => 'Telkom Mobile',
    27814 => 'Telkom Mobile',
    27815 => 'Telkom Mobile',
    27816 => 'WBS Mobile',
    27817 => 'Telkom Mobile',
    27818 => 'Vodacom',
    278190 => 'TelAfrica (Wirles Connect)',
    278191 => 'TelAfrica (Wirles Connect)',
    278192 => 'TelAfrica (Wirles Connect)',
    2782 => 'Vodacom',
    2783 => 'MTN',
    2784 => 'Cell C',
    2787086 => 'Vodacom',
    2787087 => 'Vodacom',
    2787158 => 'Vodacom',
    2787285 => 'Vodacom',
    2787286 => 'Vodacom',
    2787287 => 'Vodacom',
    2787288 => 'Vodacom',
    2787289 => 'Vodacom',
    2787310 => 'Vodacom',
];
