<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    '<PERSON><PERSON><PERSON>\\Locale\\Locale' => $vendorDir . '/giggsey/locale/src/Locale.php',
    'Symfony\\Polyfill\\Iconv\\Iconv' => $vendorDir . '/symfony/polyfill-iconv/Iconv.php',
    'Symfony\\Polyfill\\Mbstring\\Mbstring' => $vendorDir . '/symfony/polyfill-mbstring/Mbstring.php',
    'libphonenumber\\AlternateFormatsCountryCodeSet' => $vendorDir . '/giggsey/libphonenumber-for-php/src/AlternateFormatsCountryCodeSet.php',
    'libphonenumber\\AsYouTypeFormatter' => $vendorDir . '/giggsey/libphonenumber-for-php/src/AsYouTypeFormatter.php',
    'libphonenumber\\CountryCodeSource' => $vendorDir . '/giggsey/libphonenumber-for-php/src/CountryCodeSource.php',
    'libphonenumber\\CountryCodeToRegionCodeMap' => $vendorDir . '/giggsey/libphonenumber-for-php/src/CountryCodeToRegionCodeMap.php',
    'libphonenumber\\DefaultMetadataLoader' => $vendorDir . '/giggsey/libphonenumber-for-php/src/DefaultMetadataLoader.php',
    'libphonenumber\\Leniency' => $vendorDir . '/giggsey/libphonenumber-for-php/src/Leniency.php',
    'libphonenumber\\Leniency\\AbstractLeniency' => $vendorDir . '/giggsey/libphonenumber-for-php/src/Leniency/AbstractLeniency.php',
    'libphonenumber\\Leniency\\ExactGrouping' => $vendorDir . '/giggsey/libphonenumber-for-php/src/Leniency/ExactGrouping.php',
    'libphonenumber\\Leniency\\Possible' => $vendorDir . '/giggsey/libphonenumber-for-php/src/Leniency/Possible.php',
    'libphonenumber\\Leniency\\StrictGrouping' => $vendorDir . '/giggsey/libphonenumber-for-php/src/Leniency/StrictGrouping.php',
    'libphonenumber\\Leniency\\Valid' => $vendorDir . '/giggsey/libphonenumber-for-php/src/Leniency/Valid.php',
    'libphonenumber\\MatchType' => $vendorDir . '/giggsey/libphonenumber-for-php/src/MatchType.php',
    'libphonenumber\\Matcher' => $vendorDir . '/giggsey/libphonenumber-for-php/src/Matcher.php',
    'libphonenumber\\MatcherAPIInterface' => $vendorDir . '/giggsey/libphonenumber-for-php/src/MatcherAPIInterface.php',
    'libphonenumber\\MetadataLoaderInterface' => $vendorDir . '/giggsey/libphonenumber-for-php/src/MetadataLoaderInterface.php',
    'libphonenumber\\MetadataSourceInterface' => $vendorDir . '/giggsey/libphonenumber-for-php/src/MetadataSourceInterface.php',
    'libphonenumber\\MultiFileMetadataSourceImpl' => $vendorDir . '/giggsey/libphonenumber-for-php/src/MultiFileMetadataSourceImpl.php',
    'libphonenumber\\NumberFormat' => $vendorDir . '/giggsey/libphonenumber-for-php/src/NumberFormat.php',
    'libphonenumber\\NumberParseException' => $vendorDir . '/giggsey/libphonenumber-for-php/src/NumberParseException.php',
    'libphonenumber\\PhoneMetadata' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneMetadata.php',
    'libphonenumber\\PhoneNumber' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneNumber.php',
    'libphonenumber\\PhoneNumberDesc' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneNumberDesc.php',
    'libphonenumber\\PhoneNumberFormat' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneNumberFormat.php',
    'libphonenumber\\PhoneNumberMatch' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneNumberMatch.php',
    'libphonenumber\\PhoneNumberMatcher' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneNumberMatcher.php',
    'libphonenumber\\PhoneNumberToCarrierMapper' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneNumberToCarrierMapper.php',
    'libphonenumber\\PhoneNumberToTimeZonesMapper' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneNumberToTimeZonesMapper.php',
    'libphonenumber\\PhoneNumberType' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneNumberType.php',
    'libphonenumber\\PhoneNumberUtil' => $vendorDir . '/giggsey/libphonenumber-for-php/src/PhoneNumberUtil.php',
    'libphonenumber\\RegexBasedMatcher' => $vendorDir . '/giggsey/libphonenumber-for-php/src/RegexBasedMatcher.php',
    'libphonenumber\\RegionCode' => $vendorDir . '/giggsey/libphonenumber-for-php/src/RegionCode.php',
    'libphonenumber\\ShortNumberCost' => $vendorDir . '/giggsey/libphonenumber-for-php/src/ShortNumberCost.php',
    'libphonenumber\\ShortNumberInfo' => $vendorDir . '/giggsey/libphonenumber-for-php/src/ShortNumberInfo.php',
    'libphonenumber\\ShortNumbersRegionCodeSet' => $vendorDir . '/giggsey/libphonenumber-for-php/src/ShortNumbersRegionCodeSet.php',
    'libphonenumber\\ValidationResult' => $vendorDir . '/giggsey/libphonenumber-for-php/src/ValidationResult.php',
    'libphonenumber\\geocoding\\PhoneNumberOfflineGeocoder' => $vendorDir . '/giggsey/libphonenumber-for-php/src/geocoding/PhoneNumberOfflineGeocoder.php',
    'libphonenumber\\prefixmapper\\MappingFileProvider' => $vendorDir . '/giggsey/libphonenumber-for-php/src/prefixmapper/MappingFileProvider.php',
    'libphonenumber\\prefixmapper\\PhonePrefixMap' => $vendorDir . '/giggsey/libphonenumber-for-php/src/prefixmapper/PhonePrefixMap.php',
    'libphonenumber\\prefixmapper\\PrefixFileReader' => $vendorDir . '/giggsey/libphonenumber-for-php/src/prefixmapper/PrefixFileReader.php',
    'libphonenumber\\prefixmapper\\PrefixTimeZonesMap' => $vendorDir . '/giggsey/libphonenumber-for-php/src/prefixmapper/PrefixTimeZonesMap.php',
);
