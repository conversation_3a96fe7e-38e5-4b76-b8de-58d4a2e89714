<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    97330 => 'Telecommunications Regulatory Authority',
    97331 => 'Royal Court',
    97332 => 'Batelco',
    97333 => 'VIVA',
    97334 => 'VIVA',
    97335 => 'VIVA',
    97336 => 'zain BH',
    97337 => 'zain BH',
    97338 => 'Batelco',
    973385 => 'Telecommunications Regulatory Authority',
    97339 => 'Batelco',
    97363 => 'VIVA',
    97364 => 'Batelco',
    9736630 => 'zain BH',
    9736633 => 'zain BH',
    9736634 => 'zain BH',
    9736635 => 'zain BH',
    9736636 => 'zain BH',
    9736637 => 'zain BH',
    9736638 => 'zain BH',
    9736639 => 'zain BH',
    973666 => 'zain BH',
    9736670 => 'Batelco',
    9736671 => 'Batelco',
    9736672 => 'Batelco',
    9736673 => 'Batelco',
    9736674 => 'Batelco',
    9736675 => 'Batelco',
    9736676 => 'Batelco',
    9736678 => 'Batelco',
    9736679 => 'Batelco',
    973669 => 'zain BH',
];
