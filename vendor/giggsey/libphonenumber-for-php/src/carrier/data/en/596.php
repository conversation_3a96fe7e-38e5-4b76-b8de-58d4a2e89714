<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    5966960 => 'SFR/Rife',
    59669610 => 'Digicel',
    59669611 => 'Digicel',
    59669616 => 'Digicel',
    59669617 => 'Digicel',
    59669618 => 'Digicel',
    59669619 => 'Digicel',
    5966962 => 'Orange',
    5966963 => 'Orange',
    5966964 => 'Orange',
    59669650 => 'Digicel',
    59669651 => 'Digicel',
    59669652 => 'Digicel',
    59669653 => 'Digicel',
    59669654 => 'Digicel',
    59669655 => 'Orange',
    59669656 => 'Orange',
    59669660 => 'SFR/Rife',
    59669661 => 'SFR/Rife',
    59669662 => 'SFR/Rife',
    59669663 => 'SFR/Rife',
    59669664 => 'SFR/Rife',
    59669665 => 'Free Caraïbe',
    59669666 => 'Free Caraïbe',
    59669667 => 'Free Caraïbe',
    59669668 => 'Free Caraïbe',
    59669669 => 'Free Caraïbe',
    5966967 => 'Digicel',
    5966968 => 'Orange',
    59669687 => 'SFR/Rife',
    59669688 => 'SFR/Rife',
    59669689 => 'SFR/Rife',
    5966969 => 'Digicel',
    59669699 => 'Orange',
    59669727 => 'Digicel',
    59670910 => 'Free Caraïbe',
    59670912 => 'Digicel',
    59670913 => 'Orange',
];
