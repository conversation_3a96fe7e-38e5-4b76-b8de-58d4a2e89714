<?php return array(
    'root' => array(
        'name' => 'sweetcode/pixel-manager-for-woocommerce',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => 'a2c269366e5ef4a892678952c6542a0771686f9b',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'freemius/wordpress-sdk' => array(
            'pretty_version' => '2.12.0',
            'version' => '2.12.0.0',
            'reference' => 'db6f35a2b3d318a53330409dbeab49156ee76dd8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../freemius/wordpress-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'giggsey/libphonenumber-for-php' => array(
            'pretty_version' => '8.13.55',
            'version' => '8.13.55.0',
            'reference' => '6e28b3d53cf96d7f41c83d9b80b6021ecbd00537',
            'type' => 'library',
            'install_path' => __DIR__ . '/../giggsey/libphonenumber-for-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'giggsey/libphonenumber-for-php-lite' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '8.13.55',
            ),
        ),
        'giggsey/locale' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'a5c65ea3c2630f27ccb78977990eefbee6dd8f97',
            'type' => 'library',
            'install_path' => __DIR__ . '/../giggsey/locale',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sweetcode-com/wp-flush' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '6770d6b0ca9ce1b0053ecf0e13be408ad0b2ff55',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sweetcode-com/wp-flush',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sweetcode/pixel-manager-for-woocommerce' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => 'a2c269366e5ef4a892678952c6542a0771686f9b',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '5f3b930437ae03ae5dff61269024d8ea1b3774aa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.20.0',
            'version' => '1.20.0.0',
            'reference' => '39d483bdf39be819deabf04ec872eb0b2410b531',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/action-scheduler' => array(
            'pretty_version' => '3.7.3',
            'version' => '3.7.3.0',
            'reference' => '8aa895a6edfeb92f40d6eddc9dfc35df65da38ae',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../woocommerce/action-scheduler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
