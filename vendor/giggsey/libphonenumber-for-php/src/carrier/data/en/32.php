<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * <PERSON>ull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    3245001 => 'GATEWAY COMMUNICATIONS S.A.',
    324510 => 'DIGI Communications',
    32455 => 'VOO',
    32456 => 'Mobile Vikings/JIM Mobile',
    32460 => 'Proximus',
    324618 => 'N.M.B.S.',
    324630 => 'Lancelot Telecom',
    324631 => 'Lancelot Telecom',
    32465 => 'Lycamobile',
    324650 => 'Telenet',
    324660 => 'Lycamobile',
    324661 => 'Lycamobile',
    324662 => 'Lycamobile',
    324663 => 'Lycamobile',
    324664 => 'Lycamobile',
    324665 => 'Vectone',
    324666 => 'Vectone',
    324667 => 'Vectone',
    324669 => 'Voxbone SA',
    324670 => 'Telenet',
    324671 => 'Join Experience Belgium',
    324672 => 'Join Experience Belgium',
    32467306 => 'Telenet',
    324674 => 'Febo Telecom',
    324676 => 'Lycamobile',
    324677 => 'Lycamobile',
    324678 => 'Lycamobile',
    324679 => 'Interactive Digital Media GmbH',
    32468 => 'Telenet',
    324686 => 'OnOff Télécom SASU',
    324687 => 'Lancelot Telecom',
    324688 => 'Lancelot Telecom',
    324689 => 'Febo Telecom',
    32469 => 'Telenet',
    3247 => 'Proximus',
    324802 => 'TISMI BV',
    324803 => 'Lancelot Telecom',
    324805 => 'Voyacom SPRL',
    324806 => 'Telenet',
    324807 => 'MessageBird BV',
    324809 => 'Ericsson NV',
    32483 => 'Telenet',
    32484 => 'Telenet',
    32485 => 'Telenet',
    32486 => 'Telenet',
    32487 => 'Telenet',
    32488 => 'Telenet',
    32489 => 'Telenet',
    3249 => 'Orange',
];
