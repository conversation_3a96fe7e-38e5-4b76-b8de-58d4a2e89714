/*! Copyright 2025 SweetCode. All rights reserved. */(()=>{var e={348:()=>{function e(){jQuery("#script-blocker-notice").hide()}e(),jQuery((function(){e()}))},605:()=>{jQuery((function(){if("wpm"!==wpmGetPageId())return;let t=[],n={};if(jQuery(".section").closest("tr").hide(),jQuery(".section").each((function(){t.push({slug:jQuery(this).data("sectionSlug"),title:jQuery(this).data("sectionTitle")})})),jQuery(".subsection").each((function(){n[jQuery(this).data("sectionSlug")]=n[jQuery(this).data("sectionSlug")]||[],n[jQuery(this).data("sectionSlug")].push({title:jQuery(this).data("subsectionTitle"),slug:jQuery(this).data("subsectionSlug")})})),t.forEach((function(e){jQuery(".nav-tab-wrapper").append('<a href="#" class="nav-tab" data-section-slug="'+e.slug+'">'+e.title+"</a>")})),jQuery(".nav-tab-wrapper").after(wpmCreateSubtabUlHtml(n)),jQuery(".nav-tab-wrapper a").on("click",(function(e){e.preventDefault(),jQuery(this).addClass("nav-tab-active").siblings().removeClass("nav-tab-active");let s=jQuery(this).data("section-slug");wpmToggleSections(s,t),s in n&&jQuery("ul[data-section-slug="+s+"]").children(":first").trigger("click")})),jQuery(".pmw-subnav-li").on("click",(function(t){t.preventDefault(),t.stopPropagation(),jQuery(this).addClass("pmw-subnav-li-active").removeClass("pmw-subnav-li-inactive").siblings().addClass("pmw-subnav-li-inactive").removeClass("pmw-subnav-li-active"),e(jQuery(this).parent().data("section-slug"),jQuery(this).data("subsection-slug"))})),wpmGetSectionParams()){let e=wpmGetSectionParams();jQuery("a[data-section-slug="+e.section+"]").trigger("click"),!1!==e.subsection&&jQuery("ul[data-section-slug="+e.section+"]").children("[data-subsection-slug="+e.subsection+"]").trigger("click")}else jQuery("a[data-section-slug="+t[0].slug+"]").trigger("click")})),wpmCreateSubtabUlHtml=e=>{let t=Object.keys(e),n="";return t.forEach((function(t){n+='<ul class="pmw-subnav-tabs" data-section-slug="'+t+'">',e[t].forEach((function(e){n+='<li class="pmw-subnav-li pmw-subnav-li-inactive" style="cursor: pointer;" data-subsection-slug="'+e.slug+'">'+e.title+"</li>"})),n+="</ul>"})),n},wpmGetSectionParams=()=>{const e=window.location.search,t=new URLSearchParams(e);return!!t.get("section")&&{section:t.get("section"),subsection:t.get("subsection")}},wpmToggleSections=(e,t)=>{jQuery("#wpm_settings_form > h2").nextUntil(".submit").andSelf().hide(),jQuery(".pmw-subnav-tabs").hide(),jQuery(".pmw-subnav-tabs[data-section-slug="+e+"]").show();let n=t.findIndex((t=>t.slug===e));jQuery("div[data-section-slug="+e+"]").closest("table").prevAll("h2:first").next().nextUntil("h2, .submit").andSelf().show(),wpmSetUrl(t[n].slug)};const e=(e,t)=>{jQuery("#wpm_settings_form > h2").nextUntil(".submit").andSelf().hide(),jQuery("[data-section-slug="+e+"][data-subsection-slug="+t+"]").closest("tr").siblings().andSelf().hide(),jQuery("[data-section-slug="+e+"][data-subsection-slug="+t+"]").closest("table").show(),jQuery("[data-section-slug="+e+"][data-subsection-slug="+t+"]").closest("tr").nextUntil(jQuery("[data-section-slug="+e+"][data-subsection-slug]").closest("tr")).show(),wpmSetUrl(e,t)};wpmSetUrl=(e,t="")=>{const n=window.location.search,s=new URLSearchParams(n);s.delete("section"),s.delete("subsection");let a="section="+e;a+=t?"&subsection="+t:"",history.pushState("","wpm"+e,document.location.pathname+"?page=wpm&"+a),jQuery('input[name ="_wp_http_referer"]').val(wpmGetAdminPath()+"?page=wpm&"+a+"&settings-updated=true")},wpmGetAdminPath=()=>new URL(jQuery("#wp-admin-canonical").attr("href")).pathname,wpmGetPageId=()=>{const e=window.location.search;return new URLSearchParams(e).get("page")},jQuery(document).on("click","[data-section-slug]",(function(){["opportunities","diagnostics","support"].includes(jQuery(this).data("section-slug"))?jQuery(".submit").hide():jQuery(".submit").show()})),jQuery(document).on("click",".pmw.opportunity-dismiss",(function(){jQuery(this).closest(".pmw.opportunity-card").appendTo("#pmw-dismissed-opportunities"),jQuery(this).closest(".opportunity-dismiss").remove()})),jQuery(document).on("click",".advanced-section-link",(e=>{e.preventDefault();let t=jQuery(e.currentTarget).data("as-section"),n=jQuery(e.currentTarget).data("as-subsection");jQuery(`a[data-section-slug=${t}]`).trigger("click"),jQuery(`ul[data-section-slug=${t}]`).children(`[data-subsection-slug=${n}]`).trigger("click")}))},992:()=>{jQuery((function(){jQuery("#debug-info-button").on("click",(()=>{const e=jQuery("#debug-info-textarea")[0];e.select(),e.setSelectionRange(0,99999),wpm.copyTextToClipboard(jQuery("#debug-info-textarea").val());const t=jQuery("#debug-info-success");t.fadeIn(200),setTimeout((()=>t.fadeOut(200)),3e3)})),jQuery("#pmw-pro-version-demo").on("click",(function(){jQuery("#submit").trigger("click")})),document.getElementById("json-settings-file-input")&&document.getElementById("json-settings-file-input").addEventListener("change",wpm.readSettingsFile,!1),document.getElementById("ga4-data-api-credentials-upload-button")&&document.getElementById("ga4-data-api-credentials-upload-button").addEventListener("change",wpm.readGa4DataApiCredentials,!1),document.getElementById("ga4-data-api-credentials-delete-button")&&document.getElementById("ga4-data-api-credentials-delete-button").addEventListener("click",(()=>{wpm.saveGa4DataApiCredentialsToDb({})}),!1),jQuery("#wgact_download_logs_zip").on("click",(function(e){if(e.preventDefault(),jQuery(this).prop("disabled"))return;const t=jQuery(this).data("source"),n=jQuery(this),s=n.text();n.prop("disabled",!0).text("Downloading..."),fetch(pmwAdminApi.root+"pmw/v1/logs/download",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded","X-WP-Nonce":pmwAdminApi.nonce},body:new URLSearchParams({source:t}),credentials:"same-origin"}).then((e=>{if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);return e.blob()})).then((e=>{const t=window.URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="pmw-logs-"+(new Date).toISOString().slice(0,19).replace(/:/g,"-")+".zip",document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(t),document.body.removeChild(a),n.prop("disabled",!1).text(s)})).catch((e=>{console.error("Download error:",e),n.prop("disabled",!1).text(s),alert("Error downloading log files. Please try again.")}))}))})),function(e){e.saveSettingsToDisk=()=>{let t=document.getElementById("export-settings-json").value,n="unknown";try{let e=JSON.parse(t);e.timestamp&&(n=e.timestamp)}catch(e){console.warn("Could not extract timestamp from settings:",e)}t=t.replace(/\n/g,"\r\n");let s=new Blob([t],{type:"text/plain"}),a=document.createElement("a");a.download="pixel-manager-settings_"+n+"_"+e.getDateTimeFromTimestamp(n)+".json",a.href=window.URL.createObjectURL(s),a.target="_blank",a.style.display="none",document.body.appendChild(a),a.click(),document.body.removeChild(a)},e.getDateTimeFromTimestamp=t=>{if("unknown"===t||!t||isNaN(t))return e.getCurrentDateForFileName();let n=new Date(1e3*t);if(pmwAdminApi&&pmwAdminApi.timezone&&"number"==typeof pmwAdminApi.timezone.offset){let e=60*pmwAdminApi.timezone.offset*60*1e3;return n=new Date(n.getTime()+e),n.getUTCFullYear()+"."+("0"+(n.getUTCMonth()+1)).slice(-2)+"."+("0"+n.getUTCDate()).slice(-2)+"_"+("0"+n.getUTCHours()).slice(-2)+"-"+("0"+n.getUTCMinutes()).slice(-2)+"-"+("0"+n.getUTCSeconds()).slice(-2)}return n.getFullYear()+"."+("0"+(n.getMonth()+1)).slice(-2)+"."+("0"+n.getDate()).slice(-2)+"_"+("0"+n.getHours()).slice(-2)+"-"+("0"+n.getMinutes()).slice(-2)+"-"+("0"+n.getSeconds()).slice(-2)},e.getCurrentDateForFileName=()=>{let e=new Date;if(pmwAdminApi&&pmwAdminApi.timezone&&"number"==typeof pmwAdminApi.timezone.offset){let t=60*pmwAdminApi.timezone.offset*60*1e3;return e=new Date(e.getTime()+t),e.getUTCFullYear()+"."+("0"+(e.getUTCMonth()+1)).slice(-2)+"."+("0"+e.getUTCDate()).slice(-2)+"_"+("0"+e.getUTCHours()).slice(-2)+"-"+("0"+e.getUTCMinutes()).slice(-2)+"-"+("0"+e.getUTCSeconds()).slice(-2)}return e.getFullYear()+"."+("0"+(e.getMonth()+1)).slice(-2)+"."+("0"+e.getDate()).slice(-2)+"_"+("0"+e.getHours()).slice(-2)+"-"+("0"+e.getMinutes()).slice(-2)+"-"+("0"+e.getSeconds()).slice(-2)},e.readSettingsFile=t=>{let n=t.target.files[0];if(!n)return;let s=new FileReader;s.onload=function(t){try{JSON.parse(t.target.result)}catch(e){return document.getElementById("settings-upload-status-error").style.display="block",void(document.getElementById("settings-upload-status-error-message").innerHTML="Invalid JSON file.")}let n=JSON.parse(t.target.result);e.saveImportedSettingsToDb(n)},s.readAsText(n)},e.saveImportedSettingsToDb=e=>{fetch(pmwAdminApi.root+"pmw/v1/settings/",{method:"POST",credentials:"same-origin",headers:{"Content-Type":"application/json","X-WP-Nonce":pmwAdminApi.nonce},body:JSON.stringify(e)}).then((e=>e.json())).then((async e=>{e.success?(console.log(e),document.getElementById("settings-upload-status-success").style.display="block",await new Promise((e=>setTimeout(e,1e3))),window.location.reload()):(console.log(e),document.getElementById("settings-upload-status-error").style.display="block")})).catch((e=>{console.error(e),document.getElementById("settings-upload-status-error").style.display="block"}))},e.restoreBackup=e=>{console.log("restoreBackup() - timestamp: ",e),fetch(pmwAdminApi.root+"pmw/v1/options-backup/"+e+"/restore",{method:"POST",credentials:"same-origin",headers:{"Content-Type":"application/json","X-WP-Nonce":pmwAdminApi.nonce}}).then((e=>e.json())).then((async e=>{e?.data?.message&&console.log("restoreBackup() - message: ",e.data.message),e.success?window.location.reload():alert(e.data.message)})).catch((e=>{console.error(e),alert(e.message)}))},e.readGa4DataApiCredentials=t=>{let n=t.target.files[0];if(!n)return;let s=new FileReader;s.onload=function(t){try{JSON.parse(t.target.result)}catch(e){return document.getElementById("ga4-api-credentials-upload-status-error").style.display="block",void(document.getElementById("ga4-api-credentials-upload-status-error-message").innerHTML="Invalid JSON file.")}let n=JSON.parse(t.target.result);e.saveGa4DataApiCredentialsToDb(n)},s.readAsText(n)},e.deleteGa4DataApiCredentials=t=>{e.saveGa4DataApiCredentialsToDb({})},e.saveGa4DataApiCredentialsToDb=e=>{fetch(pmwAdminApi.root+"pmw/v1/ga4/data-api/credentials",{method:"POST",credentials:"same-origin",headers:{"Content-Type":"application/json","X-WP-Nonce":pmwAdminApi.nonce},body:JSON.stringify(e)}).then((e=>e.json())).then((async e=>{e.success?(console.log(e),document.getElementById("ga4-api-credentials-upload-status-success").style.display="block",window.location.reload()):(console.log(e),document.getElementById("ga4-api-credentials-upload-status-error").style.display="block",document.getElementById("ga4-api-credentials-upload-status-error-message").innerHTML="Error message: "+e.data.message)})).catch((e=>{console.error(e),document.getElementById("ga4-api-credentials-upload-status-error").style.display="block"}))},e.getAiBotUrl=()=>{if(pmw_cody?.url)return pmw_cody.url},e.loadAiChatWindow=()=>{let t=document.createElement("div");t.id="pmw-chat-overlay",t.style.position="fixed",t.style.top="0",t.style.left="0",t.style.width="100%",t.style.height="100%",t.style.zIndex="1000",t.style.backgroundColor="rgba(0,0,0,0.5)",t.style.display="flex",t.style.justifyContent="center",t.style.alignItems="center",document.getElementById("wpbody").appendChild(t);var n=document.createElement("div");n.id="pmw-chat-window-container",n.style.width="80%",n.style.height="80%",n.style.zIndex="1001",n.style.display="flex",n.style.justifyContent="center",n.style.alignItems="center",n.style.marginLeft="160px",t.appendChild(n);var s=document.createElement("iframe");s.src=e.getAiBotUrl(),s.style.width="100%",s.style.height="100%",s.style.border="none",n.appendChild(s),t.addEventListener("click",(function(e){console.log(e.target),"pmw-chat-overlay"===e.target.id&&(console.log("overlay.addEventListener() - close chat window"),t.remove())})),document.addEventListener("keydown",(function(e){"Escape"===e.key&&t.remove()}),!0)},e.codyAvailable=()=>!(void 0===window.pmw_cody||!window.pmw_cody?.available),e.ltvRecalculation=e=>{const t=e=>{let t=document.getElementsByClassName("ltv-message");for(let e=0;e<t.length;e++)t[e].style.display="none";document.getElementById("ltv-message-error").style.display="block",document.getElementById("ltv-message-error-text").innerHTML=e},n=()=>{let e=document.getElementsByClassName("ltv-message");for(let t=0;t<e.length;t++)e[t].style.display="none";document.getElementById("ltv-message-error").style.display="none"};fetch(pmwAdminApi.root+"pmw/v1/ltv/",{method:"POST",credentials:"same-origin",headers:{"Content-Type":"application/json","X-WP-Nonce":pmwAdminApi.nonce},body:JSON.stringify({action:e.target.dataset.action})}).then((e=>e.json())).then((e=>{e.success?(console.log(e),e.data.status.is_scheduled&&(n(),(()=>{let e=document.getElementsByClassName("ltv-button-text");for(let t=0;t<e.length;t++)e[t].style.display="none";document.getElementById("ltv-instant-recalculation-button-text").style.display="block"})(),document.getElementById("ltv-schedule-recalculation-confirmation-message").style.display="block"),e.data.status.is_running&&(document.getElementById("wgact_ltv_recalculation").disabled=!0,n(),document.getElementById("ltv-running-recalculation-confirmation-message").style.display="block"),e.data.status.is_running||e.data.status.is_scheduled||t(e.data.message)):(console.error(e),t(e.data.message))})).catch((e=>{console.error(e),t(e)}))},e.copyTextToClipboard=e=>{navigator.clipboard.writeText(e).catch((e=>{console.error("Failed to copy: ",e)}))}}(window.wpm=window.wpm||{},jQuery),document.addEventListener("DOMContentLoaded",(()=>{const e=document.getElementById("pmw-pro-version-demo");if(!e)return;if(e.querySelector("input:checked"))return;const t=window.innerWidth/2;document.addEventListener("mousemove",(n=>{const s=((t,n)=>{const s=e.getBoundingClientRect();return Math.sqrt(Math.pow(t.clientX-s.left,2)+Math.pow(t.clientY-s.top,2))/n})(n,t);(t=>{t>1&&(t=1);const n=100-80*t;e.classList.add("pmw-opacity-transition"),e.style.opacity=n/100})(s)}))}),!0),document.addEventListener("DOMContentLoaded",(()=>{"fms"===wpmDataLayer.version.distro&&(wpm.codyAvailable()?document.getElementById("pmw-chat-li").style.display="block":console.log("DOMContentLoaded - chatbot is not available"))}),!0),jQuery((()=>{document.querySelector("#wgact_ltv_recalculation")&&document.querySelector("#wgact_ltv_recalculation").addEventListener("click",(e=>{e.preventDefault(),wpm.ltvRecalculation(e)})),document.querySelector("#pmw_stop_ltv_calculation")&&document.querySelector("#pmw_stop_ltv_calculation").addEventListener("click",(e=>{e.preventDefault(),wpm.ltvRecalculation(e)})),jQuery((()=>{jQuery(document).on("click",".pmw-restore-backup-button",(function(e){e.preventDefault();const t=jQuery(this).data("timestamp");t&&wpm.restoreBackup(t)}))}))})),jQuery((()=>{document.querySelector("#wgact_copy_log_file_links")&&document.querySelector("#wgact_copy_log_file_links").addEventListener("click",(e=>{e.preventDefault();const t=JSON.parse(e.target.dataset.links);let n="";for(let e=0,s=t.length;e<s;e++)n+=t[e]+"\n";wpm.copyTextToClipboard(n);let s=document.getElementById("wgact_copy_log_file_links");s.style.width=getComputedStyle(s).width;const a=s.innerHTML;s.innerHTML=e.target.dataset.textCopied,setTimeout((function(){s.innerHTML=a}),3e3)}))})),jQuery((()=>{jQuery(document).on("keydown","input, textarea, select",(function(e){if("Enter"===e.key&&!e.shiftKey&&!e.ctrlKey&&!e.altKey){const t=jQuery(e.target);if(t.is("input")&&t.closest("form").length>0){const n=t.closest("form")[0],s=jQuery(n).find("#submit");s.length>0&&(e.preventDefault(),s.click())}}}))}))}},t={};function n(s){var a=t[s];if(void 0!==a)return a.exports;var o=t[s]={exports:{}};return e[s](o,o.exports,n),o.exports}n(992),n(348),n(605)})();
//# sourceMappingURL=wpm-admin.p1.min.js.map