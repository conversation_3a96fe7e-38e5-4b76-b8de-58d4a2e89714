<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    843 => 'Viettel',
    8430 => 'MobiFone',
    8451 => 'MobiFone',
    8452 => 'Vietnamobile',
    8455 => 'Reddi',
    8456 => 'Vietnamobile',
    8458 => 'Vietnamobile',
    8459 => 'G-Mobile',
    847 => 'MobiFone',
    8481 => 'Vinaphone',
    8482 => 'Vinaphone',
    8483 => 'Vinaphone',
    8484 => 'Vinaphone',
    8485 => 'Vinaphone',
    8486 => 'Viettel',
    8487 => 'Vinaphone',
    8488 => 'Vinaphone',
    8489 => 'MobiFone',
    8490 => 'MobiFone',
    8491 => 'Vinaphone',
    8492 => 'Vietnamobile',
    8493 => 'MobiFone',
    8494 => 'Vinaphone',
    8496 => 'Viettel',
    8497 => 'Viettel',
    8498 => 'Viettel',
    84993 => 'G-Mobile',
    84994 => 'G-Mobile',
    84995 => 'G-Mobile',
    84996 => 'G-Mobile',
    84997 => 'G-Mobile',
    84998 => 'Indochina Telecom',
    84999 => 'Indochina Telecom',
];
