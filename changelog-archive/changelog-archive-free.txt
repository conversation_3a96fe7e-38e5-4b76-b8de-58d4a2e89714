= 1.44.2 = 03.10.2024

* Tweak: Reenabled the new variations selection logic, added some safeguards and only activate it in case the PMW is lazy loaded.
* Tweak: Added support for GA4 add_payment_info and add_shipping_info events for WooCommerce Blocks.
* Tweak: Updated vendor packages.
* Tweak: Handle missing product IDs in data layer on view-item events more gracefully.
* Tweak: Updated a documentation link.
* Fix: Fixed a wrong status output in the advanced Google settings for the enhanced e-commmerce event output.

= 1.44.1 = 11.09.2024

* Fix: Temporary roll back to old variations selection logic to prevent issues with some themes.

= 1.44.0 = 05.09.2024

* New: Filters to add custom parameters for Google Analytics orders and order items.
* Tweak: Bumped up WP version compatibility to 6.6
* Tweak: Updated vendor packages.
* Tweak: Update the translation file.
* Tweak: Improved the logic to detect a preselected variation on the product page on page load, especially with Lazy Loading enabled.
* Tweak: Added safeguards to prevent warnings when generating the payment gateway accuracy report for the UX.
* Fix: Fixed a documentation link.

= 1.43.5 = 16.07.2024

* Tweak: Updated vendor packages.
* Tweak: Improved logic to detect orders overview page and order edit pages.
* Tweak: Renamed the pmw_custom_cogs_meta_key filter to pmw_custom_cogs_product_meta_key.

= 1.43.4 = 26.06.2024

* Tweak: Added filters to adjust the output of the order value for statistics.
* Tweak: Few improvements in the admin scripts to avoid unnecessary console errors.
* Tweak: Bumped up WC version compatibility to 9.0
* Fix: Removed the experimental Polyfill.io feature as the service has been compromised. (This feature was not active by default and had to be enabled manually by enabling an experimental filter.)

= 1.43.3 = 18.06.2024

* Tweak: Ensured `get_transient_identifiers()` returns an array.
* Tweak: Added a few semicolons into the JavaScript inline code output to bolster the code against subpar JavaScript "optimizers" that otherwise may break the code.
* Tweak: Added filters for Google Site Kit. If Google Ads and/or GA4 are enabled in the Pixel Manager, it will prevent Google Site Kit from adding its own tracking code.
* Fix: Moved some GA4 JavaScript library functions from the premium to the free version, as they are called in the free version as well.
* Fix: Added a safeguard to prevent a fatal error in case a parent product can't be loaded.
* Fix: Fixed a bug when consent was changed in a Termly CMP.

= 1.43.2 = 05.06.2024

* Tweak: Refactored directory structure.
* Tweak: Updated Google's IP ranges in ip_services.js
* Tweak: Added add_shipping_info event processing for GA4.
* Tweak: Added add_payment_info event processing for GA4, Facebook (Meta).
* Tweak: Added a safeguard and an error message when trying to upload invalid JSON settings files.
* Tweak: Added a safeguard to REST API endpoint tester to not follow redirects. Also added a proper check for the response message.
* Tweak: Removed Partytown Web Worker experiment.
* Tweak: Added add-to-cart event listener for Doofinder add-to-cart buttons.
* Fix: Fixed a bug that would prevent consent from being processed immediately after consent was given through Cookiebot (only worked on page reload).

= 1.43.1 = 21.05.2024


= 1.43.0 = 21.05.2024

* Tweak: Added a safeguard into the product container watcher to prevent an undefined error.
* Tweak: Added a safeguard in case attributes for the console logger are not passed correctly.
* Tweak: Bumped up WooCommerce version compatibility to 8.9

= 1.42.8 = 06.05.2024

* Fix: Fixed a bug with a filter for the Facebook for WooCommerce plugin that matches the product ID with the catalog ID.

= 1.42.7 = 05.05.2024

* Tweak: Added more index.php files to prevent directory listing.
* Tweak: Added an override for the Facebook for WooCommerce plugin to match the catalog ID with the product ID set in the Pixel Manager for the Facebook pixel.
* Fix: .br and .gz compressed JavaScript files got corrupted after updating a component of the build process. This update fixes the corrupted files.

= 1.42.6 = 02.05.2024

* Tweak: Refactored various parts of the codebase to streamline the code.

= 1.42.5 = 25.04.2024

* Tweak: Improved front-end consent state logging.
* Tweak: Changed event listeners to be more efficient.
* Tweak: Refactored various parts of the codebase to streamline the code.
* Tweak: Updated vendor libraries.
* Fix: Fixed consent event listener loading for Cookiebot and CookieYes.

= 1.42.4 = 23.04.2024

* Tweak: Added Virginia to the US states for the explicit consent management.
* Tweak: On first load CookieYes sets the categories with no values, just empty strings. Not "yes" or "no" like in all subsequent loads. This update handles this case.
* Tweak: Added a backwards compatibility process for website visitors that have saved a consent status before the latest update.
* Tweak: Streamlined large parts of the codebase.
* Tweak: Updated vendor libraries.
* Fix: Fixed a bug that would prevent to fire a view_item event on opening variable products with preselected variations.

= 1.42.3 = 16.04.2024

* Tweak: Better browser geo region detection for the consent mode. This is helpful if a website manager requires explicit consent from visitors located in California.
* Tweak: Block same page events after consent has been removed for various pixels.
* Fix: Fixed a text output for CookieYes activation status.

= 1.42.2 = 11.04.2024

* Fix: Fixed a edge case bug that could prevent the save button in the UX to render.

= 1.42.1 = 10.04.2024

* Fix: Removed consent update reload triggers for some CMPs, as in some cases the CMPs emit CMP click events on each page load and lead to a reload loop.

= 1.42.0 = 10.04.2024

* Tweak: Updated all vendor packages.
* Fix: Fixed a bug when reading the consent cookie of the free version of the Cookie Compliance CMP (by hu-manity.co).
* Fix: Fixed a bug when reading the consent cookie of the old versions of the CookieYes CMP.
* Fix: Fixed a bug that would throw an error if the site_id was not set.

= 1.41.1 = 09.04.2024

* Tweak: Extended the auto-off delay for HTTP request log feature.
* Tweak: Enabled the Google Consent Mode as default.
* Tweak: Removed Google Analytics Universal codes.
* Tweak: Refactored internal class names and file structure and the autoload mechanism.
* Tweak: Removed the author tab from the settings page.
* Tweak: Refactored the consent regions logic and implemented an automatic API call for better handling of unrestricted regions.
* Tweak: Added filter to suppress the version info output in the dev console.
* Tweak: Refactored sections for shop and consent management in the settings page.
* Tweak: Refactored the entire consent management logic to be more flexible and easier to maintain.
* Fix: Fixed a bug that could prevent the Google Consent Mode activation.

= 1.41.0 = 21.03.2024

* Tweak: Added more page data output to the wpmDataLayer.
* Tweak: Workaround for the Astra theme show_variation bug that triggers multiples times on page load on the product page.
* Tweak: Added safeguard if server requests for fetching product data is missing input data.
* Tweak: Added tweaks if Cookiebot is active. (Exclusions for script auto blocking and their Google Consent Mode)
* Tweak: Removed Google Optimize as it was decommissioned by Google.
* Tweak: Added safeguard to not run WooCommerce specific code on non-WooCommerce sites.
* Tweak: Updated Complianz CMP cookie names.
* Tweak: Added support for the WP Cookie Consent CMP.
* Tweak: Bumped up WooCommerce version compatibility to 8.7
* Tweak: Bumped up WordPress version compatibility to 6.5
* Tweak: Updated vendor libraries.

= 1.40.1 = 01.03.2024

* Fix: Fixed a bug that could prevent detecting if the purchase conversions were fired.

= 1.40.0 = 27.02.2024

* New: Added support for the Google Consent Mode v2 in the free version of the Pixel Manager.
* Tweak: Refactored a few internal functions.

= 1.39.0 = 19.02.2024

* Tweak: Added dashboard message for available opportunities.
* Tweak: Bumped up WooCommerce version compatibility to 8.6
* Tweak: Updated vendor libraries.
* Tweak: Removed a few unnecessary parameters.


= 1.38.0 = 12.02.2024

* Tweak: Added LTV calculation opportunity to the opportunities tab.
* Tweak: Added console logging for pixel events across platforms.
* Tweak: Improved sanitization of settings strings.
* Tweak: Improved Google Ads conversion ID and conversion label string extraction when saving the settings.
* Tweak: Added exclusion rule for inline HTML scripts in case the Iubenda CMP is active.

= 1.37.1 = 01.02.2024

* Tweak: Disabled the automatic LTV recalculation.
* Tweak: Added a button to stop an active LTV recalculation.
* Tweak: Added an option to enable or disable the order level LTV calculation.
* Tweak: Added more debug log output for Meta CAPI subscription events.

= 1.37.0 = 30.01.2024

* New: Opportunities tab that shows opportunities to improve tracking and marketing performance.
* Tweak: Updated the order modal created by the Pixel Manager and added LTV values to it.
* Tweak: Renamed the setting "order total logic" to "marketing value logic".
* Tweak: Updated the translation file.
* Tweak: Added option to disable the automatic lifetime value calculation.
* Tweak: Added an automatic library version checker that shows an error message in the console if the library version is not matching the currently installed plugin version.
* Tweak: Added a front-end error log output in case the gtag.js library can't be loaded.
* Tweak: Added a safeguard for calls to as_has_scheduled_action().

= 1.36.0 = 16.01.2024

* New: Added GA4 enhanced e-commerce events to the free version.
* New: Added a new logger tab to the settings page. It allows you to enable logging from within the plugin.
* Tweak: Improved handling of session and persistent client data.
* Tweak: Improved handling of saving and using the referrer.
* Tweak: Implemented a possible fix for the Facebook IPv6 vs IPv4 warning.
* Tweak: Updated vendor packages.
* Tweak: Added new logger for better debugging.
* Tweak: Refactored the tracking value variable into an object with more properties.
* Tweak: Refactored the order item COG retrieval to get the COG value from the order, if available. Otherwise, it will use the current COG value from the product.
* Tweak: Optimized shortcode trigger for the Meta pixel.
* Tweak: Reordered the main subsection tabs.

= 1.35.0 = 19.12.2023

* New: AI support chat bot.
* Fix: Safeguard for WooCommerce block editors that can cause a fatal error in rare cases.

= 1.34.1 = 14.12.2023

* Tweak: Removed user data output for the free version as it is not required.
* Tweak: Bumped up WordPress version compatibility to 6.4.
* Tweak: Bumped up WooCommerce version compatibility to 8.4
* Tweak: Declare Cart and Checkout Blocks compatibility.
* Tweak: Updated the Facebook pixel ID validation to allow for longer IDs.
* Tweak: Updated the jQuery ready event detection to avoid deprecation warnings.
* Fix: Don't call VarnishPurger's execute_purge() statically as it is not a static function.
* Fix: Added a safeguard to avoid render issues on empty block-activated cart pages.

= 1.34.0 = 10.10.2023

* New: Added support for WP Consent API.
* Tweak: Bumped up WooCommerce version compatibility to 8.1
* Tweak: Added more how-to videos.
* Tweak: Added root: null to the intersection observer options to avoid issues that occur on a small subset of browsers.
* Tweak: Improve the function to retrieve the productId and quantity on cart content updates.
* Tweak: Warning if transients are deactivated.
* Fix: Fixed the fallback function to retrieve the product ID on product pages that don't use the regular WooCommerce product template.
* Fix: Added safeguard for pure WP installs and WC is not active.

= 1.33.1 = 13.09.2023

* Tweak: Added an autodetect algorithm for product template elements which help tracking view_item_list events.
* Tweak: Added a new begin_checkout CSS trigger for FunnelKit.
* Tweak: Another fallback to retrieve the product ID in case the ID is saved in the value attribute for custom product pages.
* Tweak: Renamed the internal value for TikTok for the Real Cookie Banner from tiktok to tik-tok-pixel.
* Tweak: Added a filter to set the maximum orders for calculating the clv.
* Tweak: Adjusted gtag container ID retrieval order to work around an issue that can happen for GA4 accounts with multiple data streams.
* Tweak: Updated the Reddit advertiser ID validation to allow for a wider range of IDs.
* Tweak: Better logging when GA4 data API credentials can't be uploaded.
* Tweak: Added new video how tos.
* Fix: Put the license expired warning behind a hook to avoid the headers already sent error on some installs.
* Fix: Allow empty client_id for GA4 data API credentials.
* Fix: Fixed the tax price output for additional currencies when WPML Multicurrency is active.
* Fix: Fixed a bug in the view_item_list template detector for the niche case when detecting a list of one single product.

= 1.33.0 = 09.08.2023

* Tweak: Updated vendor libraries.
* Tweak: Removed an unnecessary API call from the free version on the order view page.
* Tweak: Bumped up WordPress version compatibility to 6.3
* Tweak: Updated vendor libraries.
* Tweak: UX improvements.
* Tweak: Added hooks for third-party tools to print data layer product details.
* Tweak: Fixes an issue where Elementor widgets would show the PMW scripts as visible outputs under rare conditions.
* Tweak: Bumped up WooCommerce compatibility to 8.0.
* Fix: Fixed Litespeed ESI implementation. Now only the Pixel Manager ESI block will be excluded from caching for logged in users (not the entire page). (except the woocommerce.com distribution which requires a patch in WP core)
* Fix: Put the automatic phone and link click tracking info in the UX behind a premium feature flag as it is only available in the premium version.

= 1.32.5 = 19.07.2023

* Tweak: Support for the Iubenda CMP.
* Tweak: Also flush cache on the first option save.
* Tweak: Added cache purge for Nginx Helper (Nginx or Redis) if the Nginx Helper plugin is active.
* Tweak: Added cache purge for Nginx Helper.
* Tweak: Added cache purge for Proxy Cache Purge.
* Tweak: Bumped up WooCommerce compatibility to 7.9
* Fix: Fixed a user data retrieval bug when the main administrator views an order confirmation page.
* Fix: Fixed a PMW Lazy Load activation bug.

= 1.32.4 = 11.07.2023

* Tweak: Made the pmw:ready event trigger independent of jQuery.
* Tweak: Added a begin_checkout event trigger for the XT Floating Cart for WooCommerce plugin.
* Tweak: CookieYes updated their cookie names. This update includes checks for the new cookies.
* Tweak: Various smaller UX improvements.
* Tweak: Removed the WC requirement for the wp.org distribution.
* Fix: Fixed the consent detection for CookieScript when the Google Consent Mode is enabled in CookieScript.

= 1.32.3 = 05.07.2023

* Tweak: Disabled Google Analytics Universal.
* Tweak: Updated vendor libraries.
* Tweak: Several smaller UX improvements.

= 1.32.2 = 30.06.2023

* New: Support for the Real Cookie Banner by devowl.io
* Tweak: Refactored saving the order status to optimize compatibility with cached nonces on purchase confirmation pages.
* Tweak: Added filters that can be used to add more selectors for add-to-cart and begin-checkout buttons.
* Tweak: Enabled sslverify unless on localhost.
* Tweak: Updated the Borlabs autoconfiguration to re-enable cookie groups and cookies that should be active.
* Tweak: Refactored and streamlined input sanitization.
* Tweak: Added a new pmw_order_fees filter.
* Tweak: Added more debugging info to the debugging page.

= 1.32.1 = 23.06.2023

* Tweak: Added a safeguard to prevent fatal errors if the Action Scheduler could not be loaded.
* Tweak: Safeguard if Lazy Loading is active and the wpmDataLayer was modified by another plugin.
* Tweak: Added possible solution to prevent Complianz from blocking the Pixel Manager configuration script.
* Tweak: Bumped up WC version compatibility to 7.8

= 1.32.0 = 13.06.2023

* Tweak: Unified the user data output for the wpmDataLayer.
* Tweak: New filter to enable Facebook hybrid mobile app events.
* Tweak: Updated the JavaScript email validation regex.
* Tweak: Added general filter to disable subscription renewal tracking.
* Tweak: Added filter to disable Google Analytics subscription renewal tracking.
* Tweak: Changed the wpm_get_ga_cid_logger filter to pmw_ga_cid_logger.
* Tweak: Changed the wpm_conversion_value_filter name to pmw_marketing_conversion_value_filter.
* Tweak: Changed multiple wpm_* filters to pmw_*.
* Tweak: Numerous syntax improvements.
* Tweak: Renamed the pmw_cookie_consent_management_loaded event to pmwCookieConsentManagementLoaded
* Tweak: Added an escape to add_query_arg in the order-columns class.
* Tweak: Adjusted an escape function in the shortcodes class to use a better suited escape function.
* Tweak: Updated the vendor libraries.
* Fix: Fixed a load order issue which would cause a wpm.updateGoogleConsentMode error if called on the pmwCookieConsentManagementLoaded event.

= 1.31.1 = 24.05.2023

* Tweak: Updated all vendor libraries.
* Tweak: Optimized the safeguard for the order output on the thankyou page.

= 1.31.0 = 23.05.2023

* Tweak: Better handling of options.
* Tweak: Wrapped the debug info output into a try catch block to avoid errors on a small subset of servers.
* Tweak: WC 7.7 compatibility.
* Tweak: Safeguard to avoid a rare fatal error on an in_array() call on a non array.
* Tweak: Better check if wpmDataLayer is fully loaded. And added an error log if third-party plugins are causing issues loading the data layer.
* Tweak: Moved the pmw_tracking_accuracy_analysis from WP Cron to the Action Scheduler.
* Tweak: Updated vendor libraries.
* Tweak: Updated and optimized shortcode output.
* Tweak: Automatic setup of Borlabs Cookie settings.
* Tweak: Better handling of setting strings that contain unwanted characters.
* Tweak: Simplified settings strings validations.


= 1.30.6 = 04.05.2023

* Tweak: More compatible way to check for redirects for the debug info.
* Tweak: Syntax optimizations.
* Tweak: Built in safeguards for debug info URL tests.
* Tweak: Adjusted tel and mailto click events for GA4.
* Tweak: Use of more compatible way to check if servers are reachable.

= 1.30.5 = 19.04.2023

* Fix: Use Litespeed ESI exclusion only for logged in users.

= 1.30.4 = 19.04.2023

* New: Added support for Termly CMP.
* Tweak: Litespeed ESI compatibility
* Tweak: Bumped up WooCommerce compatibility to 7.6.
* Tweak: Bumped up WP version compatibility to 6.2
* Tweak: Removed one trigger for the BeginCheckout event as it was too generic and on some themes inflated the number of events.
* Tweak: Improved product ID detection on add-to-cart events for non standard WooCommerce product types.
* Tweak: Updated vendor libraries.
* Tweak: Bumped up the Facebook API version to v16.0.
* Fix: Fixed variation output for pixel based purchase event order output.

= 1.30.3 = 14.02.2023

* Tweak: Deprecated a PWM filter to add paid order statuses and info to use the WooCommerce internal filter instead.
* Tweak: Improved safety checks for order confirmation page. (Thanks https://github.com/Willianvdv for raising this.)

= 1.30.2 = 26.01.2023

* Fix: Fixed brotli and gzip compressed JavaScript files.

= 1.30.1 = 26.01.2023

* Tweak: Added workaround if a WP install plugin or theme loads a very old Composer version that doesn't contain the setApcuPrefix function. (Thanks @johnbillion for the solution)
* Tweak: Brought back filter to disable the WooCommerce Google Analytics Integration, but only if GA3 and GA4 are both simultaneously enabled in the PMW.

= 1.30.0 = 24.01.2023

* Tweak: Workaround for an elusive bug in the consent regions in_array call.
* Tweak: Updated vendor libraries.
* Tweak: Further optimized loading composer libraries.
* Tweak: Added filters to specify the fbevents.js version to be loaded.
* Tweak: Removed filter to disable the WooCommerce Google Analytics Integration plugin when GA3 is enabled in the Pixel Manager.

= 1.29.1 = 12.01.2023

* Tweak: Proper output of optimized composer autoload files.
* Tweak: Adjusted define() to avoid warnings with old PHP versions.

= 1.29.0 = 12.01.2023

* Tweak: Safeguard if wp-hooks don't get loaded in proper order or not at all.
* Tweak: Bumped up WC version compatibility to 7.3
* Tweak: Various code improvements.
* Tweak: Optimize composer autoload files.
* Fix: Fixed a type check bug that happened in rare cases for grouped products.

= 1.28.2 = 03.01.2023

* Fix: Fixed an issue when activating the lazy loading feature of the plugin for the wc.com distribution.

= 1.28.1 = 03.01.2023

* Fix: Put new experimental opportunities dashboard notification behind a feature flag.

= 1.28.0 = 03.01.2023

* Tweak: Added one more case insensitive flag to define() calls to prevent future PHP warnings.
* Tweak: Various code syntax improvements.
* Tweak: Bumped up WC tested up to version to 7.2.
* Tweak: Added date output of last run of payment gateway analysis.
* Tweak: Implemented better guard clause to avoid fatal error when a product page doesn't link to a product in the database.
* Tweak: Added support for StoreApps Custom Thankyou Page plugin.
* Tweak: On the settings page hide the save button on tabs that only contain information and no settings.
* Fix: Fixed Google Ads cart item data reporting.
* Fix: Fixed a typo for groupsObject in the OneTrust cookie detection (thanks @armandsdz https://wordpress.org/support/topic/typo-in-js-file/)
* Fix: Fixed Cookiebot on-cookie-accept event listener.
* Fix: HPOS compatibility declaration now returns true as it should.
* Fix: Get PMW payment accuracy report scheduler to schedule the report correctly.

= 1.27.9 = 09.12.2022

* Tweak: Added case insensitive flag to define() calls to prevent future PHP warnings.
* Tweak: Disabled some loggers.
* Tweak: Adjusted the woocommerce_ga_disable_tracking filter to only be used if GA is active in the Pixel Manager.
* Tweak: Refactored logging.
* Tweak: Improved a few descriptions.

= 1.27.8 = 06.12.2022

* Tweak: Added various type validations for REST routes.
* Tweak: Increase Gutenberg compatibility for all-products block, cart block and checkout block.
* Tweak: Streamlined code for backend product retrieval and excluded saving product transients on cart and checkout pages.
* Tweak: Made sure that the action scheduler hooks always get registered (even when an Ajax request is happening).

= 1.27.7 = 24.11.2022

* Fix: Fixed Google Ads dynamic ID output for custom filters product ID filters.

= 1.27.6 = 23.11.2022

* Fix: Fixed an HPOS check for the debug info tab.

= 1.27.5 = 23.11.2022

* Tweak: Prioritize order billing info over logged-in user billing info.
* Tweak: If available, also subtract order fees (such as PayPal or Stripe fees) from the subtotal.
* Tweak: Various UX improvements.
* Tweak: Updated documentation links.


= 1.27.4 = 18.11.2022

* Tweak: Various UX improvements.

= 1.27.3 = 17.11.2022

* Tweak: Various UX improvements.


= 1.27.2 = 16.11.2022

* Fix: Fixed saving status after conversion pixels fired.

= 1.27.1 = 16.11.2022

* Tweak: Removed a console.log statement.
* Tweak: Remove an error_log if an invalid phone number is passed to the e164 formatter.
* Fix: Fixed a check if conversions have already fired for logged-in users.

= 1.27.0 = 15.11.2022

* Tweak: Added current_user_can checks for a few functions.
* Tweak: Added a safeguard in case too many products are requested from the server.
* Tweak: Added filter to disable the Facebook pixel in the Facebook for WooCommerce plugin, if Facebook tracking is enabled in the Pixel Manager.
* Tweak: Switched back to the previous method to attach most of the events to DOM elements, as the old method looks like to be compatible with more themes.
* Tweak: Added filter to exclude domains from tracking.
* Tweak: Updated third party libraries.
* Tweak: Include PMW version in filename of settings export file.
* Tweak: Added logic for dealing with database downgrades in case a user downgrades to a lower version of the plugin.
* Fix: Fixed an edge case where get_pmw_tracked_payment_methods would throw an error if no orders were found.
* Fix: Fixed generation of precompressed admin .js files.

= 1.26.0 = 07.11.2022

* Tweak: Added tracking exclusion patterns for Facebook.
* Tweak: Added filter that enables shop managers to add more tracking exclusion patterns for Facebook.
* Tweak: Added exclusion for tracking over gtm-msr.appspot.com
* Tweak: Added new text for pro features demo, so that is clear no pro features are enabled with this.
* Tweak: Added a filter to decode HTML entities in the product names before output into the data layer.
* Tweak: Implemented transient caching for volatile products.
* Tweak: Use a different approach to attach event listeners to events like add-to-cart to increase compatibility with themes that prevent event propagation.
* Fix: Fixed a bug which would overwrite the data layer if products are added to it above the pmw main script.

= 1.25.1 = 25.10.2022

* Tweak: Switched getting tracking scripts from fetch back to jQuery.ajax() as fetch on some sites returned type errors.

= 1.25.0 = 11.10.2022

* New: Added support for the WP AutoTerms plugin
* New: Added support for the Usercentrics CMP
* New: Added support for CookiePro by OneTrust CMP
* Tweak: Better detect order_received_page on themes that don't use the proper WooCommerce conditional and return page_type cart instead.

= 1.24.0 = 06.10.2022

* New: The Pixel Manager has been refactored to be ready for the upcoming WooCommerce High Performance Orders Storage
* Tweak: Some UI improvements
* Tweak: Added GAds Conversion Adjustments feed URL to debug info
* Tweak: Refactor for HPOS

= 1.23.0 = 30.09.2022

* Tweak: Few styling fixes in the settings page.

= 1.22.1 = 26.09.2022

* Tweak: Adjusted the consent API settings parameters

= 1.22.0 = 26.09.2022

* New: Added API for developers to handle consent management.
* Tweak: Included the action scheduler library to avoid the buggy WooCommerce implementation of the action scheduler.
* Tweak: Implemented a dynamic rate limiter that prevents possible timeouts if too many orders are being analysed in the payment gateway accuracy report.
* Fix: Fixed the input tag name for the product data.
* Fix: Fix for the bug that caused multiple action scheduler entries.

= 1.21.0 = 12.09.2022

* Tweak: Added option to disable the PMW order list info output.
* Tweak: Updated default settings.
* Tweak: Added doc link for order list info.
* Tweak: Refactored running PMW tracking accuracy analysis report to better work with action scheduler to avoid running into its initialization bug
* Fix: Fixed options validation when importing the settings.
* Fix: Added safeguard to not fire the conversion pixels on the /order-pay/ page.

= 1.20.2 = 31.08.2022

* Fix: Fixed division by zero if WPML multi currency is enabled and a product has a price of zero or has no price set.

= 1.20.1 = 31.08.2022

* Tweak: Added statuses "cancelled" and "refunded" to the exclusions when conversions should not be fired.
* Tweak: Moved PMW column on orders page after WC actions
* Fix: Made PMW admin css to only target PMW elements

= 1.20.0 = 30.08.2022

* New: Column on order list, showing for which orders the browser conversion pixels have fired.
* New: View in order list that only shows orders where browser conversion pixels have not fired.
* Tweak: Removed the off-site payment gateway notification.
* Tweak: Added CartFlows custom thankyou pages compatibility.
* Tweak: Changed product price output to include tax by default, plus added a filter to turn it off.
* Tweak: Improved event listeners for init_checkout, payment_method_selected and place_order for higher compatibility with themes.
* Tweak: Automatically disable specific pixels in WooFunnels if they are enabled in the Pixel Manager in order to avoid duplicate tracking.
* Tweak: Automatically disable specific pixels in Woo Product Feed Pro and Elite if they are enabled in the Pixel Manager in order to avoid duplicate tracking.
* Tweak: Refactored some code to avoid unnecessary duplicate class initializations.
* Tweak: Disable gtag in Google Listing and Ads if Google Ads is active in PMW.
* Tweak: Analyse only orders in the payment gateway report that have been created by customers, not shop managers.
* Fix: Added brand output for variations.
* Fix: Fixed a bug that caused the conversion pixels to fire on purchase order payment pages (orders manually created by the shop manager and sent to the customer for payment).
* Fix: Fixed variations output for FB CAPI server side orders when variations output is enabled.

= 1.19.4 = 14.08.2022

* Tweak: Added safeguard if function as_enqueue_async_action isn't available.

= 1.19.3 = 14.08.2022

* Tweak: Performance improvements for the new Diagnostics page.

= 1.19.2 = 13.08.2022

* Tweak: Few formatting fixes for payment gateway accuracy report
* Tweak: Improved performance for purchase confirmation page detection and order retrieval.
* Fix: Removed possible division by zero on settings page

= 1.19.1 = 10.08.2022

* Tweak: Performance improvements for the new diagnostics report.

= 1.19.0 = 10.08.2022

* New: Introduced new diagnostics section in the settings page.
* Tweak: Refactored documentation URL link compilation.
* Tweak: Added sanitization for importing settings.
* Tweak: Improved sanitization for arrays.
* Tweak: Added sanitization for saving state when purchase pixels have fired.

= 1.18.1 = 04.08.2022

* Fix: Fixed the test which checks if Facebook CAPI is enabled.
* Fix: Fixed a division by zero error when no conversions have been tracked yet.

= 1.18.0 = 03.08.2022

* New: Switched all server requests to use the REST API with AJAX fallback. Much faster. Reduces server load by approx. 30%.
* Tweak: Added WP memory limit to the debug information.
* Tweak: Added safeguard for CLV output on low memory systems.
* Tweak: Abort reporting of a subscription order to GA through the MP if it was created manually, and thus no parent order exists.
* Tweak: Added compressed versions (gzip and brotli) of the minified JavaScript files in order to decrease server load.
* Tweak: Updated Freemius library to remove PHP 8.x compatibility warnings.
* Tweak: Updated libphonenumber library.
* Tweak: Improved payment gateway analysis.
* Fix: Fixed a bug which prevented the add-to-wishlist event to be triggered.
* Fix: Fixed the ask-for-rating.js script
* Fix: Fixed a bug which prevented to run an environment server check on servers that don't return the INPUT_SERVER array.

= 1.17.11 = 08.07.2022

* New: Added export and import functionality.
* Tweak: Removed the deprecated ttq.track("Browse") event.
* Tweak: Tweaked is_order_received_page() in order to allow PMW to work with custom WooFunnels purchase confirmation pages. It might also work with custom Elementor purchase confirmation pages.
* Tweak: Added an additional trigger if a visitor clicks on an Elementor checkout button.
* Fix: Added the content_type to the FB InitiateCheckout event.

= 1.17.10 = 21.06.2022

* Tweak: Bumped up Facebook API version to v14.0
* Tweak: Changed some jQuery event listeners to work around some themes / plugins that stop propagation of those events
* Tweak: Prevent console error on purchase confirmation page when Google Ads is disabled
* Tweak: Added product and value info to Facebook InitiateCheckout event
* Tweak: Refactored getting product details for add-to-cart event plus added condition for variable-subscription
* Tweak: Adjusted price output for wpmDataLayer products to two decimals

= 1.17.9 = 07.06.2022

* Fix: Fixed the Facebook browser pixel external ID for anonymous users
* Fix: Replaced str_contains() with a backward compatible version for the admin notification check

= 1.17.8 = 07.06.2022

* Tweak: Optimized ViewItem event on variable product pages when no product is preselected. The user has now the choice to fire a ViewItem event without product.
* Tweak: Optimized how, when and where admin notifications are displayed
* Fix: Don't process CLV calculation if no billing email is available

= 1.17.7 = 30.08.2022
* Tweak: Bumped up WordPress version to 6.0
* Tweak: Bumped up WoCommerce version to 6.5
* Fix: Fixed documentation links that contain anchors
* Fix: Fixed ask-for-rating scripts

= 1.17.6 = 28.05.2022


= 1.17.5 = 28.05.2022

* Tweak: Added safeguard to check if wpmDataLayer.products exists before trying to filter it

= 1.17.4 = 26.05.2022

* New: Added GA4 session ID handling over the Measurement Protocol. This is not documented by Google and is experimental.
* Tweak: Added tier info into debug info
* Tweak: Added more FB user information for logged in users
* Tweak: Prevent slow processing of large amount of variation data for variable products with > 64 variations
* Tweak: Increased server test timout to minimize false negatives
* Fix: Workaround if $_server['HTTP_HOST'] is not set
* Fix: Workaround if $_server['REMOTE_ADDR'] is not set

= 1.17.3 = 19.05.2022

* Tweak: Optimized clv query for clv output
* Tweak: Optimized retrieval of purchase labels for Google Ads
* Tweak: Optimized precheck for Google Ads purchase event
* Tweak: Optimized retrieval function for conversion identifiers with label for Google Ads
* Tweak: Optimized new_customer query to run faster
* Tweak: Updated libphonenumber
* Fix: Prevent that the Freemius menu tabs get displayed in other plugins
* Fix: Fixed the documentation links

= 1.17.2 = 13.05.2022

* Fix: Additional safeguard to check for array when checking for updates

= 1.17.1 =  12.05.2022

* Tweak: Adjusted db query for clv order total

= 1.17.0 =  12.05.2022

* New: Added customer lifetime value output for Google Ads
* Tweak: Renamed the plugin from Pixel Manager for WooCommerce to Pixel Manager for WooCommerce
* Tweak: Added user identifiers for FB for orders and logged in users
* Tweak: Temporary disabled off_site_payment_gateway check
* Fix: Now the cache is only flushed when the Pixel Manager itself is updated
* Fix: Fixed the new_customer evaluation for guest buyers
* Fix: Fixed the position of the 'scripts blocked' warning message in the back-end if admin scripts don't load
* Fix: Fixed the link of the 'admin scripts not loaded' message

= 1.16.14 =  02.05.2022

* Tweak: Set that admins and shop managers are being tracked by default, because the majority of new users expects this
* Fix: Fixed document links for Meta (Facebook) settings

= 1.16.13 =  30.04.2022

* Tweak: One more adjustment to make sure categories are being output for purchase events on shops that don't properly sync the categories down to the variants
* Fix: It can happen that no proper product object is created by WC and is passed to the Facebook microdata output. In that case WPM will now terminate the microdata output seamlessly.
* Fix: Fixed a syntax error in on of the back-end admin scripts

= 1.16.12 =  27.04.2022

* Tweak: Added a new event listener for the Complianz Cookie Banner
* Tweak: Added polyfill iconv to fix an edge case: https://wordpress.org/support/topic/update-to-pho-8-crash-wp/


= 1.16.11 =  25.04.2022

* New: Added function with which custom Facebook events can be triggered (including Facebook CAPI if it is enabled in the pro version)
* Tweak: Changed cursor for submenu links in the back-end
* Tweak: Added an additional check for a cookie name for Cookie Law Info
* Tweak: Some code style changes
* Tweak: Updated libphone library
* Tweak: Updated Freemius library

= 1.16.10 =  21.04.2022

* Fix: Borlabs cookie auto-detection for Microsoft Ads (Bing Ads) fixed

= 1.16.9 =  19.04.2022

* Tweak: Refactored some of the GA4 functions
* Tweak: Changed escaping method for outputting the titles and names into the wpmDataLayer in order to properly deal with characters like a dash, ampersand, etc.
* Tweak: Load categories for variations from parent product, because on some installs the variations don't inherit the parent product's categories
* Tweak: Removed more cruft from the code and improved the syntax here and there
* Tweak: Adjusted validation for the Google Merchant Center ID
* Tweak: Updated selectWoo.full.*.js
* Tweak: Tested up to WooCommerce 6.4

= 1.16.8 =  14.04.2022

Tweak: Added more page type outputs (for better product list performance analysis in GA)
Tweak: Refactored GA3 and GA4 product data collection
Fix: Fix for view item events on single variation pages

= 1.16.6 =  11.04.2022

* Tweak: Safeguard for JavaScript optimizers that ignore the jQuery dependency of WPM and load jQuery after WPM
* Tweak: Refactored the WPM JavaScript library (split code logically and into folders)

= 1.16.5 =  08.04.2022

* Tweak: More verbose logging of WPM load and pixel consent status into the dev console
* Tweak: Added new logic to retrieve categories for variations

= 1.16.4 =  07.04.2022

* Fix: Fixed a bug that prevented disabling user role tracking

= 1.16.3 =  07.04.2022

* Tweak: Remove the IPv6 to IPv4 mapping in case the the external user IP contains one
* Tweak: Improved local IP detection
* Tweak: Replaced deprecated FILTER_SANITIZE_STRING filter with FILTER_SANITIZE_FULL_SPECIAL_CHARS

= 1.16.2 =  05.04.2022

* Fix: Improved detection of users that are deactivated for tracking

= 1.16.1 =  05.04.2022

* Tweak: Removed data-cfasync=”false” to allow Cloudflare Rocket Loader to optimize scripts
* Tweak: Removed unused functions
* Tweak: Refactored some code and removed cruft
* Tweak: Improved logic of when to load cart items into the wpm dataLayer
* Tweak: For users, who have been disabled for tracking, prevent tracking entirely, not just for purchase events
* Tweak: Renamed Facebook to Meta
* Tweak: Add log entry when a purchase hit has been prevented because of a user role that has been opted out from tracking

= 1.16.0 =  01.04.2022

* Tweak: Removed a few deprecated and inactive settings
* Tweak: Updated Enhanced Conversions output as per updated specs by Google
* Tweak: Improved formatting for Facebook CAPI event parameters

= 1.15.5 =  30.03.2022

* Tweak: Removed an obsolete db cleanup function
* Tweak: On variable products, with no preselection send the valid variation ID if variations output is enabled
* Tweak: Added safeguard for debug info when URL redirect test fails to open the URL
* Tweak: Syntax improvements in wpm.addProductToCart()
* Tweak: Simplified logic when the wpm.getCartItems() function is called
* Tweak: Deprecated the wpm_track_mini_cart filter
* Tweak: Added cookie detection for Cookie Law CMP with French named cookies
* Tweak: Added one more safeguard in case the main wpmDataLayer init is being blocked by some third party plugin and products need being loaded into the wpmDataLayer further down below
* Tweak: Added console log output when pixels don't fire because of user consent choice
* Tweak: Added a payload logger for Facebook CAPI events
* Tweak: Initiate WC session later in the checkout funnel (if necessary at all) to improve caching conditions
* Tweak: Added URL parameter debugConsentMode for verbose debug output in the browser console in relation to user cookie consent

= 1.15.4 =  23.03.2022

* Fix: Product name transmission for Google Analytics Universal purchases

= 1.15.3 =  22.03.2022

* Tweak: Attach click handlers later during loading in order to better deal with some side cases
* Tweak: Fire viewItem event on variable product pages when no variation is selected yet, sending the ID of the parent product

= 1.15.2 =  21.03.2022

* Tweak: Silenced status icon on compatibility mode setting when disabled
* Tweak: Made Autoptimize, FlyingPress, Async JavaScript, Optimocha and WP Optimize JavaScript optimization disabler optional through compatibility mode
* Tweak: Made Litespeed Cache and SG Optimizer JavaScript optimization disabler mostly optional through compatibility mode. Some exclusion have been left permanent without which the tracking scripts would break.
* Fix: Added safeguard if old version of FlyingPress is being used and compatibility mode is enabled
* Fix: Added safeguard if old version of WP Rocket is being used and compatibility mode is enabled
* Fix: Added safeguard if Yoast SEO is being used and compatibility mode is enabled

= 1.15.1 =  18.03.2022

* Tweak: Made WP Rocket JavaScript optimization disabler optional through compatibility mode
* Tweak: Adjusted sourcemaps output to make them work with Firefox
* Fix: Proper check for user id when logged in

= 1.15.0 =  15.03.2022

* Experiment: Added experimental filter which defers the WPM script. The filter may be removed without prior notice. If you like the filter vote it up over here: https://roadmap.sweetcode.com/pixel-manager-for-woocommerce?card=622f4ba9d0b938002e8378c0
* Experiment: Added experimental filter which moves the WPM script to the footer. The filter may be removed without prior notice. If you like the filter vote it up over here: https://roadmap.sweetcode.com/pixel-manager-for-woocommerce?card=622f4bc2d0b938002e8378e2
* Tweak: Moved sourcemaps into correct folder
* Tweak: Moved parts of the back-end logic to the front-end in order to gain much more flexibility and better handle front-end script flow
* Tweak: Bumped up WC version

= 1.14.3 =  24.02.2022

* Tweak: Adjusted all documentation links to point to new domain and sub-folder
* Tweak: Improved handling with SG Optimizer

= 1.14.2 =  17.02.2022

* Tweak: Added items to Microsoft Ads purchase event
* Tweak: Added 'wpm-js' to optimization exclusion list
* Fix: Added a safeguard when checking for order purchase redirects in the UX
* Fix: Fixed a double json encoded string for the GA4 browser purchase event

= 1.14.1 =  12.02.2022

* Tweak: Avoid loading the entire admin UX in case an admin is not viewing the plugin admin page itself
* Fix: Fixed a side case where the plugin would output wrong html in case the pro version demo is enabled

= 1.14.0 =  11.02.2022

* Tweak: Replace sweetcode.co references with sweetcode.com
* Fix: Replaced function wpm.objectExists() because it did not always work properly
* Fix: Plenty of syntax adjustments in order to adhere to all WooCommerce and WordPress coding standards
* Fix: Fixed refunds for GA UA measurement protocol

= 1.13.1 =  28.01.2022

* Fix: fixed an error that would appear if no Google pixel is turned on and the wpm scripts are looking for Google values in the wpmDataLayer

= 1.13.0 =  28.01.2022

* Tweak: Tweaked settings for new cookie names in CMP Complianz >= version 6.0.0
* Tweak: Rebranding to sweetcode.co
* Tweak: Refactored plenty function names
* Tweak: Improved WP Rocket cache flush and recreation
* Tweak: Refactored documentation links into a class
* Tweak: Added safeguards in case some wpm functions have not been loaded properly
* Tweak: Included transpiling ES6 into the JS minification process
* Tweak: Combined all front-end and back-end scripts into single, optimized and compressed files
* Tweak: Removed functions from the newly combined files that are not necessary anymore, like Promise.allSettled

= 1.12.4 =  11.01.2022

* Tweak: Removed some unnecessary console.log outputs
* Tweak: Added a safeguard when flushing Flying Press Cache
* Tweak: Added support output for various CMPs into the admin pages
* Tweak: Added more safeguards in case on the purchase confirmation page the order can't be retrieved through query vars
* Tweak: A few improvements how to read out cookies for various CMPs
* Tweak: Improved gtag firing based on cookie consent
* Tweak: Improvements to read out of Cookie Script consent cookie
* Tweak: Improvements to read out of Cookiebot consent cookie

= 1.12.3 =  04.01.2022

Fix: Deliver proper updated wpm.jsx

= 1.12.2 =  03.01.2022

* Tweak: Added automatic improvements for users of the Optimocha Speed Booster Pack plugin
* Tweak: Added automatic improvements for users of the Async JavaScript plugin
* Tweak: Added automatic improvements for users of the Flying Press plugin
* Tweak: Added a few console warnings if wooptpm scripts are being deferred
* Fix: Fixed a bug that appeared when a shop was using the "Complianz | GDPR/CCPA Cookie Consent" consent management plugin

= 1.12.1 =  02.01.2022

* Tweak: Added back the cookie prevention filter and included a deprecation notice
* Fix: Fixed a bug that appeared when a shop was using the "CookieYes, GDPR Cookie Consent (Cookie Law Info)" consent management plugin

= 1.12.0 =  31.12.2021

* New: Added dynamic remarketing ID output for the WooCommerce Google Listings & Ads plugin
* Tweak: Bumped up minimum PHP version to 7.3
* Tweak: Much more cache friendly handling of Cookie Consent Platforms
* Tweak: Added support for several more Cookie Consent Platforms
* Tweak: Properly handle error if WooCommerce is not active

= 1.11.7 =  29.10.2021

* Tweak: Adjusted HotJar ID validation to accept 6 digit IDs
* Tweak: Switched body reference to jQuery(document) for better compatibility


= 1.11.6 =  17.09.2021

* Tweak: Improved some admin interface text

= 1.11.5 =  02.09.2021

* Tweak: Don't disable the deactivation button anymore when a user chooses "other" from the list of reasons
* Tweak: Added minified versions of all front-end scripts
* Tweak: Removed query to remove old refund keys from db

= 1.11.4 =  13.08.2021


= 1.11.3 =  13.08.2021

* Fix: Fixed an array type error that could happen in rare cases

= 1.11.2 =  13.08.2021

* New: Added shortcodes for Snapchat and TikTok
* New: Saving referrer in a wooptpm cookie
* Tweak: Moved Freemius to composer vendor directory
* Tweak: Improved handler to save cookies for FB session
* Tweak: Added fallback to get an order from the order received page in case a plugin messes with query vars


= 1.11.1 =  05.08.2021

* New: Automatic cache purge for Kinsta on plugin updates and saved settings
* Tweak: Added safeguard in order to prevent a fatal error when coming across a variation that doesn't properly link to a parent product
* Tweak: Added filters to suppress certain admin notifications
* Tweak: Changed global for notifications db name
* Tweak: Changed function declaration for varExists in order to work around a Google Closure bug
* Fix: Built in a safeguard for the debug info in case no order exists yet in the shop

= 1.11.0 =  26.07.2021

* Tweak: Added coupons to GA UA and GA4 purchase events
* Tweak: Avoid an issue when trying to read WP Rocket options when no options exist yet
* Tweak: Removed some unnecessary parameters from Google Ads purchase confirmation script
* Tweak: Output purchase value with max two decimals
* Fix: Fixed some events on cart page for variable products

= 1.10.11 =  19.07.2021

* New: Filter for order items
* New: Implemented warning for incompatible plugins
* Tweak: Refactored wooptpm.setCookie() so that it can create session cookies
* Fix: Output of product prices as float
* Fix: Added string to float conversion in wooptpm_get_order_item_price to make sure a float is returned

= 1.10.10 =  7.7.2021

* New: Automatically flush cache of caching plugins and platforms on plugin settings changes
* New: Report hosting platform in debug info
* Tweak: Added new compatibility exclusions for WP Rocket
* Tweak: Added option to filter GA4 parameters

= 1.10.9 =

* New: Filter for custom brand taxonomy
* New: Added payment gateway usage and successful redirect stats to free version
* Tweak: Refactored several elements in order to render correctly in RTL environments
* Tweak: If product belongs to multiple categories, output them in a comma separated list for GA
* Tweak: Automatically detect if mini cart is being used, and depending on this, enable or disable wooptpm_get_cart_items
* Tweak: Caching cart contents in sessionStorage in order to avoid as many API calls to backend as possible
* Fix: Fixed version check for db upgrade function in case someone has been using one of the earliest versions of the plugin

= 1.10.8 =

* Tweak: Fallback on PayPal check if option doesn't exist in the db
* Tweak: Removed manual function override from Freemius source
* Tweak: Added page type output on order received page
* Tweak: Switched data wooptpmProductId output on product pages to meta tag for main product
* Fix: Fixed ViewContent id_type variable for Facebook, GA UA and GA4
* Fix: Added dynamic remarketing purchase script

= 1.10.7 =

* New: Added hook_suffix into debug info
* Tweak: Adjusted the $hook_suffix check in admin.php because on some installs the $hook_suffix output is buggy

= 1.10.6 =

* New: Added conversion accuracy warning if the PayPal standard payment gateway is active
* New: Added support for WooCommerce composite products
* Tweak: Implemented better way to reference .js and .css files
* Tweak: Removed jquery.cookie.js dependency

= 1.10.5 =

* Tweak: parseFloat for value in Facebook and Pinterest just in case no proper input is received
* Tweak: Added an exclusion to fix an issue with Microsoft Ads, caused by Cloudflare Rocket Loader
* Tweak: Removed launch deal code
* Tweak: Cleaned up some cruft
* Tweak: Added new element selector for the intersection observer for product templates that pack the wooptpmProductId into a child > child hierarchy
* Fix: Added default quantity if quantity field has been removed on a product page
* Fix: Switched to treat order IDs as strings. Otherwise it would throw an error if a shop owner changes order IDs to also use letters.

= 1.10.4 =

* New: Added filter for Google cross domain filter setting
* Tweak: Added more safeguards in case WC processes a product ID which is not a product
* Fix: Product data output on some pages triggered a critical error

= 1.10.3 =

* Tweak: Switch to use different hook on product pages to output main product data

= 1.10.2 =

* Tweak: Renamed and deprecated several old filters


= 1.10.1 =

* New: Added a debug test to see if the server can reach the outside world with a wp_remote_get() request
* Tweak: Added better fallback for cookie retrievals if cookies can't be saved in the session
* Fix: Fire view_item event on products that are out of stock
* Fix: In case the redirect check returns an array of redirects, we return the last array member.

= 1.10.0 =

* Tweak: Only show the rating notice to admins of the shop.
* Tweak: Added more exclusions for WP Rocket
* Tweak: Added exclusions for WP Optimize
* Tweak: Additional exclusions for Autoptimize
* Tweak: Removed hit on for parent IDs on a variable product page

= 1.9.6 =

* Tweak: Added one more tweak for LiteSpeed Cache users
* Fix: Changed reading options from database to passing options from primary instance in order to avoid options caching issues on saving.

= 1.9.5 =

* Tweak: Added dependencies to enqueued scripts
* Tweak: Improved the parent selector for the mutation observer for lazy loaded products
* Tweak: Implemented better product selector for modification observer
* Tweak: Added new selector for related products


= 1.9.4 =

* Tweak: Finalized improvement for front-end error handling
* Tweak: Added a JS modification exclusion for Autoptimize in order to prevent our script to get modified and broken
* Fix: Fixed a selector for cart items on the cart page which caused on certain custom shop templates to trigger an error

= 1.9.3 =

* Tweak: Added one more layer of safeguards if wpm.jsx can't evaluate the current productId

= 1.9.2 =

* Tweak: Added one more safeguard if wpm.jsx can't evaluate the current productId

= 1.9.1 =

* Tweak: Added some some safeguards in order to stop processing in case no productId can be evaluated
* Tweak: Removed deprecated "disable gtag insertion" feature entirely


= 1.9.0 =

* Tweak: Additional caching exclusions for SG Optimizer
* Tweak: Changed the gtag code in order to make it better testable
* Tweak: Moved some scripts to the footer
* Tweak: Improved add_to_cart trigger
* Tweak: Refactored view_item_list event entirely to be unaffected by caching mechanisms
* Tweak: Added a new view_item_list trigger with some interesting options
* Fix: Fixed front-end triggers for Google and Facebook to only fire if the pixels are enabled
* Fix: Fixed an array check if an old WP Rocket version was installed and threw a notice about a missing array index
* Fix: Output correct price if WPML Multilingual with Multi-currency is running

= 1.8.28 =

* New: Filter to switch Google Analytics ID output to SKU
* New: Process discounted order item price for GA if Woo Discount Rules is active
* Tweak: Moved getCartItems to document.load event
* Tweak: Added Freemius purchase conversion
* Tweak: Avoid number output with too many decimals
* Tweak: More reliable method to get order from order received page
* Fix: Proper variable types for purchase confirmation variables
* Fix: Initialize wooptpmDataLayer.pixels early, so that all pixels can use it
* Fix: Replaced $order->get_id() with $order->get_order_number in order to fix a bug on a small subset of shops
* Fix: Get proper WP db prefix for refunds SQL query

= 1.8.27 =

* Tweak: LD check

= 1.8.26 =

* Tweak: Implemented permanent compatibility mode for SG Optimizer
* Tweak: Implemented permanent compatibility mode for LiteSpeed Cache
* Tweak: Refactored the Facebook pixel and events

= 1.8.25 =

* Tweak: Implemented permanent compatibility mode for WP Rocket
* Fix: Refactored a JavaScript regex that was not working in Safari

= 1.8.24 =

* Fix: Added a function that should be available in the free version conversion_pixels_already_fired_html

= 1.8.23 =

* Fix: Include output of variable products into the visible_products object
* Fix: Added a missing opening tag for shops that are still using the gtag deactivation option

= 1.8.22 =

* Tweak: Partially decoupled pixels from pixel manager
* Tweak: Refactored browser e-commerce events into pubsub
* Fix: Under some circumstances rating_done is not set in the wgact_ratings option. This fix adds this default option.
* Fix: Fixed the GA4 config command

= 1.8.21 =

* New: Added output of related up- and cross-sell product view_item_list list events for Google Ads dynamic remarketing
* New: Added &nodedupe URL parameter for testing the order confirmation page
* Tweak: Build in a fallback for misconfigured variable products that trigger an "Error: Call to a member function get_sku() on bool"

= 1.8.20 =

* Fix: Fixed the Google Analytics config filter

= 1.8.19 =

* New: Added Pro feature demo mode
* New: Added shortcodes for tracking leads and similar conversions
* New: Filter to adjust Google Analytics config parameters

= 1.8.18 =

* New: Google Analytics link attribution
* Tweak: Bumping up WP supported version to 5.7
* Tweak: Some code syntax cleanup

= 1.8.17 =

* Tweak: Remove some freemius options

= 1.8.16 =

* New: Output the variation ID for dynamic remarketing
* New: Maximum compatibility mode
* Tweak: Switched logic to activate conversion cart data automatically when merchant center ID is set
* Tweak: Made Google Analytics always receive the post ID as product ID because this is more robust
* Tweak: Removed some unnecessary text output in the settings
* Fix: Script blocker documentation link

= 1.8.15 =

* Fix: Determine correctly new customer for shopping cart data on new customers who paid immediately
* Tweak: Created new trait to calculate brand for product
* Tweak: Added a new array with additional product attributes (like brand which is calculated)
* Tweak: added ability to load traits in autoload.php
* Tweak: Bumped up WC version
* Tweak: Added an additional is_array() check in order to suppress a PHP 7.4 notice when checking the environment

= 1.8.14 =

* Tweak: Make the noptimize tag only appear if Autoptimize is active
* Tweak: Removed a duplicate filter
* Fix: Moved get_cart() query into is_cart() condition

= 1.8.13 =

* New: Filter to prevent conversion pixels to fire on purchase confirmation page
* Tweak: Replaced _e() with echo where necessary
* Tweak: Syntax cleanup

= 1.8.12 =

* Tweak: Calculate filtered order total for all pixels

= 1.8.11 =

* Fix: Removed a function call where the function was missing

= 1.8.10 =

* New: Added basic order deduper
* New: Google Shopping new_customer parameter
* New: Added switch to enable transaction deduping (default enabled)
* Tweak: Product identifier output now for all the same
* Tweak: Adjusted the HTML comment output
* Tweak: Added new cookie for Borlabs Cookie
* Tweak: Made some input elements clickable
* Tweak: Moved check for failed payments and admin and shop manager up to the pixel manager

= 1.8.9 =

* Tweak: readme.txt links
* Tweak: fallback to post ID in case the SKU is not set
* Tweak: Adjusted the HTML comments
* Tweak: Don't inject cart scripts on cart page if cart is empty

= 1.8.8 =

* Tweak: Bumped up version
* Tweak: Changed regex for GMC IDs to allow 7 digit IDs
* Tweak: Improved speed to hide script blocker warning
* Tweak: Adjusted documentation links

= 1.8.7 =

* Fix: Added new classes to SVN

= 1.8.6 =

* Tweak: Code cleanup
* Tweak: Adjusted doc links

= 1.8.5 =

* New: Hotjar pixel

= 1.8.4 =

* Tweak: Renamed subsection 'Order Logic' to 'Shop'
* Tweak: Refactored debug info
* Tweak: Added WP Rocket JavaScript concatenation to debug info

= 1.8.3 =


= 1.8.2 =

* New: Check for WP Rocket JavaScript concatenation
* New: Added filter which helps adding multiple additional conversion IDs and labels

= 1.8.1 =

* Fix: Version number
* Fix: FB default pixel id

= 1.8.0 =

* New: Google Analytics UA standard beta
* New: Google Analytics 4 beta
* New: Google Optimize beta
* New: Activation indicators
* Tweak: Put admin scripts into header for faster rendering
* Fix: Detect proper admin path in tabs.js

= 1.7.13 =

* New: Facebook pixel
* Tweak: Adjust db and bump up to version 3
* Tweak: Introduced Pixel_Manager and restructured Google Ads class

= 1.7.12 =

* Fix: Removed namespace for main class because it was conflicting with freemius in some cases

= 1.7.11 =

* Fix: Directory name fix
* New: Warning message if an ad- or script-blocker is active
* Tweak: Improved one of the db saving functions
* Tweak: Start using namespaces


= 1.7.10 =

* Fix: child theme detection

= 1.7.9 =

* Fix: Roll back to 1.7.7 since namespace don't work everywhere
* Fix: child theme detection

= 1.7.8 =

* New: Warning message if an ad- or script-blocker is active
* Tweak: Improved one of the db saving functions
* Tweak: Start using namespaces

= 1.7.7 =

* Fix: Don't show the rating popup if an admin uses a script blocker

= 1.7.6 =

* Fix: Improved check if dynamic remarketing settings already has been set before checking for it.
* Fix: Saving to the database threw sometimes warnings that have been fixed.
* Tweak: Styling changes

= 1.7.5 =

* New: Added checks for freemius servers
* New: Dynamic remarketing pixels
* New: Deactivation trigger for the WGDR plugin if dynamic remarketing is enabled
* Fix: Adjusted the cookie name for Cookie Law Info
* Fix: Improved detection if WooCommerce is active on multisite
* Fix: Fixed default setting for conversion_id
* Tweak: Added back rating testing code
* Tweak: Adjusted some links
* Tweak: Code style cleanups

= 1.7.4 =

* Fix: Fixed the ask for rating constant

= 1.7.3 =

* Fix: Don't open the rating page if user clicks on already done
* Tweak: Backward compatibility to PHP 7.0

= 1.7.2 =

* Fix: Fixed a printf syntax error that caused issues on some installations

= 1.7.1 =

* Tweak: Removed deletion of settings on uninstall in order to preserve the settings

= 1.7.0 =

* New: Added German translations
* Fix: Reversed some code in freemius to make it compatible with older versions of PHP (< PHP 7.2)
* Fix: Fixed the uninstall hook for it to work with freemius
* Tweak: Added some comments for translators
* Tweak: Removed old language packs
* Tweak: Add gtag config if gtag insertion is disabled
* Tweak: Rating request improved
* Tweak: Removed plugin ads
* Tweak: Added documentation
* Tweak: Updated db scheme
* Tweak: Merge new default options recursively
* Tweak: On save merge new and old options recursively, set missing checkbox options to zero, omit db_version

= 1.6.17 =

* Tweak: Reactivate freemius

= 1.6.16 =

* Fix: Deactivate freemius

= 1.6.15 =

* Tweak: Adjusted freemius main slug for plugin

= 1.6.14 =

* New: Implemented Freemius telemetry
* Tweak: Adjustments to the descriptions and links to new documentation
* Tweak: Only run if WooCommerce is active

= 1.6.13 =

* New: Implemented framework for sections and subsections
* Tweak: Some code cleanup
* Tweak: Made strings more translation friendly
* Tweak: Properly escaped all translatable strings
* Fix: Textdomain

= 1.6.12 =

* New: Plugin version output into debug info
* Fix: Conversion id validation
* Tweak: Moved JavaScript to proper enqueued scripts
* Tweak: Bumped up WC and WP versions

= 1.6.11 =

* New: Tabbed settings
* New: Debug information
* Tweak: Code style adjustments

= 1.6.10 =

* Fix: Disabled some error_log invocation since it can cause issues in some rare server configurations

= 1.6.9 =

* Fix: Re-enabled settings link on plugins page

= 1.6.8 =

* Fix: Changed how Borlabs Cookie activation works

= 1.6.7 =

* Fix: Implemented check for Borlabs minimum version

= 1.6.6 =

* New: Added option to disable the pixel with a filter add_filter( 'wgact_cookie_prevention', '__return_true' )
* New: Added Borlabs cookie management approval for marketing
* Tweak: Refactored the code into classes

= 1.6.5 =

* Tweak: Removed duplicate noptimize tag
* Tweak: Removed CDATA fix since it is not necessary anymore with the new conversion tag

= 1.6.4 =

* Fix: Fixed the calculation for the non-default order total value (which includes tax and shipping)

= 1.6.3 =

* Info: Tested up to WP 5.4

= 1.6.2 =

* Tweak: More reliable method to detect the visitor country added

= 1.6.1 =

* New: Add Cart Data feature
* New: Added a switch to disable the insertion of the gtag
* Tweak: Added more descriptions on the settings page
* Tweak: Code optimisations

= 1.5.5 =

* Tweak: Made the conversion ID and label validation code more robust

= 1.5.4 =

* Tweak: Updated function that inserts the settings link on the plugins overview page

= 1.5.3 =

* Info: Tested up to WP 5.2

= 1.5.2 =

* Fix: Correctly calculate the value when no filter is active

= 1.5.1 =

* Tweak: Re-enabled order value filter

= 1.4.17 =

* Info: Tested up to WP 5.1

= 1.4.16 =

* Info: Updated a few text strings

= 1.4.15 =

* Info: Changing name from AdWords to Google Ads

= 1.4.14 =

* Info: Tested up to WC 3.5.3

= 1.4.13 =

* Info: Tested up to WC 3.5.2

= 1.4.12 =

* Tweak: bumping up the WC version

= 1.4.11 =

* Tweak: remove some debug code
* fix: properly save the order_total_logic option

= 1.4.10 =

* Tweak: switched sanitization function to wp_strip_all_tags

= 1.4.9 =

* Tweak: Added input validation and sanitization
* Tweak: Added output escaping

= 1.4.8 =

* Tweak: Added discounts into order value calculation

= 1.4.7 =

* New: Switched over to the newest version of the AdWords conversion tracking pixel

= 1.4.6 =

* Tweak: Disabled minification through Autoptimize

= 1.4.5 =

* Tweak: Order ID back in apostrophes

= 1.4.4 =

* Tweak: Switched on JavaScript tracking with a fix for the CDATA bug http://core.trac.wordpress.org/ticket/3670
* Tweak: The correct function is being used to get the currency depending on the WooCommerce version
* Fix: Added missing noscript tag

= 1.4.3 =

* Tweak: Remove campaign URL parameter

= 1.4.2 =

* Fix: Backward compatibility for $order->get_currency()

= 1.4.1 =

* Tweak: Making the plugin PHP 5.4 backwards compatible
* Fix: Fixing double counting check logic

= 1.4 =

* New: Ask kindly for a rating of the plugin
* New: Add a radio button to use different styles of order total
* Tweak: Consolidate options into one array
* Tweak: Code cleanup

= 1.3.6 =

* New: WordPress 4.8 compatibility update
* Tweak: Minor text tweak.

= 1.3.5 =

* Fix: Fixed a syntax error that caused issues on some installations.

= 1.3.4 =

* Tweak: Added some text output to make debugging for users easier.

= 1.3.3 =

* Tweak: Refurbishment of the settings page

= 1.3.2 =

* New: Uninstall routine

= 1.3.1 =

* New: Keep old deduplication logic in the code as per recommendation by AdWords

= 1.3.0 =

* New: AdWords native order ID deduplication variable

= 1.2.2 =

* New: Filter for the conversion value

= 1.2.1 =

* Fix: wrong conversion value fix

= 1.2 =

* New: Filter for the conversion value

= 1.1 =

* Tweak: Code cleanup
* Tweak: To avoid over reporting only insert the retargeting code for visitors, not shop managers and admins

= 1.0.6 =

* Tweak: Switching single pixel function from transient to post meta

= 1.0.5 =

* Fix: Adding session handling to avoid duplications

= 1.0.4 =

* Fix: Skipping a tag version

= 1.0.3 =

* Fix: Implement different logic to exclude failed orders as the old one is too restrictive

= 1.0.2 =

* Fix: Exclude orders where the payment has failed

= 1.0.1 =

* New: Banner and icon
* Update: Name change

= 1.0 =

* Update: Release of version 1.0!

= 0.2.4 =

* Update: Minor update to the internationalization

= 0.2.3 =

* Update: Minor update to the internationalization

= 0.2.2 =

* New: The plugin is now translation ready

= 0.2.1 =

* Update: Improving plugin security
* Update: Moved the settings to the submenu of WooCommerce

= 0.2.0 =

* Update: Further improving cross browser compatibility

= 0.1.9 =

* Update: Implemented a much better workaround tor the CDATA issue
* Update: Implemented the new currency field
* Fix: Corrected the missing slash dot after the order value

= 0.1.8 =

* Fix: Corrected the plugin source to prevent an error during activation

= 0.1.7 =

* Significantly improved the database access to evaluate the order value.

= 0.1.6 =

* Added some PHP code to the tracking tag as recommended by Google.

= 0.1.5 =

* Added settings field to the plugin page.
* Visual improvements to the options page.

= 0.1.4 =

* Changed the woo_foot hook to wp_footer to avoid problems with some themes. This should be more compatible with most themes as long as they use the wp_footer hook.

= 0.1.3 =

* Changed conversion language to 'en'.

= 0.1.2 =

* Disabled the check if WooCommerce is running. The check doesn't work properly with multisite WP installations, though the plugin does work with the multisite feature turned on.
* Added more description in the code to explain why I've build a workaround to not place the tracking code into the thankyou template of WC.

= 0.1.1 =

* Some minor changes to the code

= 0.1 =

* This is the initial release of the plugin. There are no known bugs so far.
