<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    37060 => 'Tele 2',
    37061 => 'Telia',
    37062 => 'Telia',
    37063 => 'BITĖ',
    37064 => 'BITĖ',
    370645 => 'Tele 2',
    370646 => 'Tele 2',
    370647 => 'Tele 2',
    370648 => 'Tele 2',
    37065 => 'BITĖ',
    370660 => 'BITĖ',
    370661 => 'BITĖ',
    3706610 => 'Tele 2',
    37066186 => 'Lancelot Telecom',
    370662 => 'Telia',
    3706630 => 'Telia',
    37066313 => 'BITĖ',
    37066314 => 'BITĖ',
    37066315 => 'BITĖ',
    37066316 => 'BITĖ',
    37066317 => 'BITĖ',
    37066318 => 'BITĖ',
    37066319 => 'BITĖ',
    37066320 => 'BITĖ',
    37066321 => 'Lancelot Telecom',
    37066323 => 'BITĖ',
    37066324 => 'Lancelot Telecom',
    37066325 => 'Lancelot Telecom',
    37066326 => 'Lancelot Telecom',
    37066327 => 'Lancelot Telecom',
    37066328 => 'Lancelot Telecom',
    37066329 => 'Lancelot Telecom',
    3706650 => 'Telia',
    3706651 => 'Telia',
    37066522 => 'Telia',
    37066523 => 'Telia',
    37066524 => 'Telia',
    37066525 => 'Telia',
    37066526 => 'Telia',
    37066527 => 'Telia',
    37066528 => 'Telia',
    37066529 => 'Telia',
    3706653 => 'Telia',
    3706660 => 'BITĖ',
    3706661 => 'BITĖ',
    37066621 => 'Telia',
    37066622 => 'BITĖ',
    37066623 => 'BITĖ',
    37066624 => 'BITĖ',
    37066625 => 'BITĖ',
    37066626 => 'BITĖ',
    37066627 => 'BITĖ',
    37066628 => 'BITĖ',
    37066629 => 'BITĖ',
    3706663 => 'Telia',
    3706664 => 'Telia',
    3706665 => 'BITĖ',
    3706666 => 'Tele 2',
    3706667 => 'BITĖ',
    3706668 => 'BITĖ',
    3706669 => 'BITĖ',
    3706670 => 'BITĖ',
    37066711 => 'BITĖ',
    37066719 => 'BITĖ',
    37066728 => 'BITĖ',
    37066729 => 'BITĖ',
    3706676 => 'BITĖ',
    3706677 => 'BITĖ',
    3706678 => 'BITĖ',
    3706679 => 'BITĖ',
    3706680 => 'Tele 2',
    37066839 => 'Tele 2',
    37066840 => 'Tele 2',
    37066841 => 'Tele 2',
    37066842 => 'Tele 2',
    37066860 => 'Tele 2',
    37066861 => 'Tele 2',
    37066862 => 'Tele 2',
    37066863 => 'Tele 2',
    37066864 => 'Tele 2',
    37066865 => 'Tele 2',
    37066876 => 'BITĖ',
    37066877 => 'BITĖ',
    370669 => 'Telia',
    37067 => 'Tele 2',
    370680 => 'Telia',
    370681 => 'BITĖ',
    370682 => 'Telia',
    370683 => 'Tele 2',
    370684 => 'Tele 2',
    370685 => 'BITĖ',
    370686 => 'Telia',
    370687 => 'Telia',
    370688 => 'Telia',
    370689 => 'BITĖ',
    370690 => 'BITĖ',
    370691 => 'BITĖ',
    370692 => 'Telia',
    370693 => 'Telia',
    370694 => 'Telia',
    370695 => 'Telia',
    370696 => 'Telia',
    3706970 => 'Telia',
    3706971 => 'Telia',
    3706972 => 'Telia',
    3706973 => 'Telia',
    37069740 => 'Telia',
    37069741 => 'Telia',
    37069742 => 'BITĖ',
    37069743 => 'BITĖ',
    37069744 => 'Telia',
    37069747 => 'Telia',
    37069748 => 'Telia',
    37069749 => 'Telia',
    3706975 => 'Telia',
    3706976 => 'Lancelot Telecom',
    3706977 => 'Telia',
    3706979 => 'Telia',
    370698 => 'Telia',
    370699 => 'BITĖ',
];
