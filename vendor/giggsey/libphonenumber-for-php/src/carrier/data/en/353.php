<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    35383 => '3',
    35385 => 'Meteor',
    35386 => 'O2',
    35387 => 'Vodafone',
    35388 => 'eMobile',
    353890 => 'Tesco Mobile',
    3538900 => 'Eircom',
    353891 => 'Tesco Mobile',
    353892 => 'Liffey Telecom',
    3538928 => 'Tesco Mobile',
    3538929 => 'Tesco Mobile',
    353893 => 'Tesco Mobile',
    353894 => 'Liffey Telecom',
    353895 => '3',
    353896 => 'Tesco Mobile',
    3538960 => 'Virgin Media',
    3538961 => 'Virgin Media',
    3538962 => 'Virgin Media',
    353897 => 'Tesco Mobile',
    3538970 => 'Carphone Warehouse Ireland Mobile Limited',
    3538971 => 'Carphone Warehouse Ireland Mobile Limited',
    353898 => 'Tesco Mobile',
    3538990 => 'Tesco Mobile',
    3538991 => 'Tesco Mobile',
    3538992 => 'Tesco Mobile',
    3538993 => 'Tesco Mobile',
    3538994 => 'Lycamobile',
    3538995 => 'Lycamobile',
    3538996 => 'Lycamobile',
    3538997 => 'Lycamobile',
    3538998 => 'Lycamobile',
    3538999 => 'Tesco Mobile',
];
