<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    97430 => 'Vodafone',
    974310 => 'Vodafone',
    974311 => 'Vodafone',
    974312 => 'Vodafone',
    974313 => 'Vodafone',
    974314 => 'Vodafone',
    974315 => 'Vodafone',
    974316 => 'Vodafone',
    97433 => 'ooredoo',
    974399 => 'ooredoo',
    97450 => 'ooredoo',
    97451 => 'ooredoo',
    97452 => 'ooredoo',
    97455 => 'ooredoo',
    974599 => 'ooredoo',
    974600 => 'ooredoo',
    97466 => 'ooredoo',
    97470 => 'Vodafone',
    97471 => 'Vodafone',
    974720 => 'Vodafone',
    974721 => 'Vodafone',
    974722 => 'Vodafone',
    974723 => 'Vodafone',
    974724 => 'Vodafone',
    974725 => 'Vodafone',
    974726 => 'Vodafone',
    97474 => 'Vodafone',
    97477 => 'Vodafone',
];
