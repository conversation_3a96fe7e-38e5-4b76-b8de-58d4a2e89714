<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    9050 => 'Turk Telekom',
    90510210 => 'Pasifik Telekom',
    90510220 => 'Netgsm',
    90510221 => 'Netgsm',
    90510222 => 'Netgsm',
    90510223 => 'Netgsm',
    90510224 => 'Netgsm',
    90510227 => 'Netgsm',
    90510232 => 'Foniv Telecommunications Services',
    90510244 => 'Nida Telekomunikasyon',
    90510255 => 'TTM',
    90510266 => 'Medium Telecommunications Services',
    90510300 => 'Mobilisim Mobil Bilisim',
    90510310 => 'Assistant Telecom',
    90510333 => 'Roitel',
    90510343 => 'Isnet',
    90510400 => 'Duru Telekom',
    90510450 => 'Pelicell Telecommunications',
    90510480 => 'Alfa iletisim',
    90510499 => 'HATNET BILGI ILETISIM TEKNOLOJILERI',
    9051616 => 'Turkcell',
    9053 => 'Turkcell',
    9053383 => 'Kuzey Kibris Turkcell',
    9053384 => 'Kuzey Kibris Turkcell',
    9053385 => 'Kuzey Kibris Turkcell',
    9053386 => 'Kuzey Kibris Turkcell',
    9053387 => 'Kuzey Kibris Turkcell',
    9054 => 'Vodafone',
    9055 => 'Turk Telekom',
    905610 => 'Selam Mobile',
    905616 => 'Turkcell',
];
