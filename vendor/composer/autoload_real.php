<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInitab770dc3559fd87d5167f1d33f2baf09
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        require __DIR__ . '/platform_check.php';

        spl_autoload_register(array('ComposerAutoloaderInitab770dc3559fd87d5167f1d33f2baf09', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInitab770dc3559fd87d5167f1d33f2baf09', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInitab770dc3559fd87d5167f1d33f2baf09::getInitializer($loader));

        if (method_exists($loader, 'setApcuPrefix')) { $loader->setApcuPrefix('cc9f3a81ef843ac5e75b'); }
        $loader->register(true);

        $filesToLoad = \Composer\Autoload\ComposerStaticInitab770dc3559fd87d5167f1d33f2baf09::$files;
        $requireFile = \Closure::bind(static function ($fileIdentifier, $file) {
            if (empty($GLOBALS['__composer_autoload_files'][$fileIdentifier])) {
                $GLOBALS['__composer_autoload_files'][$fileIdentifier] = true;

                require $file;
            }
        }, null, null);
        foreach ($filesToLoad as $fileIdentifier => $file) {
            $requireFile($fileIdentifier, $file);
        }

        return $loader;
    }
}
