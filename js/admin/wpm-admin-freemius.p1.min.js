/*! Copyright 2025 SweetCode. All rights reserved. */(()=>{var t={43:()=>{!function(){try{new MutationObserver((function(t){t.forEach((function(t){if("class"===t.attributeName){jQuery(t.target).prop(t.attributeName).includes("disabled")&&jQuery(".fs-modal").find(".button-deactivate").removeClass("disabled")}}))})).observe(jQuery(".fs-modal").find(".button-deactivate")[0],{attributes:!0})}catch(t){console.error(t)}}()}},e={};!function r(a){var o=e[a];if(void 0!==o)return o.exports;var i=e[a]={exports:{}};return t[a](i,i.exports,r),i.exports}(43)})();
//# sourceMappingURL=wpm-admin-freemius.p1.min.js.map