<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    5946940 => 'SFR',
    59469408 => 'Digicel',
    59469409 => 'Digicel',
    59469412 => 'Digicel',
    59469413 => 'Digicel',
    59469414 => 'Digicel',
    59469415 => 'Digicel',
    59469416 => 'Digicel',
    59469417 => 'SFR',
    59469418 => 'SFR',
    59469419 => 'SFR',
    5946942 => 'Orange',
    5946943 => 'Orange',
    59469435 => 'Free Caraibe',
    59469436 => 'Free Caraibe',
    59469437 => 'Free Caraibe',
    5946944 => 'Orange',
    59469446 => 'SFR',
    59469447 => 'SFR',
    5946949 => 'Digicel',
    59470930 => 'Free Caraibe',
    59470932 => 'Digicel',
    59470933 => 'Orange',
];
