{"packages": [{"name": "freemius/wordpress-sdk", "version": "2.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Freemius/wordpress-sdk.git", "reference": "db6f35a2b3d318a53330409dbeab49156ee76dd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Freemius/wordpress-sdk/zipball/db6f35a2b3d318a53330409dbeab49156ee76dd8", "reference": "db6f35a2b3d318a53330409dbeab49156ee76dd8", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^1.0", "phpcompatibility/php-compatibility": "^9.3", "phpcompatibility/phpcompatibility-wp": "^2.1", "phpstan/extension-installer": "^1.3", "squizlabs/php_codesniffer": "^3.7", "szepeviktor/phpstan-wordpress": "^1.3", "wp-coding-standards/wpcs": "^2.3"}, "time": "2025-05-11T07:07:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["start.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-only"], "description": "Freemius WordPress SDK", "homepage": "https://freemius.com", "keywords": ["<PERSON><PERSON><PERSON>", "plugin", "sdk", "theme", "wordpress", "wordpress-plugin", "wordpress-theme"], "support": {"issues": "https://github.com/Freemius/wordpress-sdk/issues", "source": "https://github.com/Freemius/wordpress-sdk/tree/2.12.0"}, "install-path": "../freemius/wordpress-sdk"}, {"name": "giggsey/libphonenumber-for-php", "version": "8.13.55", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/giggsey/libphonenumber-for-php.git", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/libphonenumber-for-php/zipball/6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "reference": "6e28b3d53cf96d7f41c83d9b80b6021ecbd00537", "shasum": ""}, "require": {"giggsey/locale": "^2.0", "php": "^7.4|^8.0", "symfony/polyfill-mbstring": "^1.17"}, "replace": {"giggsey/libphonenumber-for-php-lite": "self.version"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.64", "pear/pear-core-minimal": "^1.10", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.7", "phing/phing": "^3.0", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^9.6", "symfony/console": "^v5.2", "symfony/var-exporter": "^5.2"}, "time": "2025-02-14T08:14:08+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "8.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"libphonenumber\\": "src/"}, "exclude-from-classmap": ["/src/data/", "/src/carrier/data/", "/src/geocoding/data/", "/src/timezone/data/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "PHP Port of Google's libphonenumber", "homepage": "https://github.com/giggsey/libphonenumber-for-php", "keywords": ["geocoding", "geolocation", "libphonenumber", "mobile", "phonenumber", "validation"], "support": {"issues": "https://github.com/giggsey/libphonenumber-for-php/issues", "source": "https://github.com/giggsey/libphonenumber-for-php"}, "install-path": "../giggsey/libphonenumber-for-php"}, {"name": "giggsey/locale", "version": "2.7.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/giggsey/Locale.git", "reference": "a5c65ea3c2630f27ccb78977990eefbee6dd8f97"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/giggsey/Locale/zipball/a5c65ea3c2630f27ccb78977990eefbee6dd8f97", "reference": "a5c65ea3c2630f27ccb78977990eefbee6dd8f97", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^3.64", "pear/pear-core-minimal": "^1.9", "pear/pear_exception": "^1.0", "pear/versioncontrol_git": "^0.5", "phing/phing": "^2.7", "php-coveralls/php-coveralls": "^2.0", "phpunit/phpunit": "^8.5|^9.5", "symfony/console": "^5.0|^6.0", "symfony/filesystem": "^5.0|^6.0", "symfony/finder": "^5.0|^6.0", "symfony/process": "^5.0|^6.0", "symfony/var-exporter": "^5.2|^6.0"}, "time": "2024-11-04T11:18:07+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Giggsey\\Locale\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://giggsey.com/"}], "description": "Locale functions required by libphonenumber-for-php", "support": {"issues": "https://github.com/giggsey/Locale/issues", "source": "https://github.com/giggsey/Locale/tree/2.7.0"}, "install-path": "../giggsey/locale"}, {"name": "sweetcode-com/wp-flush", "version": "dev-main", "version_normalized": "dev-main", "dist": {"type": "path", "url": "/Users/<USER>/dev/PhpStormProjects/wp-flush", "reference": "6770d6b0ca9ce1b0053ecf0e13be408ad0b2ff55"}, "type": "library", "installation-source": "dist", "license": ["GPL3"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Flush the cache of various cache systems from within WordPress plugins", "transport-options": {"symlink": true, "relative": false}, "install-path": "../sweetcode-com/wp-flush"}, {"name": "symfony/polyfill-iconv", "version": "v1.32.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "5f3b930437ae03ae5dff61269024d8ea1b3774aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/5f3b930437ae03ae5dff61269024d8ea1b3774aa", "reference": "5f3b930437ae03ae5dff61269024d8ea1b3774aa", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "time": "2024-09-17T14:58:18+00:00", "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "installation-source": "dist", "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-iconv"}, {"name": "symfony/polyfill-mbstring", "version": "v1.20.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "39d483bdf39be819deabf04ec872eb0b2410b531"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/39d483bdf39be819deabf04ec872eb0b2410b531", "reference": "39d483bdf39be819deabf04ec872eb0b2410b531", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2020-10-23T14:02:19+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.20.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "woocommerce/action-scheduler", "version": "3.7.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/woocommerce/action-scheduler.git", "reference": "8aa895a6edfeb92f40d6eddc9dfc35df65da38ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/woocommerce/action-scheduler/zipball/8aa895a6edfeb92f40d6eddc9dfc35df65da38ae", "reference": "8aa895a6edfeb92f40d6eddc9dfc35df65da38ae", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^7.5", "woocommerce/woocommerce-sniffs": "0.1.0", "wp-cli/wp-cli": "~2.5.0", "yoast/phpunit-polyfills": "^2.0"}, "time": "2024-03-20T10:16:56+00:00", "type": "wordpress-plugin", "extra": {"scripts-description": {"test": "Run unit tests", "phpcs": "Analyze code against the WordPress coding standards with PHP_CodeSniffer", "phpcbf": "Fix coding standards warnings/errors automatically with PHP Code Beautifier"}}, "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "description": "Action Scheduler for WordPress and WooCommerce", "homepage": "https://actionscheduler.org/", "support": {"issues": "https://github.com/woocommerce/action-scheduler/issues", "source": "https://github.com/woocommerce/action-scheduler/tree/3.7.3"}, "install-path": "../woocommerce/action-scheduler"}], "dev": false, "dev-package-names": []}