<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    97250 => 'Pelephone',
    97251 => 'Xphone',
    972510 => 'Wecom',
    97252 => 'Cellcom',
    97253 => 'Hot Mobile',
    97254 => 'Orange',
    972550 => 'Beezz',
    972552 => '019mobile',
    9725520 => 'BITIT',
    9725521 => 'BITIT',
    9725522 => 'Home Cellular',
    9725523 => 'Home Cellular',
    9725530 => '019mobile',
    9725531 => '019mobile',
    9725532 => 'Free Telecom',
    9725533 => 'Free Telecom',
    9725540 => '<PERSON><PERSON>',
    9725541 => 'Merka<PERSON>ya',
    9725543 => 'Maskyoo',
    9725544 => 'Cellran Cellular Communications',
    97255440 => 'Merkaziya',
    97255442 => 'Xphone',
    97255443 => 'Yossi',
    9725545 => 'Maskyoo',
    9725550 => 'Annatel',
    9725551 => 'Annatel',
    9725552 => 'Annatel',
    9725555 => 'Rami Levy',
    9725557 => 'Rami Levy',
    972556 => 'Rami Levy',
    9725570 => 'Cellact',
    9725571 => 'Cellact',
    9725572 => 'Cellact',
    9725577 => '019mobile',
    972558 => 'Pelephone',
    972559 => '019mobile',
    97256 => 'Ooredoo',
    97258 => 'Golan Telecom',
    97259 => 'Jawwal',
];
