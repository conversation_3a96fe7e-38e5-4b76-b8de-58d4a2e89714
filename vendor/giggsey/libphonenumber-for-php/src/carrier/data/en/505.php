<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    5055 => 'Claro',
    5056 => 'CooTel',
    5057 => 'Movistar',
    50581 => 'Movistar',
    50582 => 'Movistar',
    505820 => 'Claro',
    505821 => 'Claro',
    505822 => 'Claro',
    505823 => 'Claro',
    505832 => 'Movistar',
    505833 => 'Claro',
    505835 => 'Claro',
    505836 => 'Claro',
    505837 => 'Movistar',
    505838 => 'Movistar',
    505839 => 'Movistar',
    50584 => 'Claro',
    505845 => 'Movistar',
    505846 => 'Movistar',
    505847 => 'Movistar',
    505848 => 'Movistar',
    505850 => 'Claro',
    505851 => 'Claro',
    505852 => 'Claro',
    505853 => 'Claro',
    505854 => 'Claro',
    505855 => 'Movistar',
    505856 => 'Movistar',
    505857 => 'Movistar',
    505858 => 'Movistar',
    505859 => 'Movistar',
    50586 => 'Claro',
    505867 => 'Movistar',
    505868 => 'Movistar',
    505870 => 'Claro',
    505871 => 'Claro',
    505872 => 'Claro',
    505873 => 'Claro',
    505874 => 'Claro',
    505875 => 'Movistar',
    505876 => 'Movistar',
    505877 => 'Movistar',
    505878 => 'Movistar',
    505879 => 'Movistar',
    50588 => 'Movistar',
    505882 => 'Claro',
    505883 => 'Claro',
    505884 => 'Claro',
    505885 => 'Claro',
    505890 => 'Claro',
    505891 => 'Claro',
    505892 => 'Claro',
    505893 => 'Claro',
    505894 => 'Claro',
    505895 => 'Movistar',
    505896 => 'Movistar',
    505897 => 'Movistar',
    505898 => 'Movistar',
    505899 => 'Movistar',
];
