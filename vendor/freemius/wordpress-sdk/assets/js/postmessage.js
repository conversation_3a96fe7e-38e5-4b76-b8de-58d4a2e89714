!function(t,e){var s,n,o,i,r,a,c,p,u=this;u.FS=u.FS||{},u.FS.PostMessage=(n=new NoJQueryPostMessageMixin("postMessage","receiveMessage"),o={},i=decodeURIComponent(document.location.hash.replace(/^#/,"")),r=i.substring(0,i.indexOf("/","https://"===i.substring(0,8)?8:7)),a=""!==i,c=t(window),p=t("html"),{init:function(t,e){s=t,n.receiveMessage((function(t){var e=JSON.parse(t.data);if(o[e.type])for(var s=0;s<o[e.type].length;s++)o[e.type][s](e.data)}),s),FS.PostMessage.receiveOnce("forward",(function(t){t.url&&(t.url.startsWith("http://")||t.url.startsWith("https://"))&&(window.location=t.url)})),(e=e||[]).length>0&&c.on("scroll",(function(){for(var t=0;t<e.length;t++)FS.PostMessage.postScroll(e[t])}))},init_child:function(){a&&(this.init(r),t(window).bind("load",(function(){FS.PostMessage.postHeight(),FS.PostMessage.post("loaded")})))},hasParent:function(){return a},postHeight:function(e,s){e=e||0,s=s||"#wrap_section",this.post("height",{height:e+t(s).outerHeight(!0)})},postScroll:function(t){this.post("scroll",{top:c.scrollTop(),height:c.height()-parseFloat(p.css("paddingTop"))-parseFloat(p.css("marginTop"))},t)},post:function(t,e,s){console.debug("PostMessage.post",t),s?n.postMessage(JSON.stringify({type:t,data:e}),s.src,s.contentWindow):n.postMessage(JSON.stringify({type:t,data:e}),i,window.parent)},receive:function(t,s){console.debug("PostMessage.receive",t),e===o[t]&&(o[t]=[]),o[t].push(s)},receiveOnce:function(t,e){this.is_set(t)||this.receive(t,e)},is_set:function(t){return e!=o[t]},parent_url:function(){return i},parent_subdomain:function(){return r}})}(jQuery);