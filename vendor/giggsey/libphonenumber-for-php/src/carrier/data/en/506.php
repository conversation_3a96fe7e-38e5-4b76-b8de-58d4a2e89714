<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    5063 => 'Kolbi ICE',
    50650 => 'Kolbi ICE',
    50657 => 'Kolbi ICE',
    5066 => 'Movistar',
    5067000 => 'Claro',
    50670010 => 'Claro',
    50670011 => 'Claro',
    50670012 => 'Claro',
    50670013 => 'Claro',
    50670014 => 'Claro',
    5067002 => 'Claro',
    5067003 => 'Claro',
    5067004 => 'Claro',
    5067005 => 'Claro',
    5067006 => 'Claro',
    5067007 => 'Claro',
    5067008 => 'Claro',
    5067009 => 'Claro',
    506701 => 'Claro',
    506702 => 'Claro',
    506703 => 'Claro',
    506704 => 'Claro',
    506705 => 'Claro',
    506706 => 'Claro',
    506707 => 'Claro',
    506708 => 'Claro',
    506709 => 'Claro',
    50671 => 'Claro',
    50672 => 'Claro',
    5067300 => 'Claro',
    5067301 => 'Claro',
    50683 => 'Kolbi ICE',
    50684 => 'Kolbi ICE',
    50685 => 'Kolbi ICE',
    50686 => 'Kolbi ICE',
    50687 => 'Kolbi ICE',
    50688 => 'Kolbi ICE',
    50689 => 'Kolbi ICE',
];
