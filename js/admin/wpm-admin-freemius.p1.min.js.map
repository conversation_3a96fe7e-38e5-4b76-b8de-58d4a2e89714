{"version": 3, "file": "wpm-admin-freemius.p1.min.js", "mappings": "2EAAA,WACC,IAEgB,IAAIA,kBAAiB,SAAUC,GAC7CA,EAAUC,SAAQ,SAAUC,GAC3B,GAA+B,UAA3BA,EAASC,cAA2B,CAClBC,OAAOF,EAASG,QAAQC,KAAKJ,EAASC,eACxCI,SAAS,aAC3BH,OAAO,aAAaI,KAAK,sBAAsBC,YAAY,WAE7D,CACD,GACD,IAESC,QAAQN,OAAO,aAAaI,KAAK,sBAAsB,GAAI,CACnEG,YAAY,GAGd,CAAE,MAAOC,GACRC,QAAQD,MAAMA,EACf,CACA,CArBD,E,GCCIE,EAA2B,CAAC,GAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUI,EAAQA,EAAOD,QAASJ,GAG/CK,EAAOD,OACf,CCtBAG,CAAQ,G", "sources": ["webpack://Pixel-Manager-for-WooCommerce/./src/js-src/admin/freemius-keep-deactivate-button-enabled.js", "webpack://Pixel-Manager-for-WooCommerce/webpack/bootstrap", "webpack://Pixel-Manager-for-WooCommerce/./src/js-src/admin/main-freemius.js"], "sourcesContent": ["(function () {\n\ttry {\n\n\t\tlet observer = new MutationObserver(function (mutations) {\n\t\t\tmutations.forEach(function (mutation) {\n\t\t\t\tif (mutation.attributeName === \"class\") {\n\t\t\t\t\tlet attributeValue = jQuery(mutation.target).prop(mutation.attributeName)\n\t\t\t\t\tif (attributeValue.includes(\"disabled\")) {\n\t\t\t\t\t\tjQuery(\".fs-modal\").find(\".button-deactivate\").removeClass(\"disabled\")\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t})\n\n\t\tobserver.observe(jQuery(\".fs-modal\").find(\".button-deactivate\")[0], {\n\t\t\tattributes: true,\n\t\t})\n\n\t} catch (error) {\n\t\tconsole.error(error)\n\t}\n})()\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "require(\"./freemius-keep-deactivate-button-enabled\")\n"], "names": ["MutationObserver", "mutations", "for<PERSON>ach", "mutation", "attributeName", "j<PERSON><PERSON><PERSON>", "target", "prop", "includes", "find", "removeClass", "observe", "attributes", "error", "console", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "require"], "sourceRoot": ""}