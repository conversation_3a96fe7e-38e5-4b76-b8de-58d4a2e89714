<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    9890 => 'Irancell',
    9891 => 'IR-MCI',
    9892 => 'Rightel',
    9893 => 'Irancell',
    98931 => 'MTCE',
    98932 => 'Taliya',
    98934 => 'TeleKish',
    98990 => 'IR-MCI',
    98991 => 'IR-MCI',
    98994 => 'IR-MCI',
    98996 => 'IR-MCI',
    989981 => 'Shatel Mobile',
    989982 => 'Shatel Mobile',
    9899900 => 'LOTUSTEL',
    9899902 => 'IR-MCI',
    989991 => 'Irancell',
    989998 => 'Rightel',
    9899996 => 'Rightel',
    9899997 => 'Rightel',
    9899998 => 'Rightel',
    9899999 => 'Rightel',
];
