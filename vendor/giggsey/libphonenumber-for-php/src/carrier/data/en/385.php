<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    38590 => 'Tele2',
    38591 => 'A1 Telekom',
    38592 => 'A1 Telekom',
    38595 => 'Tele2',
    385970 => 'Hrvatski Telekom',
    3859750 => 'Lancelot Telecom',
    3859751 => 'Telefocus',
    3859754 => 'Lancelot Telecom',
    3859755 => 'BSG',
    3859757 => 'Mobile One',
    38597595 => 'YATECO',
    38597596 => 'Altavox',
    38597597 => 'INNOVAC',
    38597599 => 'Digicom',
    385976 => 'Hrvatski Telekom',
    385977 => 'Hrvatski Telekom',
    385979 => 'Hrvatski Telekom',
    38598 => 'Hrvatski Telekom',
    38599 => 'Hrvatski Telekom',
];
