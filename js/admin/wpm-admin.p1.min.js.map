{"version": 3, "file": "wpm-admin.p1.min.js", "mappings": "2EAAA,SAASA,IACRC,OAAO,0BAA0BC,MAClC,CAIAF,IAIAC,QAAO,WACND,GACD,G,WCZAC,QAAO,WAIN,GAAI,QAAUE,eAAgB,OAE9B,IAAIC,EAAc,GACdC,EAAc,CAAC,EA0EnB,GAvEAJ,OAAO,YAAYK,QAAQ,MAAMJ,OAGjCD,OAAO,YAAYM,MAAK,WACvBH,EAASI,KAAK,CACb,KAASP,OAAOQ,MAAMC,KAAK,eAC3B,MAAST,OAAOQ,MAAMC,KAAK,iBAE7B,IAGAT,OAAO,eAAeM,MAAK,WAE1BF,EAAYJ,OAAOQ,MAAMC,KAAK,gBAAkBL,EAAYJ,OAAOQ,MAAMC,KAAK,iBAAmB,GAEjGL,EAAYJ,OAAOQ,MAAMC,KAAK,gBAAgBF,KAAK,CAClD,MAASP,OAAOQ,MAAMC,KAAK,mBAC3B,KAAST,OAAOQ,MAAMC,KAAK,mBAE7B,IAGAN,EAASO,SACR,SAAUC,GACTX,OAAO,oBAAoBY,OAAO,kDAAyDD,EAAc,KAAI,KAAQA,EAAe,MAAI,OACzI,IAGDX,OAAO,oBAAoBa,MAAMC,sBAAsBV,IAGvDJ,OAAO,sBAAsBe,GAAG,SAAS,SAAUC,GAElDA,EAAEC,iBAGFjB,OAAOQ,MAAMU,SAAS,kBAAkBC,WAAWC,YAAY,kBAI/D,IAAIC,EAAcrB,OAAOQ,MAAMC,KAAK,gBACpCa,kBAAkBD,EAAalB,GAG3BkB,KAAejB,GAClBJ,OAAO,wBAA0BqB,EAAc,KAAKE,SAAS,UAAUC,QAAQ,QAEjF,IAGAxB,OAAO,kBAAkBe,GAAG,SAAS,SAAUC,GAE9CA,EAAEC,iBACFD,EAAES,kBAGFzB,OAAOQ,MACLU,SAAS,wBAAwBE,YAAY,0BAC7CD,WACAD,SAAS,0BAA0BE,YAAY,wBAEjDM,EAAoB1B,OAAOQ,MAAMmB,SAASlB,KAAK,gBAAiBT,OAAOQ,MAAMC,KAAK,mBACnF,IASImB,sBAAuB,CAE1B,IAAIC,EAAgBD,sBAEpB5B,OAAO,uBAAyB6B,EAAuB,QAAI,KAAKL,QAAQ,UAEpC,IAAhCK,EAA0B,YAC7B7B,OAAO,wBAA0B6B,EAAuB,QAAI,KAAKN,SAAS,yBAA2BM,EAA0B,WAAI,KAAKL,QAAQ,QAElJ,MACCxB,OAAO,uBAAyBG,EAAS,GAAS,KAAI,KAAKqB,QAAQ,QAErE,IAGAV,sBAAyBV,IAExB,IAAI0B,EAAkBC,OAAOC,KAAK5B,GAE9B6B,EAAO,GAcX,OAZAH,EAAgBpB,SAAQ,SAAUwB,GACjCD,GAAQ,kDAAuDC,EAAgB,KAEjE9B,EAAY8B,GAElBxB,SAAQ,SAAUyB,GACzBF,GAAQ,mGAA0GE,EAAa,KAAI,KAAQA,EAAc,MAAI,OAC9J,IAEAF,GAAQ,OACT,IAEOA,GAORL,oBAAsBA,KAErB,MAAMQ,EAAcC,OAAOC,SAASC,OAC9BC,EAAc,IAAIC,gBAAgBL,GAExC,QAAII,EAAUE,IAAI,YACV,CACN,QAAcF,EAAUE,IAAI,WAC5B,WAAcF,EAAUE,IAAI,gBAQ/BpB,kBAAoBA,CAACD,EAAalB,KAEjCH,OAAO,2BAA2B2C,UAAU,WAAWC,UAAU3C,OACjED,OAAO,oBAAoBC,OAC3BD,OAAO,sCAAwCqB,EAAc,KAAKwB,OAElE,IAAIC,EAAa3C,EAAS4C,WAAWC,GAAiBA,EAAmB,OAAM3B,IAE/ErB,OAAO,yBAA2BqB,EAAc,KAAKhB,QAAQ,SAAS4C,QAAQ,YAAYC,OAAOP,UAAU,eAAeC,UAAUC,OAGpIM,UAAUhD,EAAS2C,GAAkB,OAGtC,MAAMpB,EAAsBA,CAACL,EAAa+B,KAEzCpD,OAAO,2BAA2B2C,UAAU,WAAWC,UAAU3C,OACjED,OAAO,sBAAwBqB,EAAc,0BAA4B+B,EAAiB,KAAK/C,QAAQ,MAAMc,WAAWyB,UAAU3C,OAElID,OAAO,sBAAwBqB,EAAc,0BAA4B+B,EAAiB,KAAK/C,QAAQ,SAASwC,OAChH7C,OAAO,sBAAwBqB,EAAc,0BAA4B+B,EAAiB,KAAK/C,QAAQ,MAAMsC,UAAU3C,OAAO,sBAAwBqB,EAAc,2BAA2BhB,QAAQ,OAAOwC,OAG9MM,UAAU9B,EAAa+B,IAIxBD,UAAYA,CAAC9B,EAAa+B,EAAiB,MAE1C,MAAMhB,EAAcC,OAAOC,SAASC,OAC9BC,EAAc,IAAIC,gBAAgBL,GAExCI,EAAUa,OAAO,WACjBb,EAAUa,OAAO,cAEjB,IAAIC,EAAY,WAAajC,EAC7BiC,GAAaF,EAAiB,eAAiBA,EAAiB,GAEhEG,QAAQC,UAAU,GAAI,MAAQnC,EAAaoC,SAASnB,SAASoB,SAAW,aAAeJ,GAGvFtD,OAAO,mCAAqC2D,IAAIC,kBAAoB,aAAeN,EAAY,2BAGhGM,gBAAkBA,IACP,IAAIC,IAAI7D,OAAO,uBAAuB8D,KAAK,SAC1CJ,SAGZxD,aAAeA,KAEd,MAAMkC,EAAcC,OAAOC,SAASC,OAGpC,OAFoB,IAAIE,gBAAgBL,GAEvBM,IAAI,SAItB1C,OAAOyD,UAAU1C,GAAG,QAAS,uBAAuB,WAElC,CAChB,gBACA,cACA,WAIYgD,SAAS/D,OAAOQ,MAAMC,KAAK,iBAEvCT,OAAO,WAAWC,OAGlBD,OAAO,WAAW6C,MAEpB,IAIA7C,OAAOyD,UAAU1C,GAAG,QAAS,4BAA4B,WAGxDf,OAAOQ,MAAMH,QAAQ,yBAAyB2D,SAAS,gCAEvDhE,OAAOQ,MAAMH,QAAQ,wBAAwB4D,QAC9C,IAKAjE,OAAOyD,UAAU1C,GAAG,QAAS,0BAA2BC,IAEvDA,EAAEC,iBAEF,IAAII,EAAiBrB,OAAOgB,EAAEkD,eAAezD,KAAK,cAC9C2C,EAAiBpD,OAAOgB,EAAEkD,eAAezD,KAAK,iBAElDT,OAAO,uBAAuBqB,MAAgBG,QAAQ,SAEtDxB,OAAO,wBAAwBqB,MAAgBE,SAAS,yBAAyB6B,MAAmB5B,QAAQ,W,WC3O7GxB,QAAO,WAKNA,OAAO,sBAAsBe,GAAG,SAAS,KAExC,MAAMoD,EAAWnE,OAAO,wBAAwB,GAChDmE,EAASC,SACTD,EAASE,kBAAkB,EAAG,OAG9BC,IAAIC,oBAAoBvE,OAAO,wBAAwB2D,OAGvD,MAAMa,EAAiBxE,OAAO,uBAC9BwE,EAAeC,OAAO,KACtBC,YAAW,IAAMF,EAAeG,QAAQ,MAAM,QAG/C3E,OAAO,yBAAyBe,GAAG,SAAS,WAC3Cf,OAAO,WAAWwB,QAAQ,QAC3B,IAEIiC,SAASmB,eAAe,6BAC3BnB,SAASmB,eAAe,4BACtBC,iBAAiB,SAAUP,IAAIQ,kBAAkB,GAIhDrB,SAASmB,eAAe,2CAE3BnB,SAASmB,eAAe,0CACtBC,iBAAiB,SAAUP,IAAIS,2BAA2B,GAIzDtB,SAASmB,eAAe,2CAE3BnB,SAASmB,eAAe,0CACtBC,iBAAiB,SAAS,KAC1BP,IAAIU,8BAA8B,CAAC,MACjC,GAMLhF,OAAO,4BAA4Be,GAAG,SAAS,SAASC,GAGvD,GAFAA,EAAEC,iBAEEjB,OAAOQ,MAAMyE,KAAK,YACrB,OAGD,MAAMC,EAASlF,OAAOQ,MAAMC,KAAK,UAC3B0E,EAASnF,OAAOQ,MAChB4E,EAAeD,EAAOE,OAG5BF,EAAOF,KAAK,YAAY,GAAMI,KAAK,kBAGnCC,MAAMC,YAAYC,KAAO,uBAAwB,CAChDC,OAAQ,OACRC,QAAS,CACR,eAAgB,oCAChB,aAAcH,YAAYI,OAE3BC,KAAM,IAAInD,gBAAgB,CACzByC,OAAQA,IAETW,YAAa,gBAEbC,MAAKC,IACL,IAAKA,EAASC,GACb,MAAM,IAAIC,MAAM,uBAAuBF,EAASG,UAEjD,OAAOH,EAASI,UAEhBL,MAAKK,IAEL,MAAMC,EAAM/D,OAAOwB,IAAIwC,gBAAgBF,GACjCG,EAAI7C,SAAS8C,cAAc,KACjCD,EAAEE,KAAOJ,EACTE,EAAEG,SAAW,aAAc,IAAIC,MAAOC,cAAcC,MAAM,EAAE,IAAIC,QAAQ,KAAM,KAAO,OACrFpD,SAASmC,KAAKkB,YAAYR,GAC1BA,EAAES,QACF1E,OAAOwB,IAAImD,gBAAgBZ,GAC3B3C,SAASmC,KAAKqB,YAAYX,GAG1BnB,EAAOF,KAAK,YAAY,GAAOI,KAAKD,MAEpC8B,OAAMC,IACNC,QAAQD,MAAM,kBAAmBA,GACjChC,EAAOF,KAAK,YAAY,GAAOI,KAAKD,GACpCiC,MAAM,oDAER,GACD,IAyCC,SAAU/C,GAEVA,EAAIgD,mBAAqB,KACxB,IAAIjC,EAAO5B,SAASmB,eAAe,wBAAwB2C,MAGvDC,EAAY,UAChB,IACC,IAAIC,EAAWC,KAAKC,MAAMtC,GACtBoC,EAASD,YACZA,EAAYC,EAASD,UAEvB,CAAE,MAAOL,GACRC,QAAQQ,KAAK,6CAA8CT,EAC5D,CAEA9B,EAAuBA,EAAKwB,QAAQ,MAAO,QAC3C,IAAIV,EAAmB,IAAI0B,KAAK,CAACxC,GAAO,CAACyC,KAAM,eAC3CC,EAAmBtE,SAAS8C,cAAc,KAC9CwB,EAAOtB,SAAgB,0BAA4Be,EAAY,IAAMlD,EAAI0D,yBAAyBR,GAAa,QAC/GO,EAAOvB,KAAgBnE,OAAOwB,IAAIwC,gBAAgBF,GAClD4B,EAAOE,OAAgB,SACvBF,EAAOG,MAAMC,QAAU,OACvB1E,SAASmC,KAAKkB,YAAYiB,GAC1BA,EAAOhB,QACPtD,SAASmC,KAAKqB,YAAYc,IAK3BzD,EAAI0D,yBAA4BR,IAE/B,GAAkB,YAAdA,IAA4BA,GAAaY,MAAMZ,GAClD,OAAOlD,EAAI+D,4BAIZ,IAAIC,EAAO,IAAI5B,KAAiB,IAAZc,GAGpB,GAAIjC,aAAeA,YAAYgD,UAAmD,iBAAhChD,YAAYgD,SAASC,OAAqB,CAE3F,IAAIC,EAAmD,GAA9BlD,YAAYgD,SAASC,OAAc,GAAK,IAYjE,OAXAF,EAAyB,IAAI5B,KAAK4B,EAAKI,UAAYD,GAIrCH,EAAKK,iBAOL,KANC,KAAOL,EAAKM,cAAgB,IAAIhC,OAAO,GAM1B,KALb,IAAM0B,EAAKO,cAAcjC,OAAO,GAKP,KAJzB,IAAM0B,EAAKQ,eAAelC,OAAO,GAIM,KAHvC,IAAM0B,EAAKS,iBAAiBnC,OAAO,GAGoB,KAFvD,IAAM0B,EAAKU,iBAAiBpC,OAAO,EAGnD,CAQC,OANc0B,EAAKW,cAML,KALC,KAAOX,EAAKY,WAAa,IAAItC,OAAO,GAKvB,KAJb,IAAM0B,EAAKa,WAAWvC,OAAO,GAIJ,KAHzB,IAAM0B,EAAKc,YAAYxC,OAAO,GAGS,KAFvC,IAAM0B,EAAKe,cAAczC,OAAO,GAEuB,KADvD,IAAM0B,EAAKgB,cAAc1C,OAAO,IAOjDtC,EAAI+D,0BAA4B,KAC/B,IAAIC,EAAO,IAAI5B,KAGf,GAAInB,aAAeA,YAAYgD,UAAmD,iBAAhChD,YAAYgD,SAASC,OAAqB,CAE3F,IAAIC,EAAmD,GAA9BlD,YAAYgD,SAASC,OAAc,GAAK,IAYjE,OAXAF,EAAyB,IAAI5B,KAAK4B,EAAKI,UAAYD,GAIrCH,EAAKK,iBAOL,KANC,KAAOL,EAAKM,cAAgB,IAAIhC,OAAO,GAM1B,KALb,IAAM0B,EAAKO,cAAcjC,OAAO,GAKP,KAJzB,IAAM0B,EAAKQ,eAAelC,OAAO,GAIM,KAHvC,IAAM0B,EAAKS,iBAAiBnC,OAAO,GAGoB,KAFvD,IAAM0B,EAAKU,iBAAiBpC,OAAO,EAGnD,CAQC,OANc0B,EAAKW,cAML,KALC,KAAOX,EAAKY,WAAa,IAAItC,OAAO,GAKvB,KAJb,IAAM0B,EAAKa,WAAWvC,OAAO,GAIJ,KAHzB,IAAM0B,EAAKc,YAAYxC,OAAO,GAGS,KAFvC,IAAM0B,EAAKe,cAAczC,OAAO,GAEuB,KADvD,IAAM0B,EAAKgB,cAAc1C,OAAO,IAajDtC,EAAIQ,iBAAmB9D,IAEtB,IAAIuI,EAAOvI,EAAEiH,OAAOuB,MAAM,GAC1B,IAAKD,EAAM,OACX,IAAIE,EAAY,IAAIC,WACpBD,EAAOE,OAAS,SAAU3I,GAIzB,IACC0G,KAAKC,MAAM3G,EAAEiH,OAAO2B,OACrB,CAAE,MAAOzC,GAGR,OAFA1D,SAASmB,eAAe,gCAAgCsD,MAAMC,QAAc,aAC5E1E,SAASmB,eAAe,wCAAwCiF,UAAY,qBAE7E,CAEA,IAAIC,EAAWpC,KAAKC,MAAM3G,EAAEiH,OAAO2B,QAInCtF,EAAIyF,yBAAyBD,EAC9B,EACAL,EAAOO,WAAWT,IAGnBjF,EAAIyF,yBAA2BtC,IAE9BnC,MAAMC,YAAYC,KAAO,mBAAoB,CAC5CC,OAAa,OACbI,YAAa,cACbH,QAAa,CACZ,eAAgB,mBAChB,aAAgBH,YAAYI,OAE7BC,KAAa8B,KAAKuC,UAAUxC,KAE3B3B,MAAKC,GAAYA,EAASmE,SAC1BpE,MAAKqE,UACDC,EAAQC,SACXjD,QAAQkD,IAAIF,GAEZ3G,SAASmB,eAAe,kCAAkCsD,MAAMC,QAAU,cAEpE,IAAIoC,SAAQC,GAAW9F,WAAW8F,EAAS,OACjDnI,OAAOC,SAASmI,WAEhBrD,QAAQkD,IAAIF,GACZ3G,SAASmB,eAAe,gCAAgCsD,MAAMC,QAAU,YAGzEjB,OAAMC,IACNC,QAAQD,MAAMA,GACd1D,SAASmB,eAAe,gCAAgCsD,MAAMC,QAAU,YAI3E7D,EAAIoG,cAAiBlD,IAEpBJ,QAAQkD,IAAI,gCAAiC9C,GAE7ClC,MAAMC,YAAYC,KAAO,yBAA2BgC,EAAY,WAAY,CAC3E/B,OAAa,OACbI,YAAa,cACbH,QAAa,CACZ,eAAgB,mBAChB,aAAgBH,YAAYI,SAG5BG,MAAKC,GAAYA,EAASmE,SAC1BpE,MAAKqE,UAEDC,GAAS3J,MAAM2J,SAClBhD,QAAQkD,IAAI,8BAA+BF,EAAQ3J,KAAK2J,SAGrDA,EAAQC,QACXhI,OAAOC,SAASmI,SAEhBpD,MAAM+C,EAAQ3J,KAAK2J,YAIpBlD,OAAMC,IACNC,QAAQD,MAAMA,GACdE,MAAMF,EAAMiD,aAIf9F,EAAIS,0BAA4B/D,IAE/B,IAAIuI,EAAOvI,EAAEiH,OAAOuB,MAAM,GAC1B,IAAKD,EAAM,OACX,IAAIE,EAAY,IAAIC,WACpBD,EAAOE,OAAS,SAAU3I,GAIzB,IACC0G,KAAKC,MAAM3G,EAAEiH,OAAO2B,OACrB,CAAE,MAAOzC,GAGR,OAFA1D,SAASmB,eAAe,2CAA2CsD,MAAMC,QAAc,aACvF1E,SAASmB,eAAe,mDAAmDiF,UAAY,qBAExF,CAEA,IAAIC,EAAWpC,KAAKC,MAAM3G,EAAEiH,OAAO2B,QAInCtF,EAAIU,8BAA8B8E,EACnC,EACAL,EAAOO,WAAWT,IAGnBjF,EAAIqG,4BAA8B3J,IACjCsD,EAAIU,8BAA8B,CAAC,IAGpCV,EAAIU,8BAAgCa,IAEnCP,MAAMC,YAAYC,KAAO,kCAAmC,CAC3DC,OAAa,OACbI,YAAa,cACbH,QAAa,CACZ,eAAgB,mBAChB,aAAgBH,YAAYI,OAE7BC,KAAa8B,KAAKuC,UAAUpE,KAE3BC,MAAKC,GAAYA,EAASmE,SAC1BpE,MAAKqE,UACDS,EAAaP,SAChBjD,QAAQkD,IAAIM,GAEZnH,SAASmB,eAAe,6CAA6CsD,MAAMC,QAAU,QAGrF9F,OAAOC,SAASmI,WAEhBrD,QAAQkD,IAAIM,GACZnH,SAASmB,eAAe,2CAA2CsD,MAAMC,QAAc,QAEvF1E,SAASmB,eAAe,mDAAmDiF,UAAY,kBAAoBe,EAAanK,KAAK2J,YAG9HlD,OAAMC,IACNC,QAAQD,MAAMA,GACd1D,SAASmB,eAAe,2CAA2CsD,MAAMC,QAAU,YAItF7D,EAAIuG,YAAc,KACjB,GAAIC,UAAU1E,IACb,OAAO0E,SAAS1E,KAUlB9B,EAAIyG,iBAAmB,KAYtB,IAAIC,EAA4BvH,SAAS8C,cAAc,OACvDyE,EAAQC,GAAwB,mBAChCD,EAAQ9C,MAAMgD,SAAkB,QAChCF,EAAQ9C,MAAMiD,IAAkB,IAChCH,EAAQ9C,MAAMkD,KAAkB,IAChCJ,EAAQ9C,MAAMmD,MAAkB,OAChCL,EAAQ9C,MAAMoD,OAAkB,OAChCN,EAAQ9C,MAAMqD,OAAkB,OAChCP,EAAQ9C,MAAMsD,gBAAkB,kBAChCR,EAAQ9C,MAAMC,QAAkB,OAChC6C,EAAQ9C,MAAMuD,eAAkB,SAChCT,EAAQ9C,MAAMwD,WAAkB,SAMnBjI,SAASmB,eAAe,UAC9BkC,YAAYkE,GAOnB,IAAIW,EAAuClI,SAAS8C,cAAc,OAClEoF,EAAoBV,GAAuB,4BAC3CU,EAAoBzD,MAAMmD,MAAiB,MAC3CM,EAAoBzD,MAAMoD,OAAiB,MAE3CK,EAAoBzD,MAAMqD,OAAiB,OAC3CI,EAAoBzD,MAAMC,QAAiB,OAC3CwD,EAAoBzD,MAAMuD,eAAiB,SAC3CE,EAAoBzD,MAAMwD,WAAiB,SAE3CC,EAAoBzD,MAAM0D,WAAiB,QAG3CZ,EAAQlE,YAAY6E,GAMpB,IAAIE,EAAmBpI,SAAS8C,cAAc,UAE9CsF,EAAiBC,IAAexH,EAAIuG,cACpCgB,EAAiB3D,MAAMmD,MAAS,OAChCQ,EAAiB3D,MAAMoD,OAAS,OAChCO,EAAiB3D,MAAM6D,OAAS,OAGhCJ,EAAoB7E,YAAY+E,GAGhCb,EAAQnG,iBAAiB,SAAS,SAAUmH,GAE3C5E,QAAQkD,IAAI0B,EAAM/D,QACM,qBAApB+D,EAAM/D,OAAOgD,KAChB7D,QAAQkD,IAAI,kDACZU,EAAQ/G,SAEV,IAGAR,SAASoB,iBAAiB,WAAW,SAAUmH,GAC5B,WAAdA,EAAMC,KAETjB,EAAQ/G,QAEV,IAAG,IAQJK,EAAI4H,cAAgB,WACY,IAApB7J,OAAOyI,WAA4BzI,OAAOyI,UAAUqB,WAOhE7H,EAAI8H,iBAAmBJ,IAEtB,MAAMK,EAAwBjC,IAC7B,IAAIkC,EAAW7I,SAAS8I,uBAAuB,eAC/C,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASG,OAAQD,IACpCF,EAASE,GAAGtE,MAAMC,QAAU,OAE7B1E,SAASmB,eAAe,qBAAqBsD,MAAMC,QAAW,QAC9D1E,SAASmB,eAAe,0BAA0BiF,UAAYO,GAWzDsC,EAAoBA,KACzB,IAAIJ,EAAW7I,SAAS8I,uBAAuB,eAC/C,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASG,OAAQD,IACpCF,EAASE,GAAGtE,MAAMC,QAAU,OAG7B1E,SAASmB,eAAe,qBAAqBsD,MAAMC,QAAU,QAM9D7C,MAAMC,YAAYC,KAAO,cAAe,CACvCC,OAAa,OACbI,YAAa,cACbH,QAAa,CACZ,eAAgB,mBAChB,aAAgBH,YAAYI,OAE7BC,KAAa8B,KAAKuC,UAAU,CAC3B,OAAU+B,EAAM/D,OAAO0E,QAAQC,WAG/B9G,MAAKC,GAAYA,EAASmE,SAC1BpE,MAAKsE,IACDA,EAAQC,SACXjD,QAAQkD,IAAIF,GAGRA,EAAQ3J,KAAKyF,OAAO2G,eACvBH,IAtCgCI,MACnC,IAAIR,EAAW7I,SAAS8I,uBAAuB,mBAC/C,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASG,OAAQD,IACpCF,EAASE,GAAGtE,MAAMC,QAAU,OAE7B1E,SAASmB,eAAe,yCAAyCsD,MAAMC,QAAU,SAkC9E2E,GACArJ,SAASmB,eAAe,mDAAmDsD,MAAMC,QAAU,SAGxFiC,EAAQ3J,KAAKyF,OAAO6G,aAEvBtJ,SAASmB,eAAe,2BAA2BoI,UAAW,EAC9DN,IACAjJ,SAASmB,eAAe,kDAAkDsD,MAAMC,QAAU,SAKtFiC,EAAQ3J,KAAKyF,OAAO6G,YAAe3C,EAAQ3J,KAAKyF,OAAO2G,cAC3DR,EAAqBjC,EAAQ3J,KAAK2J,WAGnChD,QAAQD,MAAMiD,GACdiC,EAAqBjC,EAAQ3J,KAAK2J,aAGnClD,OAAMC,IACNC,QAAQD,MAAMA,GACdkF,EAAqBlF,OAIxB7C,EAAIC,oBAAuBc,IAC1B4H,UAAUC,UAAUC,UAAU9H,GAC5B6B,OAAMkG,IACNhG,QAAQD,MAAM,mBAAoBiG,MAIrC,CAtcA,CAscC/K,OAAOiC,IAAMjC,OAAOiC,KAAO,CAAC,EAAGtE,QAMjCyD,SAASoB,iBAAiB,oBAAoB,KAG7C,MAAMwI,EAAiB5J,SAASmB,eAAe,wBAG/C,IAAKyI,EACJ,OAKD,GAAIA,EAAeC,cAAc,iBAChC,OAGD,MAAMC,EAAWlL,OAAOmL,WAAa,EAyBrC/J,SAASoB,iBAAiB,aAAamH,IACtC,MAAMyB,EAxB+BC,EAACC,EAAeJ,KAGrD,MAAMK,EAAkBP,EAAeQ,wBAGvC,OAFwBC,KAAKC,KAAKD,KAAKE,IAAIL,EAAcM,QAAUL,EAAgBxC,KAAM,GAAK0C,KAAKE,IAAIL,EAAcO,QAAUN,EAAgBzC,IAAK,IAElIoC,GAkBQG,CAA8B1B,EAAOuB,GAfvCY,KAEpBA,EAAa,IAChBA,EAAa,GAGd,MAEMC,EAAmB,IAFA,GACmBD,EAG5Cd,EAAegB,UAAUC,IAAI,0BAC7BjB,EAAenF,MAAMqG,QAAUH,EAAe,KAK9CI,CAAgBf,SAEf,GAWHhK,SAASoB,iBAAiB,oBAAoB,KAET,QAAhC4J,aAAaC,QAAQC,SAErBrK,IAAI4H,gBACPzI,SAASmB,eAAe,eAAesD,MAAMC,QAAU,QAEvDf,QAAQkD,IAAI,mDAGX,GAKHtK,QAAO,KACFyD,SAAS6J,cAAc,6BAC1B7J,SAAS6J,cAAc,4BAA4BzI,iBAAiB,SAASmH,IAC5EA,EAAM/K,iBACNqD,IAAI8H,iBAAiBJ,MAInBvI,SAAS6J,cAAc,8BAC1B7J,SAAS6J,cAAc,6BAA6BzI,iBAAiB,SAASmH,IAC7EA,EAAM/K,iBACNqD,IAAI8H,iBAAiBJ,MAKvBhM,QAAO,KACNA,OAAOyD,UAAU1C,GAAG,QAAS,8BAA8B,SAAUiL,GACpEA,EAAM/K,iBACN,MAAMuG,EAAYxH,OAAOQ,MAAMC,KAAK,aAChC+G,GACHlD,IAAIoG,cAAclD,EAEpB,UASFxH,QAAO,KAEDyD,SAAS6J,cAAc,+BAI5B7J,SAAS6J,cAAc,8BAA8BzI,iBAAiB,SAASmH,IAC9EA,EAAM/K,iBAEN,MAAM2N,EAAclH,KAAKC,MAAMqE,EAAM/D,OAAO0E,QAAQkC,OAEpD,IAAIC,EAAa,GAEjB,IAAK,IAAItC,EAAI,EAAGuC,EAAMH,EAAYnC,OAAQD,EAAIuC,EAAKvC,IAClDsC,GAAcF,EAAYpC,GAAK,KAGhClI,IAAIC,oBAAoBuK,GAGxB,IAAI3J,EAAiB1B,SAASmB,eAAe,6BAC7CO,EAAO+C,MAAMmD,MAAQ2D,iBAAiB7J,GAAQkG,MAC9C,MAAMjG,EAAeD,EAAO0E,UAC5B1E,EAAO0E,UAAcmC,EAAM/D,OAAO0E,QAAQsC,WAC1CvK,YAAW,WACVS,EAAO0E,UAAYzE,CACpB,GAAG,WAKLpF,QAAO,KACNA,OAAOyD,UAAU1C,GAAG,UAAW,2BAA2B,SAAUiL,GACnE,GAAkB,UAAdA,EAAMC,MAAoBD,EAAMkD,WAAalD,EAAMmD,UAAYnD,EAAMoD,OAAQ,CAEhF,MAAMC,EAAUrP,OAAOgM,EAAM/D,QAC7B,GAAIoH,EAAQC,GAAG,UAAYD,EAAQhP,QAAQ,QAAQoM,OAAS,EAAG,CAC9D,MAAM8C,EAAOF,EAAQhP,QAAQ,QAAQ,GAC/BmP,EAAexP,OAAOuP,GAAME,KAAK,WACnCD,EAAa/C,OAAS,IACzBT,EAAM/K,iBACNuO,EAAazI,QAEf,CACD,CACD,M,GC5uBG2I,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUI,EAAQA,EAAOD,QAASJ,GAG/CK,EAAOD,OACf,CChBAG,EAAQ,KACRA,EAAQ,KACRA,EAAQ,I", "sources": ["webpack://Pixel-Manager-for-WooCommerce/./src/js-src/admin/script-blocker-warning.js", "webpack://Pixel-Manager-for-WooCommerce/./src/js-src/admin/tabs.js", "webpack://Pixel-Manager-for-WooCommerce/./src/js-src/admin/helpers.js", "webpack://Pixel-Manager-for-WooCommerce/webpack/bootstrap", "webpack://Pixel-Manager-for-WooCommerce/./src/js-src/admin/main.js"], "sourcesContent": ["function wpm_hide_script_blocker_warning() {\n\tjQuery(\"#script-blocker-notice\").hide()\n}\n\n// try to hide as soon as this script is loaded\n// might be too early in some cases, as the HTML is not rendered yet\nwpm_hide_script_blocker_warning()\n\n// if all other earlier attempts to hide did fail\n// run the function after entire DOM has been loaded\njQuery(function () {\n\twpm_hide_script_blocker_warning()\n})\n", "jQuery(function () {\n\n\t// Don't run if we are not one of the\n\t// WPM main tabs\n\tif (\"wpm\" !== wpmGetPageId()) return\n\n\tlet sections    = []\n\tlet subsections = {}\n\n\t// Hide unnecessary elements\n\tjQuery(\".section\").closest(\"tr\").hide()\n\n\t// Collect information on sections\n\tjQuery(\".section\").each(function () {\n\t\tsections.push({\n\t\t\t\"slug\" : jQuery(this).data(\"sectionSlug\"),\n\t\t\t\"title\": jQuery(this).data(\"sectionTitle\"),\n\t\t})\n\t})\n\n\t// Collect information on subsections\n\tjQuery(\".subsection\").each(function () {\n\n\t\tsubsections[jQuery(this).data(\"sectionSlug\")] = subsections[jQuery(this).data(\"sectionSlug\")] || []\n\n\t\tsubsections[jQuery(this).data(\"sectionSlug\")].push({\n\t\t\t\"title\": jQuery(this).data(\"subsectionTitle\"),\n\t\t\t\"slug\" : jQuery(this).data(\"subsectionSlug\"),\n\t\t})\n\t})\n\n\t// Create tabs for sections\n\tsections.forEach(\n\t\tfunction (section) {\n\t\t\tjQuery(\".nav-tab-wrapper\").append(\"<a href=\\\"#\\\" class=\\\"nav-tab\\\" data-section-slug=\\\"\" + section[\"slug\"] + \"\\\">\" + section[\"title\"] + \"</a>\")\n\t\t})\n\n\t// Create tabs for each subsections\n\tjQuery(\".nav-tab-wrapper\").after(wpmCreateSubtabUlHtml(subsections))\n\n\t// Create on-click events on section tabs that toggle the views\n\tjQuery(\".nav-tab-wrapper a\").on(\"click\", function (e) {\n\n\t\te.preventDefault()\n\n\t\t// show clicked tab as active\n\t\tjQuery(this).addClass(\"nav-tab-active\").siblings().removeClass(\"nav-tab-active\")\n\n\t\t// toggle the sections visible / invisible based on clicked tab\n\n\t\tlet sectionSlug = jQuery(this).data(\"section-slug\")\n\t\twpmToggleSections(sectionSlug, sections)\n\n\t\t// if subsection exists, click on first subsection\n\t\tif (sectionSlug in subsections) {\n\t\t\tjQuery(\"ul[data-section-slug=\" + sectionSlug + \"]\").children(\":first\").trigger(\"click\")\n\t\t}\n\t})\n\n\t// Create on-click events on subsection tabs that toggle the views\n\tjQuery(\".pmw-subnav-li\").on(\"click\", function (e) {\n\n\t\te.preventDefault()\n\t\te.stopPropagation()\n\n\t\t// jQuery(this).hide();\n\t\tjQuery(this)\n\t\t\t.addClass(\"pmw-subnav-li-active\").removeClass(\"pmw-subnav-li-inactive\")\n\t\t\t.siblings()\n\t\t\t.addClass(\"pmw-subnav-li-inactive\").removeClass(\"pmw-subnav-li-active\")\n\n\t\twpmToggleSubsection(jQuery(this).parent().data(\"section-slug\"), jQuery(this).data(\"subsection-slug\"))\n\t})\n\n\t/**\n\t * If someone accesses a plugin tab by deep link, open the right tab\n\t * or fallback to default (first tab)\n\t *\n\t * If deeplink is being opened,\n\t * open the according section and subsection\n\t */\n\tif (wpmGetSectionParams()) {\n\n\t\tlet sectionParams = wpmGetSectionParams()\n\n\t\tjQuery(\"a[data-section-slug=\" + sectionParams[\"section\"] + \"]\").trigger(\"click\")\n\n\t\tif (sectionParams[\"subsection\"] !== false) {\n\t\t\tjQuery(\"ul[data-section-slug=\" + sectionParams[\"section\"] + \"]\").children(\"[data-subsection-slug=\" + sectionParams[\"subsection\"] + \"]\").trigger(\"click\")\n\t\t}\n\t} else {\n\t\tjQuery(\"a[data-section-slug=\" + sections[0][\"slug\"] + \"]\").trigger(\"click\")\n\t}\n})\n\n// Creates the html with all subsection elements\nwpmCreateSubtabUlHtml = (subsections) => {\n\n\tlet subsectionsKeys = Object.keys(subsections)\n\n\tlet html = \"\"\n\n\tsubsectionsKeys.forEach(function (subsectionKey) {\n\t\thtml += \"<ul class=\\\"pmw-subnav-tabs\\\" data-section-slug=\\\"\" + subsectionKey + \"\\\">\"\n\n\t\tlet subtabs = subsections[subsectionKey]\n\n\t\tsubtabs.forEach(function (subtab) {\n\t\t\thtml += \"<li class=\\\"pmw-subnav-li pmw-subnav-li-inactive\\\" style=\\\"cursor: pointer;\\\" data-subsection-slug=\\\"\" + subtab[\"slug\"] + \"\\\">\" + subtab[\"title\"] + \"</li>\"\n\t\t})\n\n\t\thtml += \"</ul>\"\n\t})\n\n\treturn html\n}\n\n/**\n * If section (and subsection) URL parameters are set,\n * return them, otherwise return false\n */\nwpmGetSectionParams = () => {\n\n\tconst queryString = window.location.search\n\tconst urlParams   = new URLSearchParams(queryString)\n\n\tif (urlParams.get(\"section\")) {\n\t\treturn {\n\t\t\t\"section\"   : urlParams.get(\"section\"),\n\t\t\t\"subsection\": urlParams.get(\"subsection\"),\n\t\t}\n\t} else {\n\t\treturn false\n\t}\n}\n\n// Toggles the sections\nwpmToggleSections = (sectionSlug, sections) => {\n\n\tjQuery(\"#wpm_settings_form > h2\").nextUntil(\".submit\").andSelf().hide()\n\tjQuery(\".pmw-subnav-tabs\").hide()\n\tjQuery(\".pmw-subnav-tabs[data-section-slug=\" + sectionSlug + \"]\").show()\n\n\tlet sectionPos = sections.findIndex((arrayElement) => arrayElement[\"slug\"] === sectionSlug)\n\n\tjQuery(\"div[data-section-slug=\" + sectionSlug + \"]\").closest(\"table\").prevAll(\"h2:first\").next().nextUntil(\"h2, .submit\").andSelf().show()\n\n\t// set the URL with the active tab parameter\n\twpmSetUrl(sections[sectionPos][\"slug\"])\n}\n\nconst wpmToggleSubsection = (sectionSlug, subsectionSlug) => {\n\n\tjQuery(\"#wpm_settings_form > h2\").nextUntil(\".submit\").andSelf().hide()\n\tjQuery(\"[data-section-slug=\" + sectionSlug + \"][data-subsection-slug=\" + subsectionSlug + \"]\").closest(\"tr\").siblings().andSelf().hide()\n\n\tjQuery(\"[data-section-slug=\" + sectionSlug + \"][data-subsection-slug=\" + subsectionSlug + \"]\").closest(\"table\").show()\n\tjQuery(\"[data-section-slug=\" + sectionSlug + \"][data-subsection-slug=\" + subsectionSlug + \"]\").closest(\"tr\").nextUntil(jQuery(\"[data-section-slug=\" + sectionSlug + \"][data-subsection-slug]\").closest(\"tr\")).show()\n\n\t// Set the URL with the active tab parameter\n\twpmSetUrl(sectionSlug, subsectionSlug)\n}\n\n// Sets the new URL parameters\nwpmSetUrl = (sectionSlug, subsectionSlug = \"\") => {\n\n\tconst queryString = window.location.search\n\tconst urlParams   = new URLSearchParams(queryString)\n\n\turlParams.delete(\"section\")\n\turlParams.delete(\"subsection\")\n\n\tlet newParams = \"section=\" + sectionSlug\n\tnewParams += subsectionSlug ? \"&subsection=\" + subsectionSlug : \"\"\n\n\thistory.pushState(\"\", \"wpm\" + sectionSlug, document.location.pathname + \"?page=wpm&\" + newParams)\n\n\t// Make WP remember which was the selected tab on a save and return to the same tab after saving\n\tjQuery(\"input[name =\\\"_wp_http_referer\\\"]\").val(wpmGetAdminPath() + \"?page=wpm&\" + newParams + \"&settings-updated=true\")\n}\n\nwpmGetAdminPath = () => {\n\tlet url = new URL(jQuery(\"#wp-admin-canonical\").attr(\"href\"))\n\treturn url.pathname\n}\n\nwpmGetPageId = () => {\n\n\tconst queryString = window.location.search\n\tconst urlParams   = new URLSearchParams(queryString)\n\n\treturn urlParams.get(\"page\")\n}\n\n// On click of an element that contains a data-section-slug with any value show an alert box with the text hello\njQuery(document).on(\"click\", \"[data-section-slug]\", function () {\n\n\tconst infoTabs = [\n\t\t\"opportunities\",\n\t\t\"diagnostics\",\n\t\t\"support\",\n\t]\n\n\t// If the data-section-slug value is one of infoTabs, hide the save button, otherwise show it\n\tif (infoTabs.includes(jQuery(this).data(\"section-slug\"))) {\n\t\t// Hide the element with class submit\n\t\tjQuery(\".submit\").hide()\n\t} else {\n\t\t// Show the element with class submit\n\t\tjQuery(\".submit\").show()\n\t}\n})\n\n\n// On click of an element that contains the classes pmw, opportunity and dismiss show an alert box with the text hello\njQuery(document).on(\"click\", \".pmw.opportunity-dismiss\", function () {\n\n\t// Move the parent element that contains the class pmw and opportunity and card and move it to the div with id pmw-dismissed-opportunities\n\tjQuery(this).closest(\".pmw.opportunity-card\").appendTo(\"#pmw-dismissed-opportunities\")\n\t// Remove the element with the class opportunity-dismiss\n\tjQuery(this).closest(\".opportunity-dismiss\").remove()\n})\n\n// if a link with the class advanced-section-link is clicked,\n// get the data-AS-section and the data-AS-subsection values of the clicked element\n// and trigger a click on the element with the data-ssection-slug value and a click on the element with the data-subsection-slug value\njQuery(document).on(\"click\", \".advanced-section-link\", (e) => {\n\n\te.preventDefault()\n\n\tlet sectionSlug    = jQuery(e.currentTarget).data(\"as-section\")\n\tlet subsectionSlug = jQuery(e.currentTarget).data(\"as-subsection\")\n\n\tjQuery(`a[data-section-slug=${sectionSlug}]`).trigger(\"click\")\n\n\tjQuery(`ul[data-section-slug=${sectionSlug}]`).children(`[data-subsection-slug=${subsectionSlug}]`).trigger(\"click\")\n})\n", "jQuery(function () {\n\n\t/**\n\t * Copy debug info to clipboard when the button is clicked.\n\t */\n\tjQuery(\"#debug-info-button\").on(\"click\", () => {\n\t\t// Select text in textarea\n\t\tconst textarea = jQuery(\"#debug-info-textarea\")[0];\n\t\ttextarea.select();\n\t\ttextarea.setSelectionRange(0, 99999); // For mobile devices\n\n\t\t// Copy selected text to clipboard\n\t\twpm.copyTextToClipboard(jQuery(\"#debug-info-textarea\").val());\n\n\t\t// Show success message and hide after 3 seconds\n\t\tconst successElement = jQuery(\"#debug-info-success\");\n\t\tsuccessElement.fadeIn(200);\n\t\tsetTimeout(() => successElement.fadeOut(200), 3000);\n\t});\n\n\tjQuery(\"#pmw-pro-version-demo\").on(\"click\", function () {\n\t\tjQuery(\"#submit\").trigger(\"click\");\n\t});\n\n\tif (document.getElementById(\"json-settings-file-input\")) {\n\t\tdocument.getElementById(\"json-settings-file-input\")\n\t\t\t.addEventListener(\"change\", wpm.readSettingsFile, false);\n\t}\n\n\t// if element ga4-data-api-credentials-upload-button exists, then we are on the GA4 settings page\n\tif (document.getElementById(\"ga4-data-api-credentials-upload-button\")) {\n\n\t\tdocument.getElementById(\"ga4-data-api-credentials-upload-button\")\n\t\t\t.addEventListener(\"change\", wpm.readGa4DataApiCredentials, false);\n\t}\n\n\t// if element ga4-data-api-credentials-upload-button exists, then we are on the GA4 settings page\n\tif (document.getElementById(\"ga4-data-api-credentials-delete-button\")) {\n\n\t\tdocument.getElementById(\"ga4-data-api-credentials-delete-button\")\n\t\t\t.addEventListener(\"click\", () => {\n\t\t\t\twpm.saveGa4DataApiCredentialsToDb({});\n\t\t\t}, false);\n\t}\n\n\t/**\n\t * Handle log files download via REST API\n\t */\n\tjQuery(\"#wgact_download_logs_zip\").on(\"click\", function(e) {\n\t\te.preventDefault();\n\t\t\n\t\tif (jQuery(this).prop(\"disabled\")) {\n\t\t\treturn;\n\t\t}\n\t\t\n\t\tconst source = jQuery(this).data(\"source\");\n\t\tconst button = jQuery(this);\n\t\tconst originalText = button.text();\n\t\t\n\t\t// Disable button and change text\n\t\tbutton.prop(\"disabled\", true).text(\"Downloading...\");\n\t\t\n\t\t// Use fetch with proper authentication\n\t\tfetch(pmwAdminApi.root + \"pmw/v1/logs/download\", {\n\t\t\tmethod: \"POST\",\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/x-www-form-urlencoded\",\n\t\t\t\t\"X-WP-Nonce\": pmwAdminApi.nonce\n\t\t\t},\n\t\t\tbody: new URLSearchParams({\n\t\t\t\tsource: source\n\t\t\t}),\n\t\t\tcredentials: 'same-origin'\n\t\t})\n\t\t.then(response => {\n\t\t\tif (!response.ok) {\n\t\t\t\tthrow new Error(`HTTP error! status: ${response.status}`);\n\t\t\t}\n\t\t\treturn response.blob();\n\t\t})\n\t\t.then(blob => {\n\t\t\t// Create download link\n\t\t\tconst url = window.URL.createObjectURL(blob);\n\t\t\tconst a = document.createElement(\"a\");\n\t\t\ta.href = url;\n\t\t\ta.download = \"pmw-logs-\" + new Date().toISOString().slice(0,19).replace(/:/g, \"-\") + \".zip\";\n\t\t\tdocument.body.appendChild(a);\n\t\t\ta.click();\n\t\t\twindow.URL.revokeObjectURL(url);\n\t\t\tdocument.body.removeChild(a);\n\t\t\t\n\t\t\t// Re-enable the button\n\t\t\tbutton.prop(\"disabled\", false).text(originalText);\n\t\t})\n\t\t.catch(error => {\n\t\t\tconsole.error(\"Download error:\", error);\n\t\t\tbutton.prop(\"disabled\", false).text(originalText);\n\t\t\talert(\"Error downloading log files. Please try again.\");\n\t\t});\n\t});\n});\n\n// jQuery(function () {\n//\n// \timport(\"../../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\")\n// \t\t.then(({\n// \t\t\t\t   computePosition,\n// \t\t\t\t   flip,\n// \t\t\t\t   shift,\n// \t\t\t\t   offset,\n// \t\t\tarrow,\n// \t\t\t   }) => {\n// \t\t\tconsole.log(\"computePosition: \", computePosition)\n//\n// \t\t\tconst button = document.querySelector('#button');\n// \t\t\tconst tooltip = document.querySelector('#tooltip');\n// \t\t\tconst arrowElement = document.querySelector('#arrow');\n//\n// \t\t\tcomputePosition(button, tooltip, {\n// \t\t\t\tplacement: 'top',\n// \t\t\t\tmiddleware: [\n// \t\t\t\t\toffset(6),\n// \t\t\t\t\tflip(),\n// \t\t\t\t\tshift({padding: 5}),\n// \t\t\t\t\tarrow({element: arrowElement}),\n// \t\t\t\t],\n// \t\t\t}).then(({x, y}) => {\n// \t\t\t\tObject.assign(tooltip.style, {\n// \t\t\t\t\tleft: `${x}px`,\n// \t\t\t\t\ttop: `${y}px`,\n// \t\t\t\t});\n// \t\t\t});\n//\n// \t\t})\n// \t\t.catch(err => {\n// \t\t\tconsole.error(err)\n// \t\t})\n//\n// });\n\n\n(function (wpm, $, undefined) {\n\n\twpm.saveSettingsToDisk = () => {\n\t\tlet text = document.getElementById(\"export-settings-json\").value;\n\n\t\t// Extract timestamp from the settings JSON\n\t\tlet timestamp = \"unknown\";\n\t\ttry {\n\t\t\tlet settings = JSON.parse(text);\n\t\t\tif (settings.timestamp) {\n\t\t\t\ttimestamp = settings.timestamp;\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.warn(\"Could not extract timestamp from settings:\", error);\n\t\t}\n\n\t\ttext                 = text.replace(/\\n/g, \"\\r\\n\"); // To retain the Line breaks.\n\t\tlet blob             = new Blob([text], {type: \"text/plain\"});\n\t\tlet anchor           = document.createElement(\"a\");\n\t\tanchor.download      = \"pixel-manager-settings_\" + timestamp + \"_\" + wpm.getDateTimeFromTimestamp(timestamp) + \".json\";\n\t\tanchor.href          = window.URL.createObjectURL(blob);\n\t\tanchor.target        = \"_blank\";\n\t\tanchor.style.display = \"none\"; // just to be safe!\n\t\tdocument.body.appendChild(anchor);\n\t\tanchor.click();\n\t\tdocument.body.removeChild(anchor);\n\t};\n\n\t// Get date and time from timestamp in year.month.day_hour-minute-second format. All components are zero padded.\n\t// Uses WordPress site timezone to match what's displayed on admin pages.\n\twpm.getDateTimeFromTimestamp = (timestamp) => {\n\t\t// If timestamp is \"unknown\" or invalid, fall back to current date/time\n\t\tif (timestamp === \"unknown\" || !timestamp || isNaN(timestamp)) {\n\t\t\treturn wpm.getCurrentDateForFileName();\n\t\t}\n\n\t\t// Convert Unix timestamp (seconds) to milliseconds for JavaScript Date\n\t\tlet date = new Date(timestamp * 1000);\n\n\t\t// Apply WordPress timezone offset if available\n\t\tif (pmwAdminApi && pmwAdminApi.timezone && typeof pmwAdminApi.timezone.offset === \"number\") {\n\t\t\t// Create new date in WordPress timezone by adding the offset\n\t\t\tlet wpTimezoneOffsetMs = pmwAdminApi.timezone.offset * 60 * 60 * 1000; // Convert hours to milliseconds\n\t\t\tdate                   = new Date(date.getTime() + wpTimezoneOffsetMs);\n\n\t\t\t// Get WordPress timezone date/time components using UTC methods\n\t\t\t// (since we've already applied the offset to the date object)\n\t\t\tlet year    = date.getUTCFullYear();\n\t\t\tlet month   = (\"0\" + (date.getUTCMonth() + 1)).slice(-2);\n\t\t\tlet day     = (\"0\" + date.getUTCDate()).slice(-2);\n\t\t\tlet hours   = (\"0\" + date.getUTCHours()).slice(-2);\n\t\t\tlet minutes = (\"0\" + date.getUTCMinutes()).slice(-2);\n\t\t\tlet seconds = (\"0\" + date.getUTCSeconds()).slice(-2);\n\n\t\t\treturn year + \".\" + month + \".\" + day + \"_\" + hours + \"-\" + minutes + \"-\" + seconds;\n\t\t} else {\n\t\t\t// Fallback to browser local timezone if WordPress timezone data is not available\n\t\t\tlet year    = date.getFullYear();\n\t\t\tlet month   = (\"0\" + (date.getMonth() + 1)).slice(-2);\n\t\t\tlet day     = (\"0\" + date.getDate()).slice(-2);\n\t\t\tlet hours   = (\"0\" + date.getHours()).slice(-2);\n\t\t\tlet minutes = (\"0\" + date.getMinutes()).slice(-2);\n\t\t\tlet seconds = (\"0\" + date.getSeconds()).slice(-2);\n\t\t\treturn year + \".\" + month + \".\" + day + \"_\" + hours + \"-\" + minutes + \"-\" + seconds;\n\t\t}\n\t};\n\n\t// Get current date and time in year.month.day_hour-minute-second format. All components are zero padded.\n\t// Uses WordPress site timezone to match what's displayed on admin pages.\n\twpm.getCurrentDateForFileName = () => {\n\t\tlet date = new Date();\n\n\t\t// Apply WordPress timezone offset if available\n\t\tif (pmwAdminApi && pmwAdminApi.timezone && typeof pmwAdminApi.timezone.offset === \"number\") {\n\t\t\t// Apply WordPress timezone offset\n\t\t\tlet wpTimezoneOffsetMs = pmwAdminApi.timezone.offset * 60 * 60 * 1000; // Convert hours to milliseconds\n\t\t\tdate                   = new Date(date.getTime() + wpTimezoneOffsetMs);\n\n\t\t\t// Get WordPress timezone date/time components using UTC methods\n\t\t\t// (since we've already applied the offset to the date object)\n\t\t\tlet year    = date.getUTCFullYear();\n\t\t\tlet month   = (\"0\" + (date.getUTCMonth() + 1)).slice(-2);\n\t\t\tlet day     = (\"0\" + date.getUTCDate()).slice(-2);\n\t\t\tlet hours   = (\"0\" + date.getUTCHours()).slice(-2);\n\t\t\tlet minutes = (\"0\" + date.getUTCMinutes()).slice(-2);\n\t\t\tlet seconds = (\"0\" + date.getUTCSeconds()).slice(-2);\n\n\t\t\treturn year + \".\" + month + \".\" + day + \"_\" + hours + \"-\" + minutes + \"-\" + seconds;\n\t\t} else {\n\t\t\t// Fallback to browser local timezone if WordPress timezone data is not available\n\t\t\tlet year    = date.getFullYear();\n\t\t\tlet month   = (\"0\" + (date.getMonth() + 1)).slice(-2);\n\t\t\tlet day     = (\"0\" + date.getDate()).slice(-2);\n\t\t\tlet hours   = (\"0\" + date.getHours()).slice(-2);\n\t\t\tlet minutes = (\"0\" + date.getMinutes()).slice(-2);\n\t\t\tlet seconds = (\"0\" + date.getSeconds()).slice(-2);\n\t\t\treturn year + \".\" + month + \".\" + day + \"_\" + hours + \"-\" + minutes + \"-\" + seconds;\n\t\t}\n\n\t\t// return date.toLocaleDateString(\n\t\t// \t\"en-US\", {\n\t\t// \t\tyear : \"numeric\",\n\t\t// \t\tmonth: \"2-digit\",\n\t\t// \t\tday  : \"2-digit\",\n\t\t// \t},\n\t\t// )\n\t};\n\n\twpm.readSettingsFile = e => {\n\n\t\tlet file = e.target.files[0];\n\t\tif (!file) return;\n\t\tlet reader    = new FileReader();\n\t\treader.onload = function (e) {\n\n\t\t\t// Check if the file is a valid JSON file\n\t\t\t// If it is not a valid JSON file, display an error message\n\t\t\ttry {\n\t\t\t\tJSON.parse(e.target.result);\n\t\t\t} catch (error) {\n\t\t\t\tdocument.getElementById(\"settings-upload-status-error\").style.display     = \"block\";\n\t\t\t\tdocument.getElementById(\"settings-upload-status-error-message\").innerHTML = \"Invalid JSON file.\";\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet contents = JSON.parse(e.target.result);\n\n\t\t\t// document.getElementById(\"import-settings-json\").textContent = JSON.stringify(contents)\n\n\t\t\twpm.saveImportedSettingsToDb(contents);\n\t\t};\n\t\treader.readAsText(file);\n\t};\n\n\twpm.saveImportedSettingsToDb = settings => {\n\n\t\tfetch(pmwAdminApi.root + \"pmw/v1/settings/\", {\n\t\t\tmethod     : \"POST\",\n\t\t\tcredentials: \"same-origin\",\n\t\t\theaders    : {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t\"X-WP-Nonce\"  : pmwAdminApi.nonce,\n\t\t\t},\n\t\t\tbody       : JSON.stringify(settings),\n\t\t})\n\t\t\t.then(response => response.json())\n\t\t\t.then(async message => {\n\t\t\t\tif (message.success) {\n\t\t\t\t\tconsole.log(message);\n\t\t\t\t\t// reload window\n\t\t\t\t\tdocument.getElementById(\"settings-upload-status-success\").style.display = \"block\";\n\t\t\t\t\t// wait 5 seconds\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 1000));\n\t\t\t\t\twindow.location.reload();\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log(message);\n\t\t\t\t\tdocument.getElementById(\"settings-upload-status-error\").style.display = \"block\";\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(error => {\n\t\t\t\tconsole.error(error);\n\t\t\t\tdocument.getElementById(\"settings-upload-status-error\").style.display = \"block\";\n\t\t\t});\n\t};\n\n\twpm.restoreBackup = (timestamp) => {\n\n\t\tconsole.log(\"restoreBackup() - timestamp: \", timestamp);\n\n\t\tfetch(pmwAdminApi.root + \"pmw/v1/options-backup/\" + timestamp + \"/restore\", {\n\t\t\tmethod     : \"POST\",\n\t\t\tcredentials: \"same-origin\",\n\t\t\theaders    : {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t\"X-WP-Nonce\"  : pmwAdminApi.nonce,\n\t\t\t},\n\t\t})\n\t\t\t.then(response => response.json())\n\t\t\t.then(async message => {\n\n\t\t\t\tif (message?.data?.message) {\n\t\t\t\t\tconsole.log(\"restoreBackup() - message: \", message.data.message);\n\t\t\t\t}\n\n\t\t\t\tif (message.success) {\n\t\t\t\t\twindow.location.reload();\n\t\t\t\t} else {\n\t\t\t\t\talert(message.data.message);\n\t\t\t\t}\n\n\t\t\t})\n\t\t\t.catch(error => {\n\t\t\t\tconsole.error(error);\n\t\t\t\talert(error.message);\n\t\t\t});\n\t};\n\n\twpm.readGa4DataApiCredentials = e => {\n\n\t\tlet file = e.target.files[0];\n\t\tif (!file) return;\n\t\tlet reader    = new FileReader();\n\t\treader.onload = function (e) {\n\n\t\t\t// Check if the file is a valid JSON file\n\t\t\t// If it is not a valid JSON file, display an error message\n\t\t\ttry {\n\t\t\t\tJSON.parse(e.target.result);\n\t\t\t} catch (error) {\n\t\t\t\tdocument.getElementById(\"ga4-api-credentials-upload-status-error\").style.display     = \"block\";\n\t\t\t\tdocument.getElementById(\"ga4-api-credentials-upload-status-error-message\").innerHTML = \"Invalid JSON file.\";\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tlet contents = JSON.parse(e.target.result);\n\n\t\t\t// document.getElementById(\"import-settings-json\").textContent = JSON.stringify(contents)\n\n\t\t\twpm.saveGa4DataApiCredentialsToDb(contents);\n\t\t};\n\t\treader.readAsText(file);\n\t};\n\n\twpm.deleteGa4DataApiCredentials = e => {\n\t\twpm.saveGa4DataApiCredentialsToDb({});\n\t};\n\n\twpm.saveGa4DataApiCredentialsToDb = credentials => {\n\n\t\tfetch(pmwAdminApi.root + \"pmw/v1/ga4/data-api/credentials\", {\n\t\t\tmethod     : \"POST\",\n\t\t\tcredentials: \"same-origin\",\n\t\t\theaders    : {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t\"X-WP-Nonce\"  : pmwAdminApi.nonce,\n\t\t\t},\n\t\t\tbody       : JSON.stringify(credentials),\n\t\t})\n\t\t\t.then(response => response.json())\n\t\t\t.then(async responseJson => {\n\t\t\t\tif (responseJson.success) {\n\t\t\t\t\tconsole.log(responseJson);\n\t\t\t\t\t// reload window\n\t\t\t\t\tdocument.getElementById(\"ga4-api-credentials-upload-status-success\").style.display = \"block\";\n\t\t\t\t\t// wait 5 seconds\n\t\t\t\t\t// await new Promise(resolve => setTimeout(resolve, 5000))\n\t\t\t\t\twindow.location.reload();\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log(responseJson);\n\t\t\t\t\tdocument.getElementById(\"ga4-api-credentials-upload-status-error\").style.display     = \"block\";\n\t\t\t\t\t// and add the error message\n\t\t\t\t\tdocument.getElementById(\"ga4-api-credentials-upload-status-error-message\").innerHTML = \"Error message: \" + responseJson.data.message;\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(error => {\n\t\t\t\tconsole.error(error);\n\t\t\t\tdocument.getElementById(\"ga4-api-credentials-upload-status-error\").style.display = \"block\";\n\t\t\t});\n\t};\n\n\twpm.getAiBotUrl = () => {\n\t\tif (pmw_cody?.url) {\n\t\t\treturn pmw_cody.url;\n\t\t}\n\t};\n\n\t/**\n\t * openChatModal must open a modal window with the chat window in it\n\t * It is an overlay over the current page, half transparent\n\t * 80% of the overlay is used by the actual chat window\n\t * The chat window is a chatbot that's loaded from https://widget.getcody.ai/public/9a20c1c8-4323-4656-a4f8-02face5dc0e7\n\t */\n\twpm.loadAiChatWindow = () => {\n\n\t\t// console.log(\"openChatModal()\")\n\n\t\t// add a half transparent overlay over the current page\n\t\t// the overlay is 100% width and 100% height\n\t\t// the overlay is fixed to the top left corner of the page\n\t\t// the overlay has a z-index of 1000\n\t\t// the overlay has a background color of rgba(0,0,0,0.5)\n\n\t\t// console.log(\"openChatModal() - add overlay\")\n\n\t\tlet overlay                   = document.createElement(\"div\");\n\t\toverlay.id                    = \"pmw-chat-overlay\";\n\t\toverlay.style.position        = \"fixed\";\n\t\toverlay.style.top             = \"0\";\n\t\toverlay.style.left            = \"0\";\n\t\toverlay.style.width           = \"100%\";\n\t\toverlay.style.height          = \"100%\";\n\t\toverlay.style.zIndex          = \"1000\";\n\t\toverlay.style.backgroundColor = \"rgba(0,0,0,0.5)\";\n\t\toverlay.style.display         = \"flex\";\n\t\toverlay.style.justifyContent  = \"center\";\n\t\toverlay.style.alignItems      = \"center\";\n\n\t\t// now show the overlay within the element with ID \"wpbody\"\n\n\t\t// console.log(\"openChatModal() - show overlay\")\n\n\t\tlet wpbody = document.getElementById(\"wpbody\");\n\t\twpbody.appendChild(overlay);\n\n\t\t// within that overly add a div that is 80% of the width and 80% of the height\n\t\t// it has white background and a z-index of 1001\n\n\t\t// console.log(\"openChatModal() - add chat window container\")\n\n\t\tvar chatWindowContainer                  = document.createElement(\"div\");\n\t\tchatWindowContainer.id                   = \"pmw-chat-window-container\";\n\t\tchatWindowContainer.style.width          = \"80%\";\n\t\tchatWindowContainer.style.height         = \"80%\";\n\t\t// chatWindowContainer.style.backgroundColor = 'white';\n\t\tchatWindowContainer.style.zIndex         = \"1001\";\n\t\tchatWindowContainer.style.display        = \"flex\";\n\t\tchatWindowContainer.style.justifyContent = \"center\";\n\t\tchatWindowContainer.style.alignItems     = \"center\";\n\t\t// left margin 160px\n\t\tchatWindowContainer.style.marginLeft     = \"160px\";\n\n\t\t// now show the chat window container\n\t\toverlay.appendChild(chatWindowContainer);\n\n\t\t// within that chat window container add an iframe that loads the chat window\n\n\t\t// console.log(\"openChatModal() - add chat window iframe\")\n\n\t\tvar chatWindowIframe = document.createElement(\"iframe\");\n\n\t\tchatWindowIframe.src          = wpm.getAiBotUrl();\n\t\tchatWindowIframe.style.width  = \"100%\";\n\t\tchatWindowIframe.style.height = \"100%\";\n\t\tchatWindowIframe.style.border = \"none\";\n\n\t\t// now show the chat window iframe\n\t\tchatWindowContainer.appendChild(chatWindowIframe);\n\n\t\t// when clicking outside of the chat window container, close the chat window\n\t\toverlay.addEventListener(\"click\", function (event) {\n\t\t\t// console.log(\"overlay.addEventListener()\")\n\t\t\tconsole.log(event.target);\n\t\t\tif (event.target.id === \"pmw-chat-overlay\") {\n\t\t\t\tconsole.log(\"overlay.addEventListener() - close chat window\");\n\t\t\t\toverlay.remove();\n\t\t\t}\n\t\t});\n\n\t\t// also close the chat window when pressing the ESC key\n\t\tdocument.addEventListener(\"keydown\", function (event) {\n\t\t\tif (event.key === \"Escape\") {\n\t\t\t\t// console.log(\"document.addEventListener() - close chat window\")\n\t\t\t\toverlay.remove();\n\t\t\t}\n\t\t}, true);\n\t};\n\n\t/**\n\t * Check if the Cody AI chatbot is available.\n\t *\n\t * @return {boolean}\n\t */\n\twpm.codyAvailable = () => {\n\t\tif (typeof window.pmw_cody !== \"undefined\" && window.pmw_cody?.available) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t};\n\n\t// Schedule a recalculation of the LTV by sending a POST REST request to the server\n\twpm.ltvRecalculation = event => {\n\n\t\tconst displayStatusMessage = (message) => {\n\t\t\tlet elements = document.getElementsByClassName(\"ltv-message\");\n\t\t\tfor (let i = 0; i < elements.length; i++) {\n\t\t\t\telements[i].style.display = \"none\";\n\t\t\t}\n\t\t\tdocument.getElementById(\"ltv-message-error\").style.display  = \"block\";\n\t\t\tdocument.getElementById(\"ltv-message-error-text\").innerHTML = message;\n\t\t};\n\n\t\tconst displayRunImmediatelyButton = () => {\n\t\t\tlet elements = document.getElementsByClassName(\"ltv-button-text\");\n\t\t\tfor (let i = 0; i < elements.length; i++) {\n\t\t\t\telements[i].style.display = \"none\";\n\t\t\t}\n\t\t\tdocument.getElementById(\"ltv-instant-recalculation-button-text\").style.display = \"block\";\n\t\t};\n\n\t\tconst removeAllMessages = () => {\n\t\t\tlet elements = document.getElementsByClassName(\"ltv-message\");\n\t\t\tfor (let i = 0; i < elements.length; i++) {\n\t\t\t\telements[i].style.display = \"none\";\n\t\t\t}\n\n\t\t\tdocument.getElementById(\"ltv-message-error\").style.display = \"none\";\n\t\t};\n\n\t\t// console log the data-action from the nested span element of the clicked element\n\t\t// console.log(\"event.target.dataset.action\", event.target.dataset.action)\n\n\t\tfetch(pmwAdminApi.root + \"pmw/v1/ltv/\", {\n\t\t\tmethod     : \"POST\",\n\t\t\tcredentials: \"same-origin\",\n\t\t\theaders    : {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t\"X-WP-Nonce\"  : pmwAdminApi.nonce,\n\t\t\t},\n\t\t\tbody       : JSON.stringify({\n\t\t\t\t\"action\": event.target.dataset.action,\n\t\t\t}),\n\t\t})\n\t\t\t.then(response => response.json())\n\t\t\t.then(message => {\n\t\t\t\tif (message.success) {\n\t\t\t\t\tconsole.log(message);\n\t\t\t\t\t// reload window\n\n\t\t\t\t\tif (message.data.status.is_scheduled) {\n\t\t\t\t\t\tremoveAllMessages();\n\t\t\t\t\t\tdisplayRunImmediatelyButton();\n\t\t\t\t\t\tdocument.getElementById(\"ltv-schedule-recalculation-confirmation-message\").style.display = \"block\";\n\t\t\t\t\t}\n\n\t\t\t\t\tif (message.data.status.is_running) {\n\t\t\t\t\t\t// Get the button element with the id \"wgact_ltv_recalculation\" and disable it\n\t\t\t\t\t\tdocument.getElementById(\"wgact_ltv_recalculation\").disabled = true;\n\t\t\t\t\t\tremoveAllMessages();\n\t\t\t\t\t\tdocument.getElementById(\"ltv-running-recalculation-confirmation-message\").style.display = \"block\";\n\t\t\t\t\t}\n\n\t\t\t\t\t// if is neither message.data.status.is_running nor message.data.status.is_scheduled\n\t\t\t\t\t// then display the error message\n\t\t\t\t\tif (!message.data.status.is_running && !message.data.status.is_scheduled) {\n\t\t\t\t\t\tdisplayStatusMessage(message.data.message);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error(message);\n\t\t\t\t\tdisplayStatusMessage(message.data.message);\n\t\t\t\t}\n\t\t\t})\n\t\t\t.catch(error => {\n\t\t\t\tconsole.error(error);\n\t\t\t\tdisplayStatusMessage(error);\n\t\t\t});\n\t};\n\n\twpm.copyTextToClipboard = (text) => {\n\t\tnavigator.clipboard.writeText(text)\n\t\t\t.catch(err => {\n\t\t\t\tconsole.error(\"Failed to copy: \", err);\n\t\t\t});\n\t};\n\n}(window.wpm = window.wpm || {}, jQuery));\n\n/**\n * This script is used to track the mouse position and set the transparency\n * of the button with which the pro-demo can be enabled.\n */\ndocument.addEventListener(\"DOMContentLoaded\", () => {\n\n\t// Get the pmw_pro_version_demo element and save it in a variable\n\tconst elementToTrack = document.getElementById(\"pmw-pro-version-demo\");\n\n\t// Only continue if the element exists\n\tif (!elementToTrack) {\n\t\treturn;\n\t}\n\n\t// This is a span element that contains two input elements.\n\t// Only continue if both are not checked.\n\tif (elementToTrack.querySelector(\"input:checked\")) {\n\t\treturn;\n\t}\n\n\tconst maxWidth = window.innerWidth / 2;\n\n\tconst calculateTheDistanceInPercent = (mousePosition, maxWidth) => {\n\n\t\t// Get the element position and save it in a variable\n\t\tconst elementPosition = elementToTrack.getBoundingClientRect();\n\t\tconst distance        = Math.sqrt(Math.pow(mousePosition.clientX - elementPosition.left, 2) + Math.pow(mousePosition.clientY - elementPosition.top, 2));\n\n\t\treturn distance / maxWidth;  // returns the percentage\n\t};\n\n\tconst setTransparency = (percentage) => {\n\n\t\tif (percentage > 1) {\n\t\t\tpercentage = 1;\n\t\t}\n\n\t\tconst maximumReduction = 80;\n\t\tconst reduction        = maximumReduction * percentage;\n\t\tconst transparency     = 100 - reduction;\n\n\t\telementToTrack.classList.add(\"pmw-opacity-transition\");\n\t\telementToTrack.style.opacity = transparency / 100;\n\t};\n\n\tdocument.addEventListener(\"mousemove\", event => {\n\t\tconst distanceInPercent = calculateTheDistanceInPercent(event, maxWidth);\n\t\tsetTransparency(distanceInPercent);\n\t});\n}, true);\n\n/**\n * The function waits for the DOMContentLoaded event,\n * and then it makes a call to get the server status of an aiBot (provided by its URL).\n * If the server status code is 200, it changes the display style of an HTML element with the id \"pmw-chat-li\" to \"block\",\n * making it visible in the webpage. If the server status request fails, it logs the message\n * \"DOMContentLoaded - chatbot is not available\" to the console.\n *\n * @listens document#DOMContentLoaded\n */\ndocument.addEventListener(\"DOMContentLoaded\", () => {\n\n\tif (wpmDataLayer.version.distro !== \"fms\") return;\n\n\tif (wpm.codyAvailable()) {\n\t\tdocument.getElementById(\"pmw-chat-li\").style.display = \"block\";\n\t} else {\n\t\tconsole.log(\"DOMContentLoaded - chatbot is not available\");\n\t}\n\n}, true);\n\n// Wait until the DOM is loaded, then console log\n// the message \"DOMContentLoaded - chatbot is not available\"\n// use jquery\njQuery(() => {\n\tif (document.querySelector(\"#wgact_ltv_recalculation\")) {\n\t\tdocument.querySelector(\"#wgact_ltv_recalculation\").addEventListener(\"click\", event => {\n\t\t\tevent.preventDefault();\n\t\t\twpm.ltvRecalculation(event);\n\t\t});\n\t}\n\n\tif (document.querySelector(\"#pmw_stop_ltv_calculation\")) {\n\t\tdocument.querySelector(\"#pmw_stop_ltv_calculation\").addEventListener(\"click\", event => {\n\t\t\tevent.preventDefault();\n\t\t\twpm.ltvRecalculation(event);\n\t\t});\n\t}\n\n\t// Add event listeners for backup restore buttons\n\tjQuery(() => {\n\t\tjQuery(document).on(\"click\", \".pmw-restore-backup-button\", function (event) {\n\t\t\tevent.preventDefault();\n\t\t\tconst timestamp = jQuery(this).data(\"timestamp\");\n\t\t\tif (timestamp) {\n\t\t\t\twpm.restoreBackup(timestamp);\n\t\t\t}\n\t\t});\n\t});\n});\n\n/**\n * add an event listener to the button with the id \"wgact_copy_log_file_links\"\n * when the button is clicked, copy the log file links to the clipboard\n * the data-links attribute of the button contains a json string with the links\n */\njQuery(() => {\n\n\tif (!document.querySelector(\"#wgact_copy_log_file_links\")) {\n\t\treturn;\n\t}\n\n\tdocument.querySelector(\"#wgact_copy_log_file_links\").addEventListener(\"click\", event => {\n\t\tevent.preventDefault();\n\n\t\tconst jsonOfLinks = JSON.parse(event.target.dataset.links);\n\n\t\tlet strOfLinks = \"\";\n\n\t\tfor (let i = 0, len = jsonOfLinks.length; i < len; i++) {\n\t\t\tstrOfLinks += jsonOfLinks[i] + \"\\n\";\n\t\t}\n\n\t\twpm.copyTextToClipboard(strOfLinks);\n\n\t\t// make the button text change to \"Copied!\" and keep the same width and text for 3 seconds\n\t\tlet button         = document.getElementById(\"wgact_copy_log_file_links\");\n\t\tbutton.style.width = getComputedStyle(button).width;\n\t\tconst originalText = button.innerHTML;\n\t\tbutton.innerHTML   = event.target.dataset.textCopied;\n\t\tsetTimeout(function () {\n\t\t\tbutton.innerHTML = originalText;\n\t\t}, 3000);\n\t});\n});\n\n// on pressing enter in form inputs, click the submit button\njQuery(() => {\n\tjQuery(document).on(\"keydown\", \"input, textarea, select\", function (event) {\n\t\tif (event.key === \"Enter\" && !event.shiftKey && !event.ctrlKey && !event.altKey) {\n\t\t\t// Only trigger submit if we're in a form and not in a textarea (where Enter should create new lines)\n\t\t\tconst $target = jQuery(event.target);\n\t\t\tif ($target.is(\"input\") && $target.closest(\"form\").length > 0) {\n\t\t\t\tconst form = $target.closest(\"form\")[0];\n\t\t\t\tconst submitButton = jQuery(form).find(\"#submit\");\n\t\t\t\tif (submitButton.length > 0) {\n\t\t\t\t\tevent.preventDefault();\n\t\t\t\t\tsubmitButton.click();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t});\n});\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * Combine all admin scripts\n * (which only run on the wpm pages)\n */\n\n// require(\"./environment-check\")\nrequire(\"./helpers\")\nrequire(\"./script-blocker-warning\")\nrequire(\"./tabs\")\n\n// #if process.env.TIER === 'premium'\n// // require(\"./helpers_premium\")\n// #endif\n\n// console.log('Pixel Manager for WooCommerce admin script')\n"], "names": ["wpm_hide_script_blocker_warning", "j<PERSON><PERSON><PERSON>", "hide", "wpmGetPageId", "sections", "subsections", "closest", "each", "push", "this", "data", "for<PERSON>ach", "section", "append", "after", "wpmCreateSubtabUlHtml", "on", "e", "preventDefault", "addClass", "siblings", "removeClass", "sectionSlug", "wpmToggleSections", "children", "trigger", "stopPropagation", "wpmToggleSubsection", "parent", "wpmGetSectionParams", "sectionParams", "subsectionsKeys", "Object", "keys", "html", "subsectionKey", "subtab", "queryString", "window", "location", "search", "urlParams", "URLSearchParams", "get", "nextUntil", "andSelf", "show", "sectionPos", "findIndex", "arrayElement", "prevAll", "next", "wpmSetUrl", "subsectionSlug", "delete", "newParams", "history", "pushState", "document", "pathname", "val", "wpmGetAdminPath", "URL", "attr", "includes", "appendTo", "remove", "currentTarget", "textarea", "select", "setSelectionRange", "wpm", "copyTextToClipboard", "successElement", "fadeIn", "setTimeout", "fadeOut", "getElementById", "addEventListener", "readSettingsFile", "readGa4DataApiCredentials", "saveGa4DataApiCredentialsToDb", "prop", "source", "button", "originalText", "text", "fetch", "pmwAdminApi", "root", "method", "headers", "nonce", "body", "credentials", "then", "response", "ok", "Error", "status", "blob", "url", "createObjectURL", "a", "createElement", "href", "download", "Date", "toISOString", "slice", "replace", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "catch", "error", "console", "alert", "saveSettingsToDisk", "value", "timestamp", "settings", "JSON", "parse", "warn", "Blob", "type", "anchor", "getDateTimeFromTimestamp", "target", "style", "display", "isNaN", "getCurrentDateForFileName", "date", "timezone", "offset", "wpTimezoneOffsetMs", "getTime", "getUTCFullYear", "getUTCMonth", "getUTCDate", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "getSeconds", "file", "files", "reader", "FileReader", "onload", "result", "innerHTML", "contents", "saveImportedSettingsToDb", "readAsText", "stringify", "json", "async", "message", "success", "log", "Promise", "resolve", "reload", "restoreBackup", "deleteGa4DataApiCredentials", "responseJson", "getAiBotUrl", "pmw_cody", "loadAiChatWindow", "overlay", "id", "position", "top", "left", "width", "height", "zIndex", "backgroundColor", "justifyContent", "alignItems", "chatWindowContainer", "marginLeft", "chatWindowIframe", "src", "border", "event", "key", "codyAvailable", "available", "ltvRecalculation", "displayStatusMessage", "elements", "getElementsByClassName", "i", "length", "removeAllMessages", "dataset", "action", "is_scheduled", "displayRunImmediatelyButton", "is_running", "disabled", "navigator", "clipboard", "writeText", "err", "elementToTrack", "querySelector", "max<PERSON><PERSON><PERSON>", "innerWidth", "distanceInPercent", "calculateTheDistanceInPercent", "mousePosition", "elementPosition", "getBoundingClientRect", "Math", "sqrt", "pow", "clientX", "clientY", "percentage", "transparency", "classList", "add", "opacity", "setTransparency", "wpmDataLayer", "version", "distro", "jsonOfLinks", "links", "strOfLinks", "len", "getComputedStyle", "textCopied", "shift<PERSON>ey", "ctrl<PERSON>ey", "altKey", "$target", "is", "form", "submitButton", "find", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "require"], "sourceRoot": ""}