<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    46700 => 'Tele2 Sverige',
    467010 => 'SPINBOX AB',
    467011 => 'Telenor Sverige',
    467012 => 'SPINBOX AB',
    46701332 => 'EU Tel AB',
    46701334 => 'EU Tel AB',
    46701335 => 'EU Tel AB',
    46701336 => 'EU Tel AB',
    46701338 => 'EU Tel AB',
    46701339 => 'EU Tel AB',
    46701341 => 'EU Tel AB',
    46701342 => 'EU Tel AB',
    46701346 => 'EU Tel AB',
    46701347 => 'EU Tel AB',
    46701348 => 'EU Tel AB',
    46701349 => 'EU Tel AB',
    46701350 => '42 Telecom AB',
    46701353 => 'EU Tel AB',
    46701356 => 'EU Tel AB',
    46701358 => 'EU Tel AB',
    46701359 => 'EU Tel AB',
    46701362 => 'EU Tel AB',
    46701364 => '42 Telecom AB',
    46701365 => '42 Telecom AB',
    46701366 => '42 Telecom AB',
    46701367 => '42 Telecom AB',
    46701368 => '42 Telecom AB',
    46701369 => '42 Telecom AB',
    4670137 => '42 Telecom AB',
    46701381 => '42 Telecom AB',
    46701383 => '42 Telecom AB',
    46701384 => '42 Telecom AB',
    46701385 => '42 Telecom AB',
    46701386 => '42 Telecom AB',
    46701388 => '42 Telecom AB',
    46701389 => '42 Telecom AB',
    46701390 => '42 Telecom AB',
    46701391 => '42 Telecom AB',
    46701392 => '42 Telecom AB',
    46701393 => '42 Telecom AB',
    46701394 => '42 Telecom AB',
    46701396 => '42 Telecom AB',
    46701397 => '42 Telecom AB',
    46701398 => '42 Telecom AB',
    46701399 => '42 Telecom AB',
    467014 => 'Telenor Sverige',
    467015 => 'Tele2 Sverige',
    467016 => 'Tele2 Sverige',
    46701717 => '42 Telecom AB',
    46701741 => '42 Telecom AB',
    46701779 => 'EU Tel AB',
    46701780 => '42 Telecom AB',
    46701781 => '42 Telecom AB',
    46701782 => '42 Telecom AB',
    46701783 => '42 Telecom AB',
    46701784 => '42 Telecom AB',
    46701785 => '42 Telecom AB',
    46701786 => '42 Telecom AB',
    46701788 => 'Ventelo Sverige',
    46701790 => 'Svea Billing System',
    46701791 => 'Svea Billing System',
    46701792 => 'Svea Billing System',
    46701793 => 'Svea Billing System',
    46701794 => 'Svea Billing System',
    46701795 => 'Svea Billing System',
    46701796 => 'Svea Billing System',
    46701797 => 'EU Tel AB',
    46701798 => 'Gotalandsnatet',
    467018 => 'SPINBOX AB',
    4670189 => 'Alltele Sverige',
    46701897 => 'Gotalandsnatet',
    4670190 => 'Ventelo Sverige',
    4670191 => 'Ventelo Sverige',
    46701920 => 'Viatel Sweden',
    46701921 => 'Beepsend',
    46701924 => 'Compatel Limited',
    46701925 => 'Mobile Arts AB',
    46701926 => 'Beepsend',
    46701928 => 'HORISEN AG',
    4670193 => 'Com Hem',
    4670194 => 'Gotalandsnatet',
    4670195 => 'Gotalandsnatet',
    46701965 => '42 Telecom AB',
    46701966 => '42 Telecom AB',
    46701967 => '42 Telecom AB',
    46701968 => '42 Telecom AB',
    4670197 => 'Weblink IP Phone',
    46701977 => '42 Telecom AB',
    46701978 => '42 Telecom AB',
    46701979 => '42 Telecom AB',
    4670198 => 'IP-Only Telecommunication',
    46701990 => 'Telenor Sverige',
    46701991 => 'Telenor Sverige',
    46701992 => 'Telenor Sverige',
    46701993 => 'Telenor Sverige',
    46701994 => 'Telenor Sverige',
    46701995 => 'Telenor Sverige',
    46701997 => '42 Telecom AB',
    46701998 => 'MERCURY INTERNATIONA',
    46701999 => '42 Telecom AB',
    46702 => 'TeliaSonera',
    46703 => 'TeliaSonera',
    46704 => 'Tele2 Sverige',
    46705 => 'TeliaSonera',
    46706 => 'TeliaSonera',
    46707 => 'Tele2 Sverige',
    46708 => 'Telenor Sverige',
    46709 => 'Telenor Sverige',
    467200 => 'Tele2 Sverige',
    467201 => 'Tele2 Sverige',
    467202 => 'Tele2 Sverige',
    467203 => 'Tele2 Sverige',
    467204 => 'Tele2 Sverige',
    46720501 => 'Generic Mobil Systems',
    46720502 => 'Telavox AB',
    46720503 => 'Telavox AB',
    46720504 => 'Telavox AB',
    46720505 => 'Telavox AB',
    46720506 => 'Telavox AB',
    46720507 => 'Telavox AB',
    46720509 => 'Telavox AB',
    4672051 => 'WIFOG AB',
    4672052 => 'WIFOG AB',
    4672053 => 'WIFOG AB',
    4672054 => 'WIFOG AB',
    4672055 => 'Bahnhof AB',
    4672056 => 'Bahnhof AB',
    4672057 => 'WIFOG AB',
    46720580 => 'MERCURY INTERNATIONA',
    46720581 => 'Beepsend',
    46720582 => 'iCentrex Sweden AB',
    46720583 => 'iCentrex Sweden AB',
    46720584 => 'iCentrex Sweden AB',
    46720585 => 'iCentrex Sweden AB',
    46720586 => 'iCentrex Sweden AB',
    4672059 => 'Telenor Sverige',
    467206 => 'Com Hem',
    467207 => 'SOLUNO BC AB',
    46720801 => 'Telavox AB',
    46720802 => 'Telavox AB',
    46720803 => 'Telavox AB',
    46720806 => 'TeliaSonera',
    46720807 => 'Telavox AB',
    46720808 => 'Telavox AB',
    4672081 => 'BM Sverige AB',
    4672082 => 'Fibio Nordic AB',
    4672083 => 'Tele2 Sverige',
    4672084 => 'Tele2 Sverige',
    4672085 => 'Tele2 Sverige',
    4672087 => 'Telenor Sverige',
    4672088 => 'Telenor Sverige',
    46720893 => 'TeliaSonera',
    46720894 => 'TeliaSonera',
    46720895 => 'TeliaSonera',
    46720896 => 'TeliaSonera',
    46720898 => 'Xplora',
    46720902 => 'Telavox AB',
    46720908 => 'Telavox AB',
    4672092 => 'Telavox AB',
    46720995 => 'Telavox AB',
    46720996 => 'Telavox AB',
    46720999 => 'MOBIWEB LTD',
    467210 => 'SVENSK KONSUMENTMOBI',
    467211 => 'SVENSK KONSUMENTMOBI',
    467212 => 'TeliaSonera',
    467213 => 'TeliaSonera',
    4672140 => 'Bredband 2',
    4672141 => 'Tele2 Sverige',
    4672142 => 'Tele2 Sverige',
    4672143 => 'Tele2 Sverige',
    4672144 => 'Tele2 Sverige',
    4672145 => 'Tele2 Sverige',
    4672146 => 'Tele2 Sverige',
    4672147 => 'Tele2 Sverige',
    4672148 => 'Tele2 Sverige',
    46721490 => 'Tele2 Sverige',
    46721491 => 'Tele2 Sverige',
    46721492 => 'Tele2 Sverige',
    46721493 => 'Tele2 Sverige',
    46721494 => 'Tele2 Sverige',
    46721495 => 'Beepsend',
    46721497 => 'MONTY UK GLOBAL LIM',
    46721498 => 'Beepsend',
    467215 => 'Telenor Sverige',
    467216 => 'Telenor Sverige',
    467217 => 'Telenor Sverige',
    467218 => 'Telenor Sverige',
    467219 => 'Telenor Sverige',
    46722 => 'TeliaSonera',
    467230 => 'HI3G Access',
    467231 => 'HI3G Access',
    467232 => 'HI3G Access',
    467233 => 'HI3G Access',
    46723401 => 'LOXYTEL AB',
    46723403 => 'Beepsend',
    46723404 => 'LOXYTEL AB',
    46723405 => 'LOXYTEL AB',
    46723406 => 'LOXYTEL AB',
    46723407 => 'LOXYTEL AB',
    46723408 => 'ONOFF TELECOM SAS',
    46723409 => 'ONOFF TELECOM SAS',
    4672341 => 'TELIGOO AB (Fello AB)',
    4672342 => 'Telenor Sverige',
    4672343 => 'MESSAGEBIRD B.V.',
    46723440 => 'Beepsend',
    46723442 => 'Xplora',
    46723443 => 'Xplora',
    46723445 => 'Xplora',
    46723446 => 'Xplora',
    46723447 => 'Xplora',
    46723449 => 'Beepsend',
    4672345 => '42 Telecom AB',
    46723460 => 'Beepsend',
    46723461 => 'Telenor Sverige',
    46723464 => 'Telenor Sverige',
    46723466 => 'Telenor Sverige',
    46723468 => 'Telenor Sverige',
    4672347 => 'Benemen Oy',
    4672348 => 'Benemen Oy',
    46723490 => 'Beepsend',
    46723491 => 'Telenor Sverige',
    46723499 => 'Beepsend',
    467235 => 'Telenor Sverige',
    467236 => 'Telenor Sverige',
    467237 => 'Telenor Sverige',
    467238 => 'Telenor Sverige',
    467239 => 'Telenor Sverige',
    46724000 => 'Telenor Sverige',
    46724001 => 'Beepsend',
    46724002 => 'Voice Integrate',
    46724003 => 'Voice Integrate',
    46724004 => 'Beepsend',
    46724008 => 'Telavox AB',
    4672401 => 'Telavox AB',
    4672402 => 'Telavox AB',
    467242 => 'WIFOG AB',
    467243 => 'WIFOG AB',
    467244 => 'Telenor Sverige',
    467245 => 'TeliaSonera',
    467246 => 'TeliaSonera',
    467247 => 'TeliaSonera',
    467248 => 'TeliaSonera',
    467249 => 'TeliaSonera',
    46725 => 'TeliaSonera',
    46726000 => 'Beepsend',
    46726001 => 'FINK TELECOM SERVIC',
    46726003 => 'MOBIWEB LTD',
    46726004 => 'Tele2 Sverige',
    46726005 => 'Tele2 Sverige',
    46726006 => 'Telavox AB',
    46726008 => 'Global Telefoni Sve',
    4672601 => 'Telavox AB',
    4672603 => 'Tele2 Sverige',
    4672604 => 'Tele2 Sverige',
    4672605 => 'Tele2 Sverige',
    4672606 => 'Tele2 Sverige',
    4672607 => 'Tele2 Sverige',
    4672608 => 'Tele2 Sverige',
    467261 => 'GLOBETOUCH AB',
    467262 => 'GLOBETOUCH AB',
    467263 => 'GLOBETOUCH AB',
    4672640 => 'SPIRIUS AB',
    4672641 => 'SPIRIUS AB',
    46726421 => 'WARSIN HOLDING AB',
    46726422 => 'Beepsend',
    46726423 => 'Global Telefoni Sve',
    46726424 => 'Global Telefoni Sve',
    46726425 => 'Global Telefoni Sve',
    46726426 => 'Global Telefoni Sve',
    46726427 => 'Global Telefoni Sve',
    46726428 => 'Global Telefoni Sve',
    46726429 => 'Global Telefoni Sve',
    4672644 => 'Telenor Sverige',
    4672648 => 'Telavox AB',
    467265 => 'TeliaSonera',
    4672660 => 'Telenor Sverige',
    4672666 => 'Telenor Sverige',
    4672669 => 'Nortech',
    467267 => 'TeliaSonera',
    467268 => 'TeliaSonera',
    4672695 => 'Telenor Sverige',
    4672698 => 'SWEDFONENET AB',
    46726990 => 'Gotalandsnatet',
    46726991 => 'Fast Communication',
    46726992 => 'Fast Communication',
    46726993 => 'SWEDFONENET AB',
    46726994 => 'SWEDFONENET AB',
    46726995 => 'SWEDFONENET AB',
    46726996 => 'Nortech',
    46726997 => 'ONOFF TELECOM SAS',
    46726998 => 'ONOFF TELECOM SAS',
    467270 => 'TeliaSonera',
    467271 => 'TeliaSonera',
    467272 => 'TeliaSonera',
    467273 => 'TeliaSonera',
    467274 => 'TeliaSonera',
    46727501 => 'ONOFF TELECOM SAS',
    46727502 => 'ONOFF TELECOM SAS',
    46727503 => 'MINITEL AB',
    46727504 => 'FINK TELECOM SERVIC',
    46727506 => 'FINK TELECOM SERVIC',
    46727507 => 'FINK TELECOM SERVIC',
    46727510 => 'ONOFF TELECOM SAS',
    46727511 => 'ONOFF TELECOM SAS',
    46727515 => 'FINK TELECOM SERVIC',
    46727516 => 'FINK TELECOM SERVIC',
    4672753 => 'NETMORE GROUP AB',
    4672754 => 'Telenor Sverige',
    4672755 => 'FINK TELECOM SERVIC',
    4672756 => 'FINK TELECOM SERVIC',
    4672758 => 'Lancelot Telecom',
    467276 => 'Lycamobile Sweden',
    467277 => 'Lycamobile Sweden',
    467278 => 'Lycamobile Sweden',
    467279 => 'Telenor Sverige',
    46728100 => 'Voice Integrate',
    46728101 => 'Beepsend',
    46728197 => 'Telenor Sverige',
    46728198 => 'Telavox AB',
    467282 => 'Telecom3 Networks',
    467283 => 'Tele2 Sverige',
    467284 => 'Tele2 Sverige',
    467285 => 'Tele2 Sverige',
    467286 => 'Tele2 Sverige',
    467287 => 'Tele2 Sverige',
    467288 => 'Telenor Sverige',
    467289 => 'Qall Telecom AB',
    467290 => 'Tele2 Sverige',
    467291 => 'Tele2 Sverige',
    467292 => 'Tele2 Sverige',
    467293 => 'Tele2 Sverige',
    467294 => 'Tele2 Sverige',
    467296 => 'Telenor Sverige',
    467297 => 'Telenor Sverige',
    467298 => 'Telenor Sverige',
    467299 => 'Telenor Sverige',
    46730 => 'TeliaSonera',
    467301 => 'Maingate (Sierra Wireless)',
    467310 => 'Telenor Sverige',
    467311 => 'TeliaSonera',
    4673120 => 'Telavox AB',
    46731214 => 'Voice Integrate',
    46731215 => 'COOLTEL APS',
    46731216 => 'HORISEN AG',
    46731219 => 'CLX Networks AB',
    4673122 => 'EU Tel AB',
    4673123 => '42 Telecom AB',
    46731245 => 'EU Tel AB',
    46731247 => 'Beepsend',
    46731248 => 'TELNESS AB',
    4673125 => 'Telenor Sverige',
    4673126 => 'Telenor Connexion',
    4673127 => 'SWEDFONENET AB',
    4673128 => 'SST Net Sverige AB',
    4673129 => 'SPIRIUS AB',
    467313 => 'iMEZ',
    467314 => 'Telenor Sverige',
    467315 => 'Telenor Sverige',
    467316 => 'Alltele Sverige',
    46731706 => 'Soatso AB',
    4673171 => 'Ventelo Sverige',
    46731721 => 'REWICOM SCANDINAVIA',
    46731723 => 'REWICOM SCANDINAVIA',
    46731724 => 'REWICOM SCANDINAVIA',
    46731725 => 'REWICOM SCANDINAVIA',
    46731726 => 'REWICOM SCANDINAVIA',
    46731727 => 'Beepsend',
    46731728 => 'Beepsend',
    46731729 => 'IPIFY LIMITED',
    4673173 => 'Svea Billing System',
    4673174 => 'Svea Billing System',
    4673175 => 'Svea Billing System',
    4673176 => 'ID Mobile',
    4673177 => 'SST Net Sverige AB',
    4673178 => 'SST Net Sverige AB',
    4673179 => 'SST Net Sverige AB',
    467318 => 'ACN Communications Sweden',
    467319 => 'TeliaSonera',
    467320 => 'Telenor Sverige',
    467321 => 'Tele2 Sverige',
    467322 => 'Tele2 Sverige',
    467323 => 'Telenor Sverige',
    467324 => 'Telenor Sverige',
    467325 => 'Telenor Sverige',
    467326 => 'Telenor Sverige',
    467327 => 'Ventelo Sverige',
    467328 => 'Telenor Sverige',
    46733 => 'Telenor Sverige',
    467340 => 'Telenor Sverige',
    467341 => 'Telenor Sverige',
    467342 => 'Telenor Sverige',
    467343 => 'Telenor Sverige',
    467344 => 'Telenor Sverige',
    4673450 => 'Weelia Enterprise A',
    4673451 => 'CELLIP AB',
    46734520 => 'Soatso AB',
    46734521 => 'Soatso AB',
    46734522 => 'Soatso AB',
    46734523 => 'Soatso AB',
    46734524 => 'Soatso AB',
    46734525 => 'Soatso AB',
    46734527 => 'Soatso AB',
    46734528 => 'Soatso AB',
    46734529 => 'Soatso AB',
    4673453 => 'TeliaSonera',
    4673454 => 'Tele2 Sverige',
    4673455 => 'Viatel Sweden',
    4673456 => 'Svea Billing System',
    4673457 => 'Telenor Sverige',
    4673458 => 'Telenor Sverige',
    4673459 => '42 Telecom AB',
    467346 => 'Telenor Sverige',
    4673460 => 'Ventelo Sverige',
    46734600 => 'MERCURY INTERNATIONA',
    46734601 => 'MERCURY INTERNATIONA',
    4673461 => 'Ventelo Sverige',
    46734700 => '42 Telecom AB',
    46734702 => 'MOBIWEB LTD',
    46734703 => 'MOBIWEB LTD',
    46734704 => 'MOBIWEB LTD',
    46734705 => 'MOBIWEB LTD',
    46734706 => 'MOBIWEB LTD',
    46734707 => 'MOBIWEB LTD',
    46734708 => 'MOBIWEB LTD',
    46734709 => 'MOBIWEB LTD',
    4673471 => 'Telenor Sverige',
    4673472 => 'Telenor Sverige',
    46734731 => 'MERCURY INTERNATIONA',
    46734732 => 'MERCURY INTERNATIONA',
    46734733 => 'MERCURY INTERNATIONA',
    46734734 => 'MERCURY INTERNATIONA',
    46734735 => 'MERCURY INTERNATIONA',
    46734736 => 'MERCURY INTERNATIONA',
    46734737 => 'MERCURY INTERNATIONA',
    46734738 => 'MERCURY INTERNATIONA',
    46734739 => 'MERCURY INTERNATIONA',
    46734740 => 'Gotalandsnatet',
    46734741 => 'Soatso AB',
    46734743 => 'Soatso AB',
    46734744 => 'Soatso AB',
    46734745 => 'Beepsend',
    46734747 => 'Telavox AB',
    4673475 => 'Lycamobile Sweden',
    4673476 => 'Lycamobile Sweden',
    4673477 => 'Lycamobile Sweden',
    4673478 => 'Lycamobile Sweden',
    4673479 => 'Lycamobile Sweden',
    467348 => 'Lycamobile Sweden',
    467349 => 'Lycamobile Sweden',
    467350 => 'HI3G Access',
    467351 => 'HI3G Access',
    467352 => 'HI3G Access',
    467353 => 'HI3G Access',
    467354 => 'HI3G Access',
    467355 => 'Tele2 Sverige',
    467356 => 'Tele2 Sverige',
    467357 => 'Tele2 Sverige',
    467358 => 'Tele2 Sverige',
    467359 => 'Tele2 Sverige',
    46736 => 'Tele2 Sverige',
    46737 => 'Tele2 Sverige',
    467380 => 'TeliaSonera',
    467381 => 'TeliaSonera',
    467382 => 'TeliaSonera',
    467383 => 'TeliaSonera',
    467384 => 'TeliaSonera',
    467385 => 'Telenor Sverige',
    4673860 => 'Telenor Sverige',
    4673861 => 'Telenor Sverige',
    4673862 => 'Telenor Sverige',
    46738631 => 'Beepsend',
    46738632 => 'Beepsend',
    46738634 => 'MERCURY INTERNATIONA',
    46738635 => 'MERCURY INTERNATIONA',
    46738636 => 'MERCURY INTERNATIONA',
    46738637 => 'MERCURY INTERNATIONA',
    46738638 => 'MERCURY INTERNATIONA',
    46738639 => 'MERCURY INTERNATIONA',
    46738640 => 'EU Tel AB',
    46738641 => 'iCentrex Sweden AB',
    46738642 => '42 Telecom AB',
    46738643 => 'Beepsend',
    46738644 => 'Beepsend',
    46738645 => 'Beepsend',
    46738647 => 'EU Tel AB',
    46738651 => 'MERCURY INTERNATIONA',
    46738652 => 'MERCURY INTERNATIONA',
    46738653 => 'MERCURY INTERNATIONA',
    46738654 => 'MERCURY INTERNATIONA',
    46738655 => 'MERCURY INTERNATIONA',
    46738656 => 'MERCURY INTERNATIONA',
    46738657 => 'MERCURY INTERNATIONA',
    46738658 => 'MERCURY INTERNATIONA',
    46738659 => 'MERCURY INTERNATIONA',
    4673866 => 'Tele2 Sverige',
    4673867 => 'Tele2 Sverige',
    4673868 => 'Tele2 Sverige',
    46738691 => 'MERCURY INTERNATIONA',
    46738692 => 'MERCURY INTERNATIONA',
    46738693 => 'MERCURY INTERNATIONA',
    46738694 => 'MERCURY INTERNATIONA',
    46738695 => 'MERCURY INTERNATIONA',
    46738696 => 'MERCURY INTERNATIONA',
    46738697 => 'MERCURY INTERNATIONA',
    46738698 => 'MERCURY INTERNATIONA',
    46738699 => 'MERCURY INTERNATIONA',
    467387 => 'Tele2 Sverige',
    467388 => 'Telenor Sverige',
    467389 => 'Tele2 Sverige',
    46739 => 'Tele2 Sverige',
    467600 => 'HI3G Access',
    467601 => 'HI3G Access',
    467602 => 'HI3G Access',
    467603 => 'HI3G Access',
    467604 => 'HI3G Access',
    467605 => 'Tele2 Sverige',
    467606 => 'Tele2 Sverige',
    467607 => 'Tele2 Sverige',
    467608 => 'Tele2 Sverige',
    467609 => 'Tele2 Sverige',
    467610 => 'TeliaSonera',
    467611 => 'TeliaSonera',
    467612 => 'TeliaSonera',
    467613 => 'TeliaSonera',
    467614 => 'TeliaSonera',
    467615 => 'Lycamobile Sweden',
    467616 => 'HI3G Access',
    467617 => 'HI3G Access',
    467618 => 'HI3G Access',
    467619 => 'HI3G Access',
    46762 => 'Tele2 Sverige',
    46763 => 'HI3G Access',
    467635 => 'Telenor Sverige',
    467636 => 'Telenor Sverige',
    467637 => 'Telenor Sverige',
    467638 => 'Easy Telecom AB (BILDNINGSAGENTEN 559)',
    467640 => 'Tele2 Sverige',
    467641 => 'Tele2 Sverige',
    467642 => 'Tele2 Sverige',
    467643 => 'Lycamobile Sweden',
    467644 => 'Lycamobile Sweden',
    467645 => 'Lycamobile Sweden',
    4676460 => 'Lycamobile Sweden',
    4676461 => 'Lycamobile Sweden',
    4676462 => 'Lycamobile Sweden',
    4676463 => 'Lycamobile Sweden',
    4676464 => 'Lycamobile Sweden',
    46764651 => 'EU Tel AB',
    46764652 => 'MERCURY INTERNATIONA',
    46764653 => 'MERCURY INTERNATIONA',
    46764654 => 'MERCURY INTERNATIONA',
    46764655 => 'MERCURY INTERNATIONA',
    46764656 => 'MERCURY INTERNATIONA',
    46764657 => 'MERCURY INTERNATIONA',
    46764658 => 'MERCURY INTERNATIONA',
    46764659 => 'MERCURY INTERNATIONA',
    4676466 => 'Gotalandsnatet',
    4676467 => 'MERCURY INTERNATIONA',
    4676468 => 'MERCURY INTERNATIONA',
    4676469 => 'MERCURY INTERNATIONA',
    467647 => 'Tele2 Sverige',
    4676478 => 'WIFOG AB',
    4676479 => 'Beepsend',
    467648 => 'GLOBETOUCH AB',
    46764901 => 'MERCURY INTERNATIONA',
    46764902 => 'MERCURY INTERNATIONA',
    46764903 => 'MERCURY INTERNATIONA',
    46764904 => 'MERCURY INTERNATIONA',
    46764905 => 'MERCURY INTERNATIONA',
    46764906 => 'MERCURY INTERNATIONA',
    46764907 => 'MERCURY INTERNATIONA',
    46764908 => 'MERCURY INTERNATIONA',
    46764909 => 'MERCURY INTERNATIONA',
    4676492 => 'Telavox AB',
    46764940 => 'Tele2 Sverige',
    46764942 => 'IPIFY LIMITED',
    46764943 => 'IPIFY LIMITED',
    46764944 => 'IPIFY LIMITED',
    46764945 => 'IPIFY LIMITED',
    46764946 => 'IPIFY LIMITED',
    46764947 => 'IPIFY LIMITED',
    46764948 => 'IPIFY LIMITED',
    46764949 => 'IPIFY LIMITED',
    4676495 => 'Tele2 Sverige',
    4676496 => 'Tele2 Sverige',
    46764981 => 'MERCURY INTERNATIONA',
    46764982 => 'MERCURY INTERNATIONA',
    46764983 => 'MERCURY INTERNATIONA',
    46764984 => 'MERCURY INTERNATIONA',
    46764985 => 'MERCURY INTERNATIONA',
    46764986 => 'MERCURY INTERNATIONA',
    46764987 => 'MERCURY INTERNATIONA',
    46764988 => 'MERCURY INTERNATIONA',
    46764989 => 'MERCURY INTERNATIONA',
    46764990 => 'Gotalandsnatet',
    46764991 => 'MERCURY INTERNATIONA',
    46764992 => 'MERCURY INTERNATIONA',
    46764993 => 'MERCURY INTERNATIONA',
    46764994 => 'MERCURY INTERNATIONA',
    46764995 => 'MERCURY INTERNATIONA',
    46764996 => 'MERCURY INTERNATIONA',
    46764997 => 'MERCURY INTERNATIONA',
    46764998 => 'MERCURY INTERNATIONA',
    46765 => 'Tele2 Sverige',
    467660 => 'Telenor Sverige',
    467661 => 'Telenor Sverige',
    467662 => 'Telenor Sverige',
    467663 => 'Telenor Sverige',
    467664 => 'Telenor Sverige',
    467665 => 'Tele2 Sverige',
    4676660 => 'NETETT SVERIGE AB (AINMT Sverige)',
    4676661 => 'NETETT SVERIGE AB (AINMT Sverige)',
    4676662 => 'NETETT SVERIGE AB (AINMT Sverige)',
    4676663 => 'NETETT SVERIGE AB (AINMT Sverige)',
    4676664 => 'NETETT SVERIGE AB (AINMT Sverige)',
    4676665 => 'NETETT SVERIGE AB (AINMT Sverige)',
    4676666 => 'ÖRETEL AB',
    4676667 => 'Unicorn Telecom',
    4676668 => 'MERCURY INTERNATIONA',
    46766696 => 'Telavox AB',
    46766697 => 'Telavox AB',
    46766698 => 'Telavox AB',
    4676670 => 'Svea Billing System',
    4676671 => 'Svea Billing System',
    4676672 => 'Svea Billing System',
    4676673 => 'Svea Billing System',
    4676674 => 'Svea Billing System',
    46766750 => '42 Telecom AB',
    46766753 => 'Beepsend',
    46766754 => 'Beepsend',
    46766760 => 'Voice Integrate',
    4676677 => 'Telavox AB',
    4676678 => 'SWEDFONENET AB',
    46766791 => 'Beepsend',
    46766798 => 'Beepsend',
    46766799 => '42 Telecom AB',
    467668 => 'Tele2 Sverige',
    46766901 => 'MERCURY INTERNATIONA',
    46766902 => 'MERCURY INTERNATIONA',
    46766903 => 'MERCURY INTERNATIONA',
    46766904 => 'MERCURY INTERNATIONA',
    46766905 => 'MERCURY INTERNATIONA',
    46766906 => 'MERCURY INTERNATIONA',
    46766907 => 'MERCURY INTERNATIONA',
    46766908 => 'MERCURY INTERNATIONA',
    46766909 => 'MERCURY INTERNATIONA',
    46766911 => 'MERCURY INTERNATIONA',
    46766912 => 'MERCURY INTERNATIONA',
    46766913 => 'MERCURY INTERNATIONA',
    46766914 => 'MERCURY INTERNATIONA',
    46766915 => 'MERCURY INTERNATIONA',
    46766916 => 'MERCURY INTERNATIONA',
    46766917 => 'MERCURY INTERNATIONA',
    46766918 => 'MERCURY INTERNATIONA',
    46766919 => 'MERCURY INTERNATIONA',
    4676692 => 'Voxbone',
    46766930 => 'MERCURY INTERNATIONA',
    46766931 => 'Beepsend',
    46766932 => 'IPIFY LIMITED',
    46766933 => 'Connectel AB',
    46766934 => 'IPIFY LIMITED',
    46766935 => 'Beepsend',
    46766936 => 'IPIFY LIMITED',
    46766937 => 'IPIFY LIMITED',
    46766938 => 'IPIFY LIMITED',
    4676694 => '42 Telecom AB',
    4676695 => 'Tele2 Sverige',
    4676696 => 'Tele2 Sverige',
    4676697 => 'Tele2 Sverige',
    4676698 => 'Tele2 Sverige',
    4676699 => 'Tele2 Sverige',
    467670 => 'Tele2 Sverige',
    467671 => 'Tele2 Sverige',
    4676720 => 'Tele2 Sverige',
    4676721 => 'Tele2 Sverige',
    4676722 => 'Tele2 Sverige',
    4676723 => 'Tele2 Sverige',
    4676724 => 'Tele2 Sverige',
    4676725 => 'Tele2 Sverige',
    46767260 => 'EU Tel AB',
    46767261 => 'Beepsend',
    46767262 => 'Beepsend',
    46767265 => 'HORISEN AG',
    46767266 => 'Beepsend',
    46767268 => 'Rebtel Networks',
    4676727 => 'Telenor Sverige',
    467674 => 'Lycamobile Sweden',
    467675 => 'Lycamobile Sweden',
    467676 => 'TeliaSonera',
    467677 => 'TeliaSonera',
    467678 => 'TeliaSonera',
    467679 => 'TeliaSonera',
    467680 => 'TeliaSonera',
    467681 => 'TeliaSonera',
    467682 => 'TeliaSonera',
    467683 => 'TeliaSonera',
    467684 => 'TeliaSonera',
    467685 => 'Telenor Sverige',
    467686 => 'Telenor Sverige',
    467687 => 'Telenor Sverige',
    467688 => 'Telenor Sverige',
    467689 => 'Telenor Sverige',
    467690 => 'Tele2 Sverige',
    467691 => 'Tele2 Sverige',
    467692 => 'Tele2 Sverige',
    467693 => 'Tele2 Sverige',
    467694 => 'Tele2 Sverige',
    467695 => 'Lycamobile Sweden',
    467696 => 'Lycamobile Sweden',
    467697 => 'Lycamobile Sweden',
    467698 => 'TeliaSonera',
    467699 => 'TeliaSonera',
    4679000 => '0700 LTD',
    4679001 => 'EU Tel AB',
    4679002 => '0700 LTD',
    4679003 => '0700 LTD',
    4679004 => '0700 LTD',
    46790050 => 'Telenor Sverige',
    46790051 => 'Telenor Sverige',
    46790052 => 'Telenor Sverige',
    46790053 => 'Telenor Sverige',
    46790054 => 'Telenor Sverige',
    46790055 => 'Telenor Sverige',
    46790056 => 'Telenor Sverige',
    46790057 => 'Telenor Sverige',
    4679006 => 'Telavox AB',
    4679007 => 'FONIA AB',
    4679008 => 'Voice Integrate',
    4679009 => 'BIZTELCO SVERIGE AB',
    467901 => 'Tele2 Sverige',
    467902 => 'Tele2 Sverige',
    467903 => 'Tele2 Sverige',
    467904 => 'Tele2 Sverige',
    467905 => 'Tele2 Sverige',
    467906 => 'Tele2 Sverige',
    467907 => 'Tele2 Sverige',
    467908 => 'Tele2 Sverige',
    467909 => 'Tele2 Sverige',
    467910 => 'TELL ESS AB',
    467930 => 'HI3G Access',
    467931 => 'HI3G Access',
    467932 => 'HI3G Access',
    467933 => 'HI3G Access',
    467934 => 'HI3G Access',
    467950 => 'JUNYVERSE AB',
    467951 => 'JUNYVERSE AB',
    467952 => 'JUNYVERSE AB',
    467953 => 'JUNYVERSE AB',
    467954 => 'JUNYVERSE AB',
    4679551 => 'Tele2 Sverige',
    4679552 => 'Tele2 Sverige',
    4679553 => 'Tele2 Sverige',
    4679554 => 'Tele2 Sverige',
    4679580 => 'Borderlight',
    4679581 => 'Borderlight',
    4679585 => 'Telavox AB',
    467997 => 'Telenor Sverige',
];
