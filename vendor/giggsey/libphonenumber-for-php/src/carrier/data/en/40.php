<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    4060 => 'Telekom',
    4062 => 'Vodafone',
    4063 => 'Digi Mobil',
    407000 => 'Enigma-System',
    407013 => 'Lycamobile',
    407014 => 'Lycamobile',
    407015 => 'Lycamobile',
    407016 => 'Lycamobile',
    407017 => 'Lycamobile',
    407018 => 'Lycamobile',
    407019 => 'Lycamobile',
    40702 => 'Lycamobile',
    40705 => 'Iristel',
    40711 => 'Orange',
    40712 => 'Orange',
    40713 => 'Orange',
    4072 => 'Vodafone',
    4073 => 'Vodafone',
    4074 => 'Orange',
    4075 => 'Orange',
    4076 => 'Telekom',
    40770 => 'Digi Mobil',
    40771 => 'Digi Mobil',
    40772 => 'Digi Mobil',
    40773 => 'Digi Mobil',
    40774 => 'Digi Mobil',
    40775 => 'Digi Mobil',
    40776 => 'Digi Mobil',
    40777 => 'Digi Mobil',
    40780 => 'Telekom',
    40783 => 'Orange',
    40784 => 'Telekom',
    40785 => 'Telekom',
    40786 => 'Telekom',
    40787 => 'Orange',
    40788 => 'Telekom',
    4079 => 'Vodafone',
];
