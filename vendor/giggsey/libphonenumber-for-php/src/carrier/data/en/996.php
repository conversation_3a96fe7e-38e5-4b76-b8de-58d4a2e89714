<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    99620 => 'Aktel',
    99622 => 'Sky mobile',
    99631258 => 'Sky mobile',
    996312973 => 'Nur Telecom',
    99650 => 'Nur Telecom',
    996506 => 'Winline',
    99651 => 'Katel',
    99654 => 'Aktel',
    99655 => 'ALFA Telecom',
    99656 => 'Winline',
    99657 => 'Sotel',
    996600 => 'Sky mobile',
    99670 => 'Nur Telecom',
    99675 => 'ALFA Telecom',
    99677 => 'Sky mobile',
    99688 => 'ALFA Telecom',
    99691 => 'Smart Connect',
    996990 => 'ALFA Telecom',
    996995 => 'ALFA Telecom',
    996996 => 'Sky mobile',
    996997 => 'ALFA Telecom',
    996998 => 'ALFA Telecom',
    996999 => 'ALFA Telecom',
];
