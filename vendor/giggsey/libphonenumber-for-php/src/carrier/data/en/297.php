<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    29729 => 'Digicel',
    29756 => 'SETAR',
    29759 => 'SETAR',
    29760 => 'SETAR',
    29762 => 'MIO Wireless',
    29763 => 'MIO Wireless',
    29764 => 'Digicel',
    29766 => 'SETAR',
    297690 => 'SETAR',
    297699 => 'SETAR',
    29773 => 'Digicel',
    29774 => 'Digicel',
    29777 => 'SETAR',
    297995 => 'SETAR',
];
