/*! Copyright 2025 SweetCode. All rights reserved. */(()=>{var t={28:()=>{jQuery(document).on("pmw:load-pixels",(()=>{!wpmDataLayer?.pixels?.facebook?.pixel_id||wpmDataLayer?.pixels?.facebook?.loaded||wpm.doesUrlContainPatterns(wpmDataLayer?.pixels?.facebook?.exclusion_patterns)||wpm.consent.canPixelBeFired("marketing","Facebook")&&wpm.loadFacebookPixel()})),jQuery(document).on("pmw:s2s:page-view",((e,t)=>{try{if(wpm.canNotFireFbq())return;fbq("track","PageView",t.facebook.custom_data,{eventID:t.facebook.event_id})}catch(e){console.error(e)}})),jQuery(document).on("pmw:s2s:add-to-cart",((e,t)=>{try{if(wpm.canNotFireFbq())return;fbq("track","AddToCart",t.facebook.custom_data,{eventID:t.facebook.event_id}),pmw.console.log("Facebook Pixel: AddToCart event sent",t.facebook)}catch(e){console.error(e)}})),jQuery(document).on("pmw:s2s:begin-checkout",((e,t)=>{try{if(wpm.canNotFireFbq())return;fbq("track","InitiateCheckout",t.facebook.custom_data,{eventID:t.facebook.event_id}),pmw.console.log("Facebook Pixel: InitiateCheckout event sent",t.facebook)}catch(e){console.error(e)}})),jQuery(document).on("pmw:s2s:add-payment-info",((e,t)=>{try{if(wpm.canNotFireFbq())return;const e="AddPaymentInfo";fbq("track",e,t.facebook.custom_data,{eventID:t.facebook.event_id}),pmw.console.log(`Facebook Pixel: ${e} event sent`,t.facebook)}catch(e){console.error(e)}})),jQuery(document).on("pmw:s2s:add-to-wishlist",((e,t)=>{try{if(wpm.canNotFireFbq())return;fbq("track","AddToWishlist",t.facebook.custom_data,{eventID:t.facebook.event_id}),pmw.console.log("Facebook Pixel: AddToWishlist event sent",t.facebook)}catch(e){console.error(e)}})),jQuery(document).on("pmw:s2s:view-item",((e,t)=>{try{if(wpm.canNotFireFbq())return;fbq("track","ViewContent",t.facebook.custom_data,{eventID:t.facebook.event_id}),pmw.console.log("Facebook Pixel: ViewContent event sent",t.facebook)}catch(e){console.error(e)}})),jQuery(document).on("pmw:s2s:search",((e,t)=>{try{if(wpm.canNotFireFbq())return;fbq("track","Search",t.facebook.custom_data,{eventID:t.facebook.event_id}),pmw.console.log("Facebook Pixel: Search event sent",t.facebook)}catch(e){console.error(e)}})),jQuery(document).on("pmw:load-always",(()=>{try{if(wpm.canNotFireFbq())return;wpm.setFbUserData()}catch(e){console.error(e)}})),jQuery(document).on("pmw:s2s:view-order-received-page",((e,t)=>{try{if(wpm.canNotFireFbq())return;fbq("track","Purchase",t.facebook.custom_data,{eventID:t.facebook.event_id}),pmw.console.log("Facebook Pixel: Purchase event sent",t.facebook)}catch(e){console.error(e)}})),document.addEventListener("pmw:consent:update",(({detail:e})=>{const t=e;try{if(!wpmDataLayer?.pixels?.facebook?.loaded)return;const e=t.marketing?"grant":"revoke";fbq("consent",e),pmw.console.log(`Facebook Consent Mode updated: ${e}`)}catch(e){console.error(e)}}),!0)},62:(e,t,a)=>{a(352),a(729),a(931)},153:(t,a,r)=>{"use strict";r.r(a),r.d(a,{canPixelBeFired:()=>S,categories:()=>A,cmpConsentClickObserver:()=>x,explicitConsentModeActive:()=>D,load:()=>h,logSuppressedPixel:()=>E,settings:()=>v});const o={getConsent:()=>{let e=wpm.getCookie("cmplz_statistics")||wpm.getCookie("cmplz_rt_statistics"),t=wpm.getCookie("cmplz_marketing")||wpm.getCookie("cmplz_rt_marketing"),a=wpm.getCookie("cmplz_preferences")||wpm.getCookie("cmplz_rt_preferences"),r=wpm.getCookie("cmplz_functional")||wpm.getCookie("cmplz_rt_functional");return wpm.getCookie("cmplz_consent_status")||wpm.getCookie("cmplz_banner-status")||wpm.getCookie("cmplz_rt_banner-status")?(pmw.console.log("Complianz CMP consent detected"),{statistics:""===e||"allow"===e,marketing:""===t||"allow"===t,preferences:""===a||"allow"===a,necessary:""===r||"allow"===r}):null},loadEventListeners:()=>{document.addEventListener("cmplz_fire_categories",n,!0)}},n=e=>{let t={statistics:e.detail.categories.includes("statistics"),marketing:e.detail.categories.includes("marketing"),preferences:e.detail.categories.includes("preferences"),necessary:e.detail.categories.includes("functional")};pmw.consent.api.updateSelectively(t)},i={getConsent:()=>{let e=wpm.getCookie("CookieConsent");return e?(pmw.console.log("Cookiebot CMP consent detected"),s(e)||(e=decodeURI(e),e=e.replace(/'/g,'"'),e=decodeURIComponent(e),e=e.replace(/(\w+):/g,'"$1":')),e=JSON.parse(e),{statistics:e.statistics??!0,marketing:e.marketing??!0,preferences:e.preferences??!0,necessary:e.necessary??!0}):null},loadEventListeners:()=>{window.addEventListener("CookiebotOnAccept",(e=>{let t={statistics:e.currentTarget.CookieConsent.consent.statistics??!0,marketing:e.currentTarget.CookieConsent.consent.marketing??!0,preferences:e.currentTarget.CookieConsent.consent.preferences??!0,necessary:e.currentTarget.CookieConsent.consent.necessary??!0};pmw.consent.api.updateSelectively(t)}),!0)}},s=e=>{try{JSON.parse(e)}catch(e){return!1}return!0},c={getConsent:()=>{let e=wpm.getCookie("cookiefirst-consent");return e?(pmw.console.log("Cookiefirst CMP consent detected"),d(e)||(e=decodeURI(e),e=e.replace(/'/g,'"'),e=decodeURIComponent(e),e=e.replace(/(\w+):/g,'"$1":')),e=JSON.parse(e),console.log("cookiefirst consent object",e),{statistics:e.performance??!0,marketing:e.advertising??!0,preferences:e.functional??!0,necessary:e.necessary??!0}):null},loadEventListeners:()=>{window.addEventListener("cf_consent",(function(e){let t={statistics:consent.performance??!0,marketing:consent.advertising??!0,preferences:consent.functional??!0,necessary:consent.necessary??!0};pmw.consent.api.updateSelectively(t)}),!0)}},d=e=>{try{JSON.parse(e)}catch(e){return!1}return!0},p={getConsent:()=>{let e=wpm.getCookie("cookieyes-consent");if(e)return pmw.console.log("CookieYes CMP consent detected"),e=l(e),{statistics:e.analytics??!0,marketing:e.advertisement??!0,preferences:e.functional??!0,necessary:e.necessary??!0};const t=e=>{for(let t of e){let e=wpm.getCookie(t);if("yes"===e)return!0;if("no"===e)return!1}return!0},a={statistics:["cookielawinfo-checkbox-analytics","cookielawinfo-checkbox-analytiques","cookieyes-analytics"],marketing:["cookielawinfo-checkbox-advertisement","cookielawinfo-checkbox-performance","cookielawinfo-checkbox-publicite","cookieyes-advertisement"],preferences:["cookielawinfo-checkbox-functional","cookielawinfo-checkbox-preferences","cookieyes-functional"],necessary:["cookielawinfo-checkbox-necessary","cookielawinfo-checkbox-necessaire","cookieyes-necessary"]};return(e=>{for(let t in e)for(let a of e[t])if(wpm.getCookie(a))return!0;return!1})(a)?(pmw.console.log("CookieYes CMP consent detected"),{statistics:t(a.statistics),marketing:t(a.marketing),preferences:t(a.preferences),necessary:t(a.necessary)}):null},loadEventListeners:()=>{document.addEventListener("cookieyes_consent_update",(e=>{let t={statistics:e.detail.accepted.includes("analytics"),marketing:e.detail.accepted.includes("advertisement"),preferences:e.detail.accepted.includes("functional"),necessary:e.detail.accepted.includes("necessary")};pmw.consent.api.updateSelectively(t)}),!0)}},l=e=>{e=e.split(",");let t={};e.forEach((e=>{let[a,r]=e.split(":");t[a]=r})),e=t;for(let t in e)"yes"===e[t]?e[t]=!0:"no"!==e[t]&&""!==e[t]||(e[t]=!1);return e},m={getConsent:()=>{let e=wpm.getCookie("OptanonConsent");return e?(pmw.console.log("OneTrust CMP consent detected"),e=u(e),{statistics:e[2]??!0,marketing:e[4]??!0,preferences:e[3]??!0,necessary:e[1]??!0}):null},loadEventListeners:()=>{document.addEventListener("consent.onetrust",(e=>{let t={statistics:e.detail.includes("2"),marketing:e.detail.includes("4"),preferences:e.detail.includes("3"),necessary:e.detail.includes("1")};pmw.consent.api.updateSelectively(t)}),!0)}},u=e=>{let t=(e=decodeURIComponent(e)).split("&").reduce(((e,t)=>{let[a,r]=t.split("=");return e[a]=r,e}),{});return t.groups=t.groups.split(",").reduce(((e,t)=>{let[a,r]=t.split(":");return e[a]="1"===r,e}),{}),t.groups},g={getConsent:()=>{if(!w())return null;if(wpm.waitForLibrary("consentApi"),!window.consentApi)return null;pmw.console.log("Real Cookie Banner CMP consent detected");let e={statistics:void 0,marketing:void 0,preferences:!0,necessary:!0};return f.forEach((t=>{t.cookies.forEach((a=>{let r=window.consentApi.consentSync("http",a,"*");if(r?.cookie&&r?.cookieOptIn)switch(t.type){case"statistics":e.statistics=!0;break;case"marketing":e.marketing=!0;break;case"preferences":e.preferences=!0;break;case"necessary":e.necessary=!0}}))})),e},loadEventListeners:()=>{document.addEventListener("RCB/OptIn/All",(e=>{}),!0)}},w=()=>{let e=document.cookie.split(";");for(let t=0;t<e.length;t++){if(e[t].trim().startsWith("real_cookie_banner"))return!0}return!1},f=[{service:"adroll-ads",type:"marketing",cookies:["__adroll_fpc","_ar_v4","_adroll"]},{service:"bing-ads",type:"marketing",cookies:["_uetsid","_uetvid"]},{service:"facebook-ads",type:"marketing",cookies:["_fbp"]},{service:"google-analytics",type:"statistics",cookies:["_ga","_gid","_gat","_gat_gtag_UA_*"]},{service:"google-optimize",type:"statistics",cookies:["_ga","_gid","_gat","_gat_gtag_UA_*"]},{service:"google-ads",type:"marketing",cookies:["_gcl_au","_gcl_aw","_gcl_dc","_gac_*"]},{service:"hotjar",type:"statistics",cookies:["_hj*","_hjid"]},{service:"linkedin-ads",type:"marketing",cookies:["_li_ss","_li_id","_li_mk_*"]},{service:"microsoft-ads",type:"marketing",cookies:["_uetsid","_uetvid"]},{service:"outbrain-ads",type:"marketing",cookies:[]},{service:"pinterest-ads",type:"marketing",cookies:["_pinterest_ct_ua","_pinterest_ct_rt","_pin_unauth","_derived_epik","_pinterest_sess"]},{service:"reddit-ads",type:"marketing",cookies:["_rdt_uuid"]},{service:"snapchat-ads",type:"marketing",cookies:["sc_at","sc_anonymous_id","sc_id","_scid","_scid_r"]},{service:"taboola-ads",type:"marketing",cookies:[]},{service:"tiktok-ads",type:"marketing",cookies:["_ttp","_ttclid","ttwid"]},{service:"twitter-ads",type:"marketing",cookies:["twitter_ads_id","twid","_twclid","muc_ads"]}],y=[{getConsent:()=>{let e=wpm.getCookie("borlabs-cookie");return e?(pmw.console.log("Borlabs Cookie CMP consent detected"),console.log("Pixel Manger: We deprecated direct support for Borlabs Cookie. Borlabs Cookie still can pass consent to PMW by using Google Consent Mode update calls which will be processd by the Pixel Manager."),e=decodeURI(e),e=JSON.parse(e),{statistics:e?.consents?.statistics??!0,marketing:e?.consents?.marketing??!0,preferences:!0,necessary:!0}):null},loadEventListeners:()=>{document.addEventListener("borlabs-cookie-consent-saved",(()=>{}),!0)}},o,{getConsent:()=>{let e=wpm.getCookie("cookie_notice_accepted");return e?(pmw.console.log("Cookie Compliance CMP (by hu-manity.co) consent detected"),e="true"===e,{statistics:e,marketing:e,preferences:e,necessary:!0}):(e=wpm.getCookie("hu-consent"),e?(pmw.console.log("Cookie Compliance CMP (by hu-manity.co) consent detected"),e=JSON.parse(e),{statistics:e.categories[3]??!0,marketing:e.categories[4]??!0,preferences:e.categories[2]??!0,necessary:e.categories[1]??!0}):null)},loadEventListeners:()=>{document.addEventListener("set-consent.hu",(e=>{if(!e.detail.categories)return;let t={statistics:e.detail.categories[3],marketing:e.detail.categories[4],preferences:e.detail.categories[2],necessary:e.detail.categories[1]};pmw.consent.api.updateSelectively(t)}),!0)}},{getConsent:()=>{let e=wpm.getCookie("CookieScriptConsent");return e?(pmw.console.log("Cookie Script CMP consent detected"),e=JSON.parse(e),"string"==typeof e.categories&&(e.categories=JSON.parse(e.categories)),e.action&&"reject"===e.action?{statistics:!1,marketing:!1,preferences:!1,necessary:!0}:e.categories&&e.categories.length>0?{statistics:e.categories.indexOf("performance")>=0,marketing:e.categories.indexOf("targeting")>=0,preferences:e.categories.indexOf("functionality")>=0,necessary:!0}:{statistics:!0,marketing:!0,preferences:!0,necessary:!0}):null},loadEventListeners:()=>{document.addEventListener("CookieScriptAccept",(e=>{let t={statistics:e.detail.categories.includes("performance"),marketing:e.detail.categories.includes("targeting"),preferences:e.detail.categories.includes("functionality"),necessary:!0};pmw.consent.api.updateSelectively(t)}),!0),document.addEventListener("CookieScriptAcceptAll",(()=>{pmw.consent.api.acceptAll()}),!0),document.addEventListener("CookieScriptReject",(()=>{pmw.consent.api.revokeAll()}),!0)}},i,c,p,{getConsent:()=>{let e=new RegExp("_iub_cs-\\d{8,}"),t=wpm.getCookieThatContainsRegex(e);return t?(pmw.console.log("Iubenda CMP consent detected"),t=decodeURIComponent(t),t=t.replace(/_iub_cs-.*=/,""),t=JSON.parse(t),{statistics:t.purposes[4]??!0,marketing:t.purposes[5]??!0,preferences:t.purposes[2]??!0,necessary:t.purposes[1]??!0}):null},loadEventListeners:()=>{window._iub&&wpm.consent.cmpConsentClickObserver({ids:["iubFooterBtn"],classes:["iubenda-cs-reject-btn","iubenda-cs-accept-btn"]})}},{getConsent:()=>{let e=wpm.getCookie("moove_gdpr_popup");return e?(pmw.console.log("GDPR Cookie Compliance CMP (by Moove Agency) consent detected"),e=JSON.parse(e),{statistics:"0"!==e.thirdparty,marketing:"0"!==e.advanced,preferences:!0,necessary:"0"!==e.strict}):null},loadEventListeners:()=>{if(!window.moove_frontend_gdpr_scripts)return;const e=document.querySelector(".mgbutton");e&&e.addEventListener("click",(()=>{}),!0),wpm.consent.cmpConsentClickObserver({classes:["moove-gdpr-infobar-allow-all","moove-gdpr-modal-allow-all","moove-gdpr-modal-save-settings"]})}},m,g,{getConsent:()=>{let e=localStorage.getItem("termly_gtm_template_default_consents");return!!e&&(pmw.console.log("Termly CMP consent detected"),e=JSON.parse(e),{statistics:"denied"!==e.analytics_storage,marketing:"denied"!==e.ad_storage,preferences:"denied"!==e.functionality_storage,necessary:"denied"!==e.security_storage})},loadEventListeners:()=>{document.addEventListener("termlyConsent",(function(e){const t=["analytics","advertising","performance"];t.includes("analytics")&&t.includes("advertising")&&t.includes("performance")?pmw.consent.api.acceptAll():t.includes("analytics")||t.includes("advertising")||t.includes("performance")?pmw.consent.api.updateSelectively({statistics:t.includes("analytics"),marketing:t.includes("advertising"),preferences:t.includes("performance"),necessary:t.includes("essential")}):pmw.consent.api.revokeAll()}),!0)}},{getConsent:()=>{let e=window.localStorage.getItem("ucData");return e?(pmw.console.log("Usercentrics CMP consent detected"),e=JSON.parse(e).gcm,{statistics:"denied"!==e.analyticsStorage,marketing:"denied"!==e.adStorage,preferences:!0,necessary:!0}):null},loadEventListeners:()=>{document.addEventListener("UC_UI_CMP_EVENT",(t=>{if("ACCEPT_ALL"===t.detail.type&&pmw.consent.api.acceptAll(),"DENY_ALL"===t.detail.type&&pmw.consent.api.revokeAll(),"SAVE"===t.detail.type){let t=JSON.parse(e.currentTarget.localStorage.ucData).gcm;t&&pmw.consent.api.updateSelectively({statistics:"granted"===t.analyticsStorage,marketing:"granted"===t.adStorage,preferences:!0,necessary:!0})}}),!0)}},{getConsent:()=>{let e=wpm.getCookie("wpautoterms-cookies-notice");return e&&"1"===e?(pmw.console.log("WP AutoTerms CMP consent detected"),{statistics:!0,marketing:!0,preferences:!0,necessary:!0}):null},loadEventListeners:()=>{let e=document.querySelectorAll(".wpautoterms-notice-close")[0];e&&e.addEventListener("click",(e=>{pmw.consent.api.acceptAll()}),!0)}},{getConsent:()=>{if(wpm.getCookieThatContainsRegex(/^wp_consent_/))return pmw.console.log("WP Consent API CMP consent detected"),{statistics:"deny"!==wpm.getCookie("wp_consent_statistics"),marketing:"deny"!==wpm.getCookie("wp_consent_marketing"),preferences:"deny"!==wpm.getCookie("wp_consent_preferences"),necessary:"deny"!==wpm.getCookie("wp_consent_functional")}},loadEventListeners:()=>{document.addEventListener("wp_listen_for_consent_change",(e=>{const t=e.detail;let a={statistics:"deny"!==t.statistics,marketing:"deny"!==t.marketing,preferences:"deny"!==t.preferences,necessary:"deny"!==t.functional};pmw.consent.api.updateSelectively(a)}),!0)}},{getConsent:()=>{let e=wpm.getCookie("wpl_user_preference");return e?(pmw.console.log("WP Cookie Consent CMP (by wpeka.com) consent detected"),e=JSON.parse(e),{statistics:"no"!==e.analytics,marketing:"no"!==e.marketing,preferences:"no"!==e.preferences,necessary:"no"!==e.necessary}):null},loadEventListeners:()=>{["cookie_action_accept","cookie_action_reject"].forEach((e=>{const t=document.getElementById(e);t&&t.addEventListener("click",(()=>{}),!0)}))}}];let v={categories:{statistics:!0,marketing:!0,preferences:!0,necessary:!0},visitorHasChosen:!1,get:(e=null)=>{if(null===e)return v;let t={};for(let a of e)t[a]=v[a];return t},set:e=>{for(let t in e)v[t]=e[t]}};const h=async()=>{k(),b(),_(),await P(),j()},_=()=>{let e=L();e&&(wpm.consent.categories.set(e),wpm.consent.settings.set({visitorHasChosen:!0}))},k=()=>{for(let e of y)"function"==typeof e.loadEventListeners&&e.loadEventListeners()},b=()=>{let e=!wpm.consent.explicitConsentModeActive();wpm.consent.categories.set({statistics:e,marketing:e,preferences:e,necessary:!0})},D=()=>wpmDataLayer?.general?.consent_management?.explicit_consent;wpm.getConsentValues=()=>wpm.consent.settings.get();const A={set:({statistics:e=wpm.consent.settings.get().statistics,marketing:t=wpm.consent.settings.get().marketing,preferences:a=wpm.consent.settings.get().preferences,necessary:r=wpm.consent.settings.get().necessary})=>{v.categories.statistics=e,v.categories.marketing=t,v.categories.preferences=a,v.categories.necessary=r},get:(e=null)=>{if(null===e)return v.categories;let t={};for(let a of e)t[a]=v.categories[a];return t}},L=()=>{const e="pmw_cookie_consent";let t=wpm.retrieveData(e,!0)||wpm.getCookie(e);if(t)return t="object"==typeof t?t:JSON.parse(t),t=C(t),t;for(let e of y){if("function"!=typeof e.getConsent)continue;let t=e.getConsent();if(t)return t}return null},C=e=>e.hasOwnProperty("analytics")||e.hasOwnProperty("ads")?(e.hasOwnProperty("analytics")&&(e.statistics=e.analytics,delete e.analytics),e.hasOwnProperty("ads")&&(e.marketing=e.ads,delete e.ads),e.preferences=!0,e.necessary=!0,e):e,P=async()=>{if(!D())return;if(wpm.consent.settings.get().visitorHasChosen)return;if(!wpmDataLayer?.general?.consent_management?.restricted_regions)return;const e=await wpm.getBrowserGeo();if(I(e))return pmw.console.log("The country and/or region is restricted. Set the consent values to false and block the pixels.",e),void wpm.consent.categories.set({statistics:!1,marketing:!1,preferences:!1,necessary:!0});pmw.console.log("The country and/or region is not restricted. Set the consent values to true and fire the pixels.",e),wpm.consent.categories.set({statistics:!0,marketing:!0,preferences:!0,necessary:!0})},I=e=>null===e||(!(!e.countryCode||!wpmDataLayer?.general?.consent_management?.restricted_regions.includes(e.countryCode))||!(!e.regionCode||!wpmDataLayer?.general?.consent_management?.restricted_regions.includes(e.regionCode))),S=(e,t)=>wpm.consent.categories.get()[e]?(pmw.console.log(`The category ${e} has been approved. Loading the ${t} pixel.`),!0):(E(e,t),!1),E=(e,t)=>{let a=D()?"explicit":"implicit";console.log(`Pixel Manager: The pixel has not loaded because you have not given consent for it yet. - mode: ${a}, category: ${e}, pixel: ${t}`)},j=()=>{document.dispatchEvent(new Event("pmw_cookie_consent_management_loaded")),document.dispatchEvent(new Event("pmwCookieConsentManagementLoaded")),pmw.console.log("Loaded consent category settings: ",wpm.consent.categories.get())},x=({ids:e=[],classes:t=[]})=>{t=t.map((e=>e.startsWith(".")?e:"."+e));new MutationObserver(((a,r)=>{for(let o of a)if(o.addedNodes.length){if(t.length>0){document.querySelectorAll(t.join(", ")).forEach((e=>{e.addEventListener("click",(()=>{location.reload()})),r.disconnect()}))}e.length>0&&e.forEach((e=>{const t=document.getElementById(e);t&&(t.addEventListener("click",(()=>{location.reload()})),r.disconnect())}))}})).observe(document.body,{childList:!0,subtree:!0})}},155:(e,t,a)=>{a(211),a(728)},160:function(e,t,a){"use strict";var r=this&&this.__spreadArrays||function(){for(var e=0,t=0,a=arguments.length;t<a;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<a;t++)for(var n=arguments[t],i=0,s=n.length;i<s;i++,o++)r[o]=n[i];return r};Object.defineProperty(t,"__esModule",{value:!0}),t.isSpecial=t.isReserved=t.isIPv4MappedAddress=t.isLocalhost=t.isPrivate=t.createChecker=t.isInSubnet=t.extractMappedIpv4=void 0;var o=a(609),n=a(503),i=/\./,s=/^(.+:ffff:)(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(?:%.+)?$/,c=/:/,d=/::/;function p(e){if(!o.isIPv6(e))throw new Error("not a valid IPv6 address: "+e);if(i.test(e))return p(function(e){var t=e.match(s);if(!t||!o.isIPv4(t[2]))throw new Error("not a mapped IPv4 address: "+e);var a=t[1],r=t[2].split(i).map((function(e){return parseInt(e,10)}));return""+a+((r[0]<<8)+r[1]).toString(16)+":"+((r[2]<<8)+r[3]).toString(16)}(e));var t=e.split(d),a=t[0],r=t[1],n=a&&a.split(c)||[],l=r&&r.split(c)||[],m=new Array(8-(n.length+l.length));return n.concat(m,l)}function l(e){if(Array.isArray(e)){var t=e.map((function(e){return m(e)}));return function(e){var a=p(e);return t.some((function(e){return e(a)}))}}var a=m(e);return function(e){var t=p(e);return a(t)}}function m(e){var t=e.split("/"),a=t[0],r=t[1],o=parseInt(r,10);if(!a||!Number.isInteger(o))throw new Error("not a valid IPv6 CIDR subnet: "+e);if(o<0||o>128)throw new Error("not a valid IPv6 prefix length: "+o+" (from "+e+")");var n=p(a);return function(e){for(var t=0;t<8;++t){var a=Math.min(o-16*t,16);if(a<=0)break;if((n[t]&&parseInt(n[t],16)||0)>>16-a!==(e[t]&&parseInt(e[t],16)||0)>>16-a)return!1}return!0}}t.extractMappedIpv4=function(e){var t=e.match(s);if(!t||!o.isIPv4(t[2]))throw new Error("not a mapped IPv4 address: "+e);return t[2]},t.isInSubnet=function(e,t){return l(t)(e)},t.createChecker=l;var u={};t.isPrivate=function(e){return"private"in u==!1&&(u.private=l(n.default.private.ipv6)),u.private(e)},t.isLocalhost=function(e){return"localhost"in u==!1&&(u.localhost=l(n.default.localhost.ipv6)),u.localhost(e)},t.isIPv4MappedAddress=function(e){if("mapped"in u==!1&&(u.mapped=l("::ffff:0:0/96")),u.mapped(e)){var t=e.match(s);return Boolean(t&&o.isIPv4(t[2]))}return!1},t.isReserved=function(e){return"reserved"in u==!1&&(u.reserved=l(n.default.reserved.ipv6)),u.reserved(e)},t.isSpecial=function(e){return"special"in u==!1&&(u.special=l(r(n.default.private.ipv6,n.default.localhost.ipv6,n.default.reserved.ipv6))),u.special(e)}},179:(e,t,a)=>{"use strict";a.r(t),a.d(t,{getLocationByTimeZone:()=>r});const r=()=>{const e=Intl.DateTimeFormat().resolvedOptions().timeZone;if(""===e||!e)return null;let t={countryCode:{"Africa/Abidjan":["CI","BF","GH","GM","GN","ML","MR","SH","SL","SN","TG"],"Africa/Accra":["GH"],"Africa/Addis_Ababa":["ET"],"Africa/Algiers":["DZ"],"Africa/Asmara":["ER"],"Africa/Asmera":["ER"],"Africa/Bamako":["ML"],"Africa/Bangui":["CF"],"Africa/Banjul":["GM"],"Africa/Bissau":["GW"],"Africa/Blantyre":["MW"],"Africa/Brazzaville":["CG"],"Africa/Bujumbura":["BI"],"Africa/Cairo":["EG"],"Africa/Casablanca":["MA"],"Africa/Ceuta":["ES"],"Africa/Conakry":["GN"],"Africa/Dakar":["SN"],"Africa/Dar_es_Salaam":["TZ"],"Africa/Djibouti":["DJ"],"Africa/Douala":["CM"],"Africa/El_Aaiun":["EH"],"Africa/Freetown":["SL"],"Africa/Gaborone":["BW"],"Africa/Harare":["ZW"],"Africa/Johannesburg":["ZA","LS","SZ"],"Africa/Juba":["SS"],"Africa/Kampala":["UG"],"Africa/Khartoum":["SD"],"Africa/Kigali":["RW"],"Africa/Kinshasa":["CD"],"Africa/Lagos":["NG","AO","BJ","CD","CF","CG","CM","GA","GQ","NE"],"Africa/Libreville":["GA"],"Africa/Lome":["TG"],"Africa/Luanda":["AO"],"Africa/Lubumbashi":["CD"],"Africa/Lusaka":["ZM"],"Africa/Malabo":["GQ"],"Africa/Maputo":["MZ","BI","BW","CD","MW","RW","ZM","ZW"],"Africa/Maseru":["LS"],"Africa/Mbabane":["SZ"],"Africa/Mogadishu":["SO"],"Africa/Monrovia":["LR"],"Africa/Nairobi":["KE","DJ","ER","ET","KM","MG","SO","TZ","UG","YT"],"Africa/Ndjamena":["TD"],"Africa/Niamey":["NE"],"Africa/Nouakchott":["MR"],"Africa/Ouagadougou":["BF"],"Africa/Porto-Novo":["BJ"],"Africa/Sao_Tome":["ST"],"Africa/Timbuktu":["ML"],"Africa/Tripoli":["LY"],"Africa/Tunis":["TN"],"Africa/Windhoek":["NA"],"America/Adak":["US"],"America/Anchorage":["US"],"America/Anguilla":["AI"],"America/Antigua":["AG"],"America/Araguaina":["BR"],"America/Argentina/Buenos_Aires":["AR"],"America/Argentina/Catamarca":["AR"],"America/Argentina/Cordoba":["AR"],"America/Argentina/Jujuy":["AR"],"America/Argentina/La_Rioja":["AR"],"America/Argentina/Mendoza":["AR"],"America/Argentina/Rio_Gallegos":["AR"],"America/Argentina/Salta":["AR"],"America/Argentina/San_Juan":["AR"],"America/Argentina/San_Luis":["AR"],"America/Argentina/Tucuman":["AR"],"America/Argentina/Ushuaia":["AR"],"America/Aruba":["AW"],"America/Asuncion":["PY"],"America/Atikokan":["CA"],"America/Bahia":["BR"],"America/Bahia_Banderas":["MX"],"America/Barbados":["BB"],"America/Belem":["BR"],"America/Belize":["BZ"],"America/Blanc-Sablon":["CA"],"America/Boa_Vista":["BR"],"America/Bogota":["CO"],"America/Boise":["US"],"America/Cambridge_Bay":["CA"],"America/Campo_Grande":["BR"],"America/Cancun":["MX"],"America/Caracas":["VE"],"America/Cayenne":["GF"],"America/Cayman":["KY"],"America/Chicago":["US"],"America/Chihuahua":["MX"],"America/Coral_Harbour":["CA"],"America/Costa_Rica":["CR"],"America/Creston":["CA"],"America/Cuiaba":["BR"],"America/Curacao":["CW"],"America/Danmarkshavn":["GL"],"America/Dawson":["CA"],"America/Dawson_Creek":["CA"],"America/Denver":["US"],"America/Detroit":["US"],"America/Dominica":["DM"],"America/Edmonton":["CA"],"America/Eirunepe":["BR"],"America/El_Salvador":["SV"],"America/Fort_Nelson":["CA"],"America/Fortaleza":["BR"],"America/Glace_Bay":["CA"],"America/Goose_Bay":["CA"],"America/Grand_Turk":["TC"],"America/Grenada":["GD"],"America/Guadeloupe":["GP"],"America/Guatemala":["GT"],"America/Guayaquil":["EC"],"America/Guyana":["GY"],"America/Halifax":["CA"],"America/Havana":["CU"],"America/Hermosillo":["MX"],"America/Indiana/Indianapolis":["US"],"America/Indiana/Knox":["US"],"America/Indiana/Marengo":["US"],"America/Indiana/Petersburg":["US"],"America/Indiana/Tell_City":["US"],"America/Indiana/Vevay":["US"],"America/Indiana/Vincennes":["US"],"America/Indiana/Winamac":["US"],"America/Inuvik":["CA"],"America/Iqaluit":["CA"],"America/Jamaica":["JM"],"America/Juneau":["US"],"America/Kentucky/Louisville":["US"],"America/Kentucky/Monticello":["US"],"America/Kralendijk":["BQ"],"America/La_Paz":["BO"],"America/Lima":["PE"],"America/Los_Angeles":["US"],"America/Lower_Princes":["SX"],"America/Maceio":["BR"],"America/Managua":["NI"],"America/Manaus":["BR"],"America/Marigot":["MF"],"America/Martinique":["MQ"],"America/Matamoros":["MX"],"America/Mazatlan":["MX"],"America/Menominee":["US"],"America/Merida":["MX"],"America/Metlakatla":["US"],"America/Mexico_City":["MX"],"America/Miquelon":["PM"],"America/Moncton":["CA"],"America/Monterrey":["MX"],"America/Montevideo":["UY"],"America/Montreal":["CA"],"America/Montserrat":["MS"],"America/Nassau":["BS"],"America/New_York":["US"],"America/Nipigon":["CA"],"America/Nome":["US"],"America/Noronha":["BR"],"America/North_Dakota/Beulah":["US"],"America/North_Dakota/Center":["US"],"America/North_Dakota/New_Salem":["US"],"America/Nuuk":["GL"],"America/Ojinaga":["MX"],"America/Panama":["PA","CA","KY"],"America/Pangnirtung":["CA"],"America/Paramaribo":["SR"],"America/Phoenix":["US","CA"],"America/Port-au-Prince":["HT"],"America/Port_of_Spain":["TT"],"America/Porto_Velho":["BR"],"America/Puerto_Rico":["PR","AG","CA","AI","AW","BL","BQ","CW","DM","GD","GP","KN","LC","MF","MS","SX","TT","VC","VG","VI"],"America/Punta_Arenas":["CL"],"America/Rainy_River":["CA"],"America/Rankin_Inlet":["CA"],"America/Recife":["BR"],"America/Regina":["CA"],"America/Resolute":["CA"],"America/Rio_Branco":["BR"],"America/Santarem":["BR"],"America/Santiago":["CL"],"America/Santo_Domingo":["DO"],"America/Sao_Paulo":["BR"],"America/Scoresbysund":["GL"],"America/Sitka":["US"],"America/St_Barthelemy":["BL"],"America/St_Johns":["CA"],"America/St_Kitts":["KN"],"America/St_Lucia":["LC"],"America/St_Thomas":["VI"],"America/St_Vincent":["VC"],"America/Swift_Current":["CA"],"America/Tegucigalpa":["HN"],"America/Thule":["GL"],"America/Thunder_Bay":["CA"],"America/Tijuana":["MX"],"America/Toronto":["CA","BS"],"America/Tortola":["VG"],"America/Vancouver":["CA"],"America/Virgin":["VI"],"America/Whitehorse":["CA"],"America/Winnipeg":["CA"],"America/Yakutat":["US"],"America/Yellowknife":["CA"],"Antarctica/Casey":["AQ"],"Antarctica/Davis":["AQ"],"Antarctica/DumontDUrville":["AQ"],"Antarctica/Macquarie":["AU"],"Antarctica/Mawson":["AQ"],"Antarctica/McMurdo":["AQ"],"Antarctica/Palmer":["AQ"],"Antarctica/Rothera":["AQ"],"Antarctica/South_Pole":["AQ"],"Antarctica/Syowa":["AQ"],"Antarctica/Troll":["AQ"],"Antarctica/Vostok":["AQ"],"Arctic/Longyearbyen":["SJ"],"Asia/Aden":["YE"],"Asia/Almaty":["KZ"],"Asia/Amman":["JO"],"Asia/Anadyr":["RU"],"Asia/Aqtau":["KZ"],"Asia/Aqtobe":["KZ"],"Asia/Ashgabat":["TM"],"Asia/Atyrau":["KZ"],"Asia/Baghdad":["IQ"],"Asia/Bahrain":["BH"],"Asia/Baku":["AZ"],"Asia/Bangkok":["TH","KH","LA","VN"],"Asia/Barnaul":["RU"],"Asia/Beirut":["LB"],"Asia/Bishkek":["KG"],"Asia/Brunei":["BN"],"Asia/Chita":["RU"],"Asia/Choibalsan":["MN"],"Asia/Colombo":["LK"],"Asia/Damascus":["SY"],"Asia/Dhaka":["BD"],"Asia/Dili":["TL"],"Asia/Dubai":["AE","OM"],"Asia/Dushanbe":["TJ"],"Asia/Famagusta":["CY"],"Asia/Gaza":["PS"],"Asia/Hebron":["PS"],"Asia/Ho_Chi_Minh":["VN"],"Asia/Hong_Kong":["HK"],"Asia/Hovd":["MN"],"Asia/Irkutsk":["RU"],"Asia/Jakarta":["ID"],"Asia/Jayapura":["ID"],"Asia/Jerusalem":["IL"],"Asia/Kabul":["AF"],"Asia/Kamchatka":["RU"],"Asia/Karachi":["PK"],"Asia/Kathmandu":["NP"],"Asia/Khandyga":["RU"],"Asia/Kolkata":["IN"],"Asia/Krasnoyarsk":["RU"],"Asia/Kuala_Lumpur":["MY"],"Asia/Kuching":["MY"],"Asia/Kuwait":["KW"],"Asia/Macau":["MO"],"Asia/Magadan":["RU"],"Asia/Makassar":["ID"],"Asia/Manila":["PH"],"Asia/Muscat":["OM"],"Asia/Nicosia":["CY"],"Asia/Novokuznetsk":["RU"],"Asia/Novosibirsk":["RU"],"Asia/Omsk":["RU"],"Asia/Oral":["KZ"],"Asia/Phnom_Penh":["KH"],"Asia/Pontianak":["ID"],"Asia/Pyongyang":["KP"],"Asia/Qatar":["QA","BH"],"Asia/Qostanay":["KZ"],"Asia/Qyzylorda":["KZ"],"Asia/Riyadh":["SA","AQ","KW","YE"],"Asia/Sakhalin":["RU"],"Asia/Samarkand":["UZ"],"Asia/Seoul":["KR"],"Asia/Shanghai":["CN"],"Asia/Singapore":["SG","MY"],"Asia/Srednekolymsk":["RU"],"Asia/Taipei":["TW"],"Asia/Tashkent":["UZ"],"Asia/Tbilisi":["GE"],"Asia/Tehran":["IR"],"Asia/Thimphu":["BT"],"Asia/Tokyo":["JP"],"Asia/Tomsk":["RU"],"Asia/Ulaanbaatar":["MN"],"Asia/Urumqi":["CN"],"Asia/Ust-Nera":["RU"],"Asia/Vientiane":["LA"],"Asia/Vladivostok":["RU"],"Asia/Yakutsk":["RU"],"Asia/Yangon":["MM"],"Asia/Yekaterinburg":["RU"],"Asia/Yerevan":["AM"],"Atlantic/Azores":["PT"],"Atlantic/Bermuda":["BM"],"Atlantic/Canary":["ES"],"Atlantic/Cape_Verde":["CV"],"Atlantic/Faroe":["FO"],"Atlantic/Jan_Mayen":["SJ"],"Atlantic/Madeira":["PT"],"Atlantic/Reykjavik":["IS"],"Atlantic/South_Georgia":["GS"],"Atlantic/St_Helena":["SH"],"Atlantic/Stanley":["FK"],"Australia/Adelaide":["AU"],"Australia/Brisbane":["AU"],"Australia/Broken_Hill":["AU"],"Australia/Darwin":["AU"],"Australia/Eucla":["AU"],"Australia/Hobart":["AU"],"Australia/Lindeman":["AU"],"Australia/Lord_Howe":["AU"],"Australia/Melbourne":["AU"],"Australia/Perth":["AU"],"Australia/Sydney":["AU"],"Canada/Eastern":["CA"],"Europe/Amsterdam":["NL"],"Europe/Andorra":["AD"],"Europe/Astrakhan":["RU"],"Europe/Athens":["GR"],"Europe/Belfast":["GB"],"Europe/Belgrade":["RS","BA","HR","ME","MK","SI"],"Europe/Berlin":["DE"],"Europe/Bratislava":["SK"],"Europe/Brussels":["BE"],"Europe/Bucharest":["RO"],"Europe/Budapest":["HU"],"Europe/Busingen":["DE"],"Europe/Chisinau":["MD"],"Europe/Copenhagen":["DK"],"Europe/Dublin":["IE"],"Europe/Gibraltar":["GI"],"Europe/Guernsey":["GG"],"Europe/Helsinki":["FI","AX"],"Europe/Isle_of_Man":["IM"],"Europe/Istanbul":["TR"],"Europe/Jersey":["JE"],"Europe/Kaliningrad":["RU"],"Europe/Kiev":["UA"],"Europe/Kirov":["RU"],"Europe/Lisbon":["PT"],"Europe/Ljubljana":["SI"],"Europe/London":["GB","GG","IM","JE"],"Europe/Luxembourg":["LU"],"Europe/Madrid":["ES"],"Europe/Malta":["MT"],"Europe/Mariehamn":["AX"],"Europe/Minsk":["BY"],"Europe/Monaco":["MC"],"Europe/Moscow":["RU"],"Europe/Oslo":["NO","SJ","BV"],"Europe/Paris":["FR"],"Europe/Podgorica":["ME"],"Europe/Prague":["CZ","SK"],"Europe/Riga":["LV"],"Europe/Rome":["IT","SM","VA"],"Europe/Samara":["RU"],"Europe/San_Marino":["SM"],"Europe/Sarajevo":["BA"],"Europe/Saratov":["RU"],"Europe/Simferopol":["RU","UA"],"Europe/Skopje":["MK"],"Europe/Sofia":["BG"],"Europe/Stockholm":["SE"],"Europe/Tallinn":["EE"],"Europe/Tirane":["AL"],"Europe/Ulyanovsk":["RU"],"Europe/Uzhgorod":["UA"],"Europe/Vaduz":["LI"],"Europe/Vatican":["VA"],"Europe/Vienna":["AT"],"Europe/Vilnius":["LT"],"Europe/Volgograd":["RU"],"Europe/Warsaw":["PL"],"Europe/Zagreb":["HR"],"Europe/Zaporozhye":["UA"],"Europe/Zurich":["CH","DE","LI"],GB:["GB"],"GB-Eire":["GB"],"Indian/Antananarivo":["MG"],"Indian/Chagos":["IO"],"Indian/Christmas":["CX"],"Indian/Cocos":["CC"],"Indian/Comoro":["KM"],"Indian/Kerguelen":["TF","HM"],"Indian/Mahe":["SC"],"Indian/Maldives":["MV"],"Indian/Mauritius":["MU"],"Indian/Mayotte":["YT"],"Indian/Reunion":["RE","TF"],NZ:["NZ"],"Pacific/Apia":["WS"],"Pacific/Auckland":["NZ","AQ"],"Pacific/Bougainville":["PG"],"Pacific/Chatham":["NZ"],"Pacific/Chuuk":["FM"],"Pacific/Easter":["CL"],"Pacific/Efate":["VU"],"Pacific/Fakaofo":["TK"],"Pacific/Fiji":["FJ"],"Pacific/Funafuti":["TV"],"Pacific/Galapagos":["EC"],"Pacific/Gambier":["PF"],"Pacific/Guadalcanal":["SB"],"Pacific/Guam":["GU","MP"],"Pacific/Honolulu":["US","UM"],"Pacific/Johnston":["UM"],"Pacific/Kanton":["KI"],"Pacific/Kiritimati":["KI"],"Pacific/Kosrae":["FM"],"Pacific/Kwajalein":["MH"],"Pacific/Majuro":["MH"],"Pacific/Marquesas":["PF"],"Pacific/Midway":["UM"],"Pacific/Nauru":["NR"],"Pacific/Niue":["NU"],"Pacific/Norfolk":["NF"],"Pacific/Noumea":["NC"],"Pacific/Pago_Pago":["AS","UM"],"Pacific/Palau":["PW"],"Pacific/Pitcairn":["PN"],"Pacific/Pohnpei":["FM"],"Pacific/Port_Moresby":["PG","AQ"],"Pacific/Rarotonga":["CK"],"Pacific/Saipan":["MP"],"Pacific/Samoa":["WS"],"Pacific/Tahiti":["PF"],"Pacific/Tarawa":["KI"],"Pacific/Tongatapu":["TO"],"Pacific/Wake":["UM"],"Pacific/Wallis":["WF"],Singapore:["SG"],"US/Arizona":["US"],"US/Hawaii":["US"],"US/Samoa":["WS"]}[e][0].toUpperCase()};return"America/Los_Angeles"===e&&(t.regionCode="US-CA"),"Europe/Zurich"===e&&(t.regionCode="CH-ZH"),t}},181:(e,t,a)=>{"use strict";a.r(t),a.d(t,{error:()=>o,log:()=>r});const r=(e,...t)=>{n()&&("object"==typeof e?console.log("Pixel Manager: ",e,...t):t.length?console.log("Pixel Manager: "+e,...t):console.log("Pixel Manager: "+e))},o=(e,...t)=>{"object"==typeof e?console.error("Pixel Manager: ",e,...t):t.length?console.error("Pixel Manager: "+e,...t):console.error("Pixel Manager: "+e)},n=()=>!i()&&(!!s()||(!!c()||!!wpmDataLayer?.general?.logger?.is_active)),i=()=>(wpm.urlHasParameter("pmwloggeroff")&&wpm.storeData("loggerEnabled",!1),wpm.urlHasParameter("pmwloggeroff")),s=()=>wpm.retrieveData("loggerEnabled"),c=()=>(wpm.urlHasParameter("pmwloggeron")&&wpm.storeData("loggerEnabled",!0),wpm.urlHasParameter("pmwloggeron"))},189:(e,t,a)=>{a(783),window.wpm.consent=a(153),window.pmw={consent:{api:a(857)},console:a(181)},a(616)},196:()=>{const e=e=>e=(e=e.replace(/<[^>]*>?/gm,"").replace(/\n/gm,"").replace(/\t/gm,"")).trim();jQuery("form.woocommerce-checkout").on("change","#shipping_method",(a=>{a.target?.value&&a.target?.id&&t({slug:a.target.value,text:e(jQuery("label[for='"+a.target.id+"']").text())})})),jQuery((()=>{if(jQuery("#shipping_method").length){let a=jQuery("#shipping_method").find(":checked");if(a.length){if(!a.val())return;if(!a.attr("id"))return;t({slug:a.val(),text:e(jQuery("label[for='"+a.attr("id")+"']").text())})}}}));const t=e=>{jQuery(document).trigger("pmw:add-shipping-info",{shippingTier:e})};let a=!1;jQuery("form.woocommerce-checkout").on("change",'input[name="payment_method"]',(t=>{t.target.value&&t.target.id&&(r({slug:t.target.value,text:e(jQuery("label[for='"+t.target.id+"']").text())}),a=!0)})),jQuery((()=>{if(a)return;let t=jQuery('input[name="payment_method"]:checked');if(t.length){if(!t.val())return;if(!t.attr("id"))return;r({slug:t.val(),text:e(jQuery("label[for='"+t.attr("id")+"']").text())})}}));const r=e=>{jQuery(document).trigger("pmw:add-payment-info",{paymentType:e})};jQuery(document).on("click",".remove_from_cart_button, .remove",(e=>{try{let t=new URL(jQuery(e.currentTarget).attr("href")),a=wpm.getProductIdByCartItemKeyUrl(t);wpm.removeProductFromCart(a)}catch(e){console.error(e)}}));const o=wpm.prepareSelectors([".checkout-button",".cart-checkout-button",".button.checkout",".xoo-wsc-ft-btn-checkout",".elementor-button--checkout",".xt_woofc-checkout",".fkcart-checkout--text"],"beginCheckout");jQuery(document).on("click init_checkout",o,(()=>{jQuery(document).trigger("pmw:begin-checkout")})),jQuery(document).on("updated_cart_totals",(()=>{jQuery(document).trigger("pmw:view-cart")})),jQuery(document).on("wpmLoad",(e=>{jQuery(document).on("payment_method_selected",(()=>{!1===wpm.paymentMethodSelected&&wpm.fireCheckoutProgress(3),wpm.fireCheckoutOption(3,jQuery("input[name='payment_method']:checked").val()),wpm.paymentMethodSelected=!0}))})),jQuery(document).on("wpmLoad",(()=>{try{wpm.initCart()}catch(e){console.error(e)}})),jQuery(document).on("wpmLoad",(()=>{wpmDataLayer.products=wpmDataLayer.products||{};let e=wpm.getAddToCartLinkProductIds();wpm.getProductsFromBackend(e)})),jQuery(document).on("wpmLoad",(()=>{if(!document.referrer)return;if(wpm.retrieveData("referrer"))return;let e=new URL(document.referrer).hostname;e!==window.location.host&&wpm.storeData("referrer",e)})),jQuery(document).on("wpmLoad",(()=>{let e=wpm.getUrlParameter("gclid");e&&wpm.storeData("gclid",e);let t=wpm.getUrlParameter("wbraid");t&&wpm.storeData("wbraid",t);let a=wpm.getUrlParameter("gbraid");a&&wpm.storeData("gbraid",a);let r=wpm.getUrlParameter("fbclid");r&&(r="fb.1."+Math.floor(Date.now()/1e3)+"."+r,wpm.storeData("fbclid",r));let o=wpm.getUrlParameter("ttclid");o&&wpm.storeData("ttclid",o);let n=wpm.getUrlParameter("ScCid");n&&wpm.storeData("scid",n);let i=wpm.getUrlParameter("epik");i&&wpm.storeData("epik",i)})),jQuery(document).on("wpmLoad",(()=>{try{if("undefined"!=typeof wpmDataLayer&&!wpmDataLayer?.pmw_loaded){if(jQuery(document).trigger("pmw:load-always"),wpmDataLayer?.shop)if("product"===wpmDataLayer.shop.page_type&&"variable"!==wpmDataLayer.shop.product_type&&wpm.getMainProductIdFromProductPage()){let e=wpm.getProductDataForViewItemEvent(wpm.getMainProductIdFromProductPage());jQuery(document).trigger("pmw:view-item",e)}else"product_category"===wpmDataLayer.shop.page_type?jQuery(document).trigger("pmw:view-category"):"search"===wpmDataLayer.shop.page_type?jQuery(document).trigger("pmw:search"):"cart"===wpmDataLayer.shop.page_type?jQuery(document).trigger("pmw:view-cart"):"order_received_page"===wpmDataLayer.shop.page_type?wpmDataLayer?.order&&!wpm.isOrderIdStored(wpmDataLayer.order.id)&&(jQuery(document).trigger("pmw:view-order-received-page"),wpm.writeOrderIdToStorage(wpmDataLayer.order.id,wpmDataLayer.order.key),"function"==typeof wpm.acrRemoveCookie&&wpm.acrRemoveCookie()):jQuery(document).trigger("pmw:everywhere-else");else jQuery(document).trigger("pmw:everywhere-else");wpmDataLayer?.user?.id&&!wpm.hasLoginEventFired()&&(jQuery(document).trigger("pmw:login"),wpm.setLoginEventFired()),wpmDataLayer.pmw_loaded=!0}}catch(e){console.error(e)}})),wpmDataLayer?.general?.lazy_load_pmw&&wpm.registerShowVariationEventListener(),jQuery(document).on("wpmLoad",(()=>{if(wpmDataLayer?.general?.lazy_load_pmw)try{const e=jQuery(".variations_form");if(!e.length)return;const t={};if(!e.find("select").toArray().every((e=>{const a=jQuery(e).val();return!!a&&(t[jQuery(e).attr("name")]=a,!0)})))return;const a=e.data("product_variations");if(!a)return;const r=a.find((e=>Object.entries(t).every((([t,a])=>e.attributes[t]===a))));r&&wpm.triggerViewItemEventPrep(r.variation_id)}catch(e){pmw.console.error(e)}})),jQuery(document).on("wpmLoad",(async()=>{!1===wpm.retrieveData("restEndpointAvailable")&&pmw.console.error("REST endpoint is not available. Using admin-ajax.php instead.")})),jQuery(document).on("pmw:page-view",(e=>{pmw.console.log("pmw:page-view event fired",{event:e});let t={event:"page_view"};wpmDataLayer?.pixels?.facebook?.loaded&&(t.facebook={event_name:"PageView",event_id:wpm.getFbRandomEventId(),user_data:wpm.getFbUserData(),event_source_url:window.location.href},wpm.retrieveData("referrer")&&(t.facebook.referrer_url=wpm.makeFullUrl(wpm.retrieveData("referrer")))),wpmDataLayer?.pixels?.snapchat?.loaded&&(t.snapchat=wpm.getSnapchatS2SBaseData(),t.snapchat.event_name="PAGE_VIEW"),jQuery(document).trigger("pmw:s2s:page-view",t),wpmDataLayer?.general?.server_2_server?.pageview_event_s2s?.is_active&&wpm.sendEventPayloadToServer(t)})),jQuery(document).on("pmw:add-to-cart",((e,t)=>{pmw.console.log("pmw:add-to-cart event fired",{event:e,product:t});let a={event:"add_to_cart",product:t};wpmDataLayer?.pixels?.facebook?.loaded&&(a.facebook={event_name:"AddToCart",event_id:wpm.getFbRandomEventId(),user_data:wpm.getFbUserData(),event_source_url:window.location.href,custom_data:wpm.fbGetProductDataForCapiEvent(t)},wpm.retrieveData("referrer")&&(a.facebook.referrer_url=wpm.makeFullUrl(wpm.retrieveData("referrer")))),wpmDataLayer?.pixels?.pinterest?.loaded&&(a.pinterest=wpm.getPinterestS2SBaseData(),a.pinterest.event_name="addtocart",a.pinterest.custom_data=wpm.pinterestGetProductDataForCapiEvent(t)),wpmDataLayer?.pixels?.tiktok?.loaded&&(a.tiktok={event:"AddToCart",event_id:wpm.getRandomEventId(),user:wpm.tiktokGetEventUserData(),page:wpm.tiktokGetEventPageData(),properties:{value:t.price*t.quantity,currency:t.currency,content_type:"product",contents:[{content_id:wpm.getProductIdForSpecificPixel(t,"tiktok"),content_name:t.name,quantity:t.quantity,price:t.price}]}}),wpmDataLayer?.pixels?.snapchat?.loaded&&(a.snapchat=wpm.getSnapchatS2SBaseData(),a.snapchat.event_name="ADD_CART",a.snapchat.custom_data=wpm.getSnapchatCustomDataForProduct(t)),jQuery(document).trigger("pmw:s2s:add-to-cart",a),wpm.sendEventPayloadToServer(a)})),jQuery(document).on("pmw:begin-checkout",(e=>{pmw.console.log("pmw:begin-checkout event fired",{event:e});let t={event:"begin_checkout"};wpmDataLayer?.pixels?.facebook?.loaded&&(t.facebook={event_name:"InitiateCheckout",event_id:wpm.getFbRandomEventId(),user_data:wpm.getFbUserData(),event_source_url:window.location.href,custom_data:{}},wpm.retrieveData("referrer")&&(t.facebook.referrer_url=wpm.makeFullUrl(wpm.retrieveData("referrer"))),wpmDataLayer?.cart&&!jQuery.isEmptyObject(wpmDataLayer.cart)&&(t.facebook.custom_data={content_type:"product",content_ids:wpm.fbGetContentIdsFromCart(),value:wpm.getCartValue(),currency:wpmDataLayer.shop.currency})),wpmDataLayer?.pixels?.tiktok?.loaded&&(t.tiktok={event:"InitiateCheckout",event_id:wpm.getRandomEventId(),user:wpm.tiktokGetEventUserData(),page:wpm.tiktokGetEventPageData(),properties:wpm.tiktokGetPropertiesFromCart()}),wpmDataLayer?.pixels?.snapchat?.loaded&&(t.snapchat=wpm.getSnapchatS2SBaseData(),t.snapchat.event_name="START_CHECKOUT"),jQuery(document).trigger("pmw:s2s:begin-checkout",t),wpm.sendEventPayloadToServer(t)})),jQuery(document).on("pmw:add-payment-info",(e=>{pmw.console.log("pmw:add-payment-info event fired",{event:e});let t={event:"add_payment_info"};wpmDataLayer?.pixels?.facebook?.loaded&&(t.facebook={event_name:"AddPaymentInfo",event_id:wpm.getFbRandomEventId(),user_data:wpm.getFbUserData(),event_source_url:window.location.href,custom_data:{}},wpm.retrieveData("referrer")&&(t.facebook.referrer_url=wpm.makeFullUrl(wpm.retrieveData("referrer")))),wpmDataLayer?.pixels?.tiktok?.loaded&&(t.tiktok={event:"AddPaymentInfo",event_id:wpm.getRandomEventId(),user:wpm.tiktokGetEventUserData(),page:wpm.tiktokGetEventPageData()}),jQuery(document).trigger("pmw:s2s:add-payment-info",t),wpm.sendEventPayloadToServer(t)})),jQuery(document).on("pmw:add-to-wishlist",((e,t)=>{pmw.console.log("pmw:add-to-wishlist event fired",{event:e,product:t});let a={event:"add_to_wishlist",product:t};wpmDataLayer?.pixels?.facebook?.loaded&&(a.facebook={event_name:"AddToWishlist",event_id:wpm.getFbRandomEventId(),user_data:wpm.getFbUserData(),event_source_url:window.location.href,custom_data:wpm.fbGetProductDataForCapiEvent(t)},wpm.retrieveData("referrer")&&(a.facebook.referrer_url=wpm.makeFullUrl(wpm.retrieveData("referrer")))),wpmDataLayer?.pixels?.tiktok?.loaded&&(a.tiktok={event:"AddToWishlist",event_id:wpm.getRandomEventId(),user:wpm.tiktokGetEventUserData(),page:wpm.tiktokGetEventPageData(),properties:{value:t.price*t.quantity,currency:t.currency,content_type:"product",contents:[{content_id:wpm.getProductIdForSpecificPixel(t,"tiktok"),content_name:t.name,quantity:t.quantity,price:t.price}]}}),wpmDataLayer?.pixels?.snapchat?.loaded&&(a.snapchat=wpm.getSnapchatS2SBaseData(),a.snapchat.event_name="ADD_TO_WISHLIST",a.snapchat.custom_data=wpm.getSnapchatCustomDataForProduct(t)),jQuery(document).trigger("pmw:s2s:add-to-wishlist",a),wpm.sendEventPayloadToServer(a)})),jQuery(document).on("pmw:view-item",((e,t=null)=>{pmw.console.log("pmw:view-item event fired",{event:e,product:t});let a={event:"view_item",product:t};wpmDataLayer?.pixels?.facebook?.loaded&&(a.facebook={event_name:"ViewContent",event_id:wpm.getFbRandomEventId(),user_data:wpm.getFbUserData(),event_source_url:window.location.href,custom_data:{}},wpm.retrieveData("referrer")&&(a.facebook.referrer_url=wpm.makeFullUrl(wpm.retrieveData("referrer"))),t&&(a.facebook.custom_data=wpm.fbGetProductDataForCapiEvent(t))),wpmDataLayer?.pixels?.pinterest?.loaded&&(a.pinterest=wpm.getPinterestS2SBaseData(),a.pinterest.event_name="pagevisit",t&&(a.pinterest.custom_data=wpm.pinterestGetProductDataForCapiEvent(t))),wpmDataLayer?.pixels?.tiktok?.loaded&&(a.tiktok={event:"ViewContent",event_id:wpm.getRandomEventId(),user:wpm.tiktokGetEventUserData(),page:wpm.tiktokGetEventPageData()},t&&(a.tiktok.properties={value:t.price*t.quantity,currency:t.currency,content_type:"product",contents:[{content_id:wpm.getProductIdForSpecificPixel(t,"tiktok"),content_name:t.name,quantity:t.quantity,price:t.price}]})),wpmDataLayer?.pixels?.snapchat?.loaded&&(a.snapchat=wpm.getSnapchatS2SBaseData(),a.snapchat.event_name="VIEW_CONTENT",t&&(a.snapchat.custom_data=wpm.getSnapchatCustomDataForProduct(t))),jQuery(document).trigger("pmw:s2s:view-item",a),wpm.sendEventPayloadToServer(a)})),jQuery(document).on("pmw:view-category",((e,t=null)=>{pmw.console.log("pmw:view-category event fired",{event:e,product:t});let a={event:"view_category"};wpmDataLayer?.pixels?.pinterest?.loaded&&(a.pinterest=wpm.getPinterestS2SBaseData(),a.pinterest.event_name="viewcategory"),jQuery(document).trigger("pmw:s2s:view-category",a),wpm.sendEventPayloadToServer(a)})),jQuery(document).on("pmw:search",(e=>{pmw.console.log("pmw:search event fired",{event:e});let t={event:"search"};wpmDataLayer?.pixels?.facebook?.loaded&&(t.facebook={event_name:"Search",event_id:wpm.getFbRandomEventId(),user_data:wpm.getFbUserData(),event_source_url:window.location.href,custom_data:{search_string:wpm.getSearchTermFromUrl()}},wpm.retrieveData("referrer")&&(t.facebook.referrer_url=wpm.makeFullUrl(wpm.retrieveData("referrer")))),wpmDataLayer?.pixels?.pinterest?.loaded&&(t.pinterest=wpm.getPinterestS2SBaseData(),t.pinterest.event_name="search",t.pinterest.custom_data={search_string:wpm.getSearchTermFromUrl()}),wpmDataLayer?.pixels?.tiktok?.loaded&&(t.tiktok={event:"Search",event_id:wpm.getRandomEventId(),user:wpm.tiktokGetEventUserData(),page:wpm.tiktokGetEventPageData(),properties:{query:wpm.getSearchTermFromUrl()}}),wpmDataLayer?.pixels?.snapchat?.loaded&&(t.snapchat=wpm.getSnapchatS2SBaseData(),t.snapchat.event_name="SEARCH",t.snapchat.custom_data={search_string:wpm.getSearchTermFromUrl()}),jQuery(document).trigger("pmw:s2s:search",t),wpm.sendEventPayloadToServer(t)})),jQuery(document).on("pmw:place-order",(e=>{pmw.console.log("pmw:place-order event fired",{event:e});let t={event:"place_order"};wpmDataLayer?.pixels?.tiktok?.loaded&&(t.tiktok={event:"PlaceAnOrder",event_id:wpm.getRandomEventId(),user:wpm.tiktokGetEventUserData(),page:wpm.tiktokGetEventPageData(),properties:wpm.tiktokGetPropertiesFromCart()}),jQuery(document).trigger("pmw:s2s:place-order",t),wpm.sendEventPayloadToServer(t)})),jQuery(document).on("pmw:view-order-received-page",(e=>{pmw.console.log("pmw:view-order-received-page event fired",{event:e});let t={event:"order_received"};wpmDataLayer?.pixels?.facebook?.loaded&&(t.facebook={event_name:"Purchase",event_id:wpmDataLayer.order.id.toString(),user_data:wpm.getFbUserData(),event_source_url:window.location.href,custom_data:{content_type:"product",value:wpmDataLayer.order.value.marketing,currency:wpmDataLayer.order.currency,content_ids:wpm.facebookContentIds()}},wpm.retrieveData("referrer")&&(t.facebook.referrer_url=wpm.makeFullUrl(wpm.retrieveData("referrer")))),wpmDataLayer?.pixels?.pinterest?.loaded&&(t.pinterest=wpm.getPinterestS2SBaseData(),t.pinterest.event_name="checkout"),wpmDataLayer?.pixels?.tiktok?.loaded&&(t.tiktok={event:"CompletePayment",event_id:wpmDataLayer.order.id.toString(),user:wpm.tiktokGetEventUserData(),page:wpm.tiktokGetEventPageData(),properties:{value:wpmDataLayer.order.value.marketing,currency:wpmDataLayer.order.currency,content_type:"product",contents:wpm.getTikTokOrderItemIds()}}),jQuery(document).trigger("pmw:s2s:view-order-received-page",t)})),jQuery(document).on("pmw:login",(()=>{pmw.console.log("pmw:login event fired");let e={event:"login"};wpmDataLayer?.pixels?.snapchat?.loaded&&(e.snapchat=wpm.getSnapchatS2SBaseData(),e.snapchat.event_name="LOGIN"),jQuery(document).trigger("pmw:s2s:login",e),wpm.sendEventPayloadToServer(e)})),jQuery(document).on("pmw:ready",(async()=>{wpm.isWooCommerceActive()&&(wpm.startIntersectionObserverToWatch(),wpm.startProductsMutationObserverToWatch())})),jQuery(document).on("pmw:load-pixels",(()=>{wpm.waitForPixelsAndTriggerPageView()}))},211:()=>{!function(e){e.load_hotjar_pixel=function(){try{e=window,t=document,e.hj=e.hj||function(){(e.hj.q=e.hj.q||[]).push(arguments)},e._hjSettings={hjid:wpmDataLayer.pixels.hotjar.site_id,hjsv:6},a=t.getElementsByTagName("head")[0],(r=t.createElement("script")).async=1,r.src="https://static.hotjar.com/c/hotjar-"+e._hjSettings.hjid+".js?sv="+e._hjSettings.hjsv,a.appendChild(r),wpmDataLayer.pixels.hotjar.loaded=!0}catch(e){console.error(e)}var e,t,a,r}}(window.wpm=window.wpm||{},jQuery)},263:(e,t,a)=>{!function(e){e.jQueryExists=async()=>new Promise((e=>{!function t(){if("undefined"!=typeof jQuery)return e();setTimeout(t,100)}()})),e.waitForLibrary=(e,t=5e3,a=100)=>{const r=Date.now();for(;void 0===window[e]&&!(Date.now()-r>t);)setTimeout((()=>{}),a)},e.wpmDataLayerFullyLoaded=async()=>{let e=new Promise((e=>{!function t(){if("undefined"!=typeof wpmDataLayer&&void 0!==wpmDataLayer.version)return e();setTimeout(t,100)}()})),t=setTimeout((()=>{console.error("Pixel Manager error: The wpmDataLayer is not defined. Please make sure that the wpmDataLayer script snippet is inserted and not modified by any third-party plugin, such as a consent management platform or a JavaScript optimizer.")}),6e3);return e.then((()=>{clearTimeout(t)})),e},e.wpHooksExists=async()=>new Promise((e=>{!function t(){if("undefined"!=typeof wp&&void 0!==wp.hooks)return e();setTimeout(t,50)}()})),e.loadWcHooksFunctions=async()=>{await e.wpHooksExists(),a(489)}}(window.wpm=window.wpm||{},jQuery)},265:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.check=t.isSpecial=t.isReserved=t.isIPv4MappedAddress=t.isLocalhost=t.isPrivate=t.createChecker=t.isInSubnet=t.IPv6=t.IPv4=t.isIPv6=t.isIPv4=t.isIP=void 0;var r=a(618);t.IPv4=r;var o=a(160);t.IPv6=o;var n=a(609),i=a(609);function s(e,t){return c(t)(e)}function c(e){if(!Array.isArray(e))return c([e]);var t=e.reduce((function(e,t){var a=t.split("/")[0];return e[n.isIP(a)].push(t),e}),{0:[],4:[],6:[]});if(0!==t[0].length)throw new Error("some subnets are not valid IP addresses: "+t[0]);var a=r.createChecker(t[4]),i=o.createChecker(t[6]);return function(e){if(!n.isIP(e))throw new Error("not a valid IPv4 or IPv6 address: "+e);return n.isIPv6(e)&&o.isIPv4MappedAddress(e)?i(e)||a(o.extractMappedIpv4(e)):n.isIPv6(e)?i(e):a(e)}}Object.defineProperty(t,"isIP",{enumerable:!0,get:function(){return i.isIP}}),Object.defineProperty(t,"isIPv4",{enumerable:!0,get:function(){return i.isIPv4}}),Object.defineProperty(t,"isIPv6",{enumerable:!0,get:function(){return i.isIPv6}}),t.isInSubnet=s,t.createChecker=c,t.isPrivate=function(e){return n.isIPv6(e)?o.isIPv4MappedAddress(e)?r.isPrivate(o.extractMappedIpv4(e)):o.isPrivate(e):r.isPrivate(e)},t.isLocalhost=function(e){return n.isIPv6(e)?o.isIPv4MappedAddress(e)?r.isLocalhost(o.extractMappedIpv4(e)):o.isLocalhost(e):r.isLocalhost(e)},t.isIPv4MappedAddress=function(e){return!!n.isIPv6(e)&&o.isIPv4MappedAddress(e)},t.isReserved=function(e){return n.isIPv6(e)?o.isIPv4MappedAddress(e)?r.isReserved(o.extractMappedIpv4(e)):o.isReserved(e):r.isReserved(e)},t.isSpecial=function(e){return n.isIPv6(e)?o.isIPv4MappedAddress(e)?r.isSpecial(o.extractMappedIpv4(e)):o.isSpecial(e):r.isSpecial(e)},t.check=s},282:()=>{document.addEventListener("doofinder.cart.add",(async e=>{const{item_id:t,amount:a}=e.detail;if(wpmDataLayer.products[t]||await wpm.getProductsFromBackend([t]),!wpmDataLayer.products[t])return void console.log("Product not found in dataLayer or server. Exiting...");let r=wpm.getProductDetailsFormattedForEvent(t,a);r?jQuery(document).trigger("pmw:add-to-cart",r):pmw.console.error("Product not found")}))},346:()=>{!function(e){e.googleConfigConditionsMet=({type:t=null,log:a=!1})=>wpmDataLayer?.pixels?.google?.consent_mode?.is_active?(a&&pmw.console.log("Google Consent Mode is active. Loading the Google pixel."),!0):t?e.consent.categories.get()[t]?(a&&pmw.console.log("The category "+t+" has been approved. Loading the Google pixel."),!0):(a&&pmw.console.log("The category "+t+" has been denied. Not loading the Google pixel."),!1):!(!e.consent.categories.get().marketing&&!e.consent.categories.get().statistics)&&(a&&pmw.console.log("The categories statistics and/or marketing are approved. Loading the Google pixel."),!0),e.fireGtagGoogleAds=()=>{try{wpmDataLayer.pixels.google.ads.state="loading",wpmDataLayer?.pixels?.google?.enhanced_conversions?.is_active?Object.keys(wpmDataLayer.pixels.google.ads.conversion_ids).forEach((e=>{gtag("config",e,{allow_enhanced_conversions:!0})})):Object.keys(wpmDataLayer.pixels.google.ads.conversion_ids).forEach((e=>{gtag("config",e)})),wpmDataLayer?.pixels?.google?.ads?.conversion_ids&&wpmDataLayer?.pixels?.google?.ads?.phone_conversion_label&&wpmDataLayer?.pixels?.google?.ads?.phone_conversion_number&&gtag("config",Object.keys(wpmDataLayer.pixels.google.ads.conversion_ids)[0]+"/"+wpmDataLayer.pixels.google.ads.phone_conversion_label,{phone_conversion_number:wpmDataLayer.pixels.google.ads.phone_conversion_number}),wpmDataLayer.pixels.google.ads.state="ready"}catch(e){console.error(e)}},e.fireGtagGoogleAnalyticsGA4=()=>{try{wpmDataLayer.pixels.google.analytics.ga4.state="loading";let e=wpmDataLayer.pixels.google.analytics.ga4.parameters;wpmDataLayer?.pixels?.google?.analytics?.ga4?.debug_mode&&(e.debug_mode=!0),gtag("config",wpmDataLayer.pixels.google.analytics.ga4.measurement_id,e),wpmDataLayer.pixels.google.analytics.ga4.state="ready"}catch(e){console.error(e)}},e.isGoogleActive=()=>!!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id||!jQuery.isEmptyObject(wpmDataLayer?.pixels?.google?.ads?.conversion_ids),e.getGoogleGtagId=()=>wpmDataLayer?.pixels?.google?.tag_id?wpmDataLayer.pixels.google.tag_id:wpmDataLayer?.pixels?.google?.ads?.conversion_ids&&Object.keys(wpmDataLayer.pixels.google.ads.conversion_ids)[0]?Object.keys(wpmDataLayer.pixels.google.ads.conversion_ids)[0]:wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id?wpmDataLayer.pixels.google.analytics.ga4.measurement_id:"",getGoogleTrackingUrl=()=>wpmDataLayer?.pixels?.google?.tag_gateway?.measurement_path?wpmDataLayer.pixels.google.tag_gateway.measurement_path+"/":getGoogleStandardTrackingUrl(),getGoogleStandardTrackingUrl=()=>"https://www.googletagmanager.com/gtag/js?id="+e.getGoogleGtagId(),e.loadGoogle=()=>{e.isGoogleActive()&&(wpmDataLayer.pixels.google.state="loading",pmw.console.log("Loading Google gtag.js from",getGoogleTrackingUrl().startsWith("/")?window.location.origin+getGoogleTrackingUrl():getGoogleTrackingUrl()),e.loadScriptAndCacheIt(getGoogleTrackingUrl(),getGoogleStandardTrackingUrl()).then(((t,a)=>{try{if(wpmDataLayer?.pixels?.google?.tcf_support&&(window.gtag_enable_tcf_support=!0),window.dataLayer=window.dataLayer||[],window.gtag=function(){pmw.console.log("gtag called with",arguments),pmw.consent.api.processExternalGcmConsentUpdate_experimental(arguments),3===arguments.length&&arguments[2]?.source&&delete arguments[2].source,dataLayer.push(arguments)},wpmDataLayer?.pixels?.google?.consent_mode?.is_active){let t={ad_personalization:e.consent.categories.get().marketing?"granted":"denied",ad_storage:e.consent.categories.get().marketing?"granted":"denied",ad_user_data:e.consent.categories.get().marketing?"granted":"denied",analytics_storage:e.consent.categories.get().statistics?"granted":"denied",functionality_storage:e.consent.categories.get().preferences?"granted":"denied",personalization_storage:e.consent.categories.get().preferences?"granted":"denied",security_storage:e.consent.categories.get().necessary?"granted":"denied",wait_for_update:wpmDataLayer.pixels.google.consent_mode.wait_for_update};wpmDataLayer?.general?.consent_management?.restricted_regions&&(t.region=wpmDataLayer.general.consent_management.restricted_regions),pmw.console.log("Google Consent Mode settings",t),gtag("consent","default",t),gtag("set","ads_data_redaction",wpmDataLayer.pixels.google.consent_mode.ads_data_redaction),gtag("set","url_passthrough",wpmDataLayer.pixels.google.consent_mode.url_passthrough)}wpmDataLayer?.pixels?.google?.linker?.settings&&gtag("set","linker",wpmDataLayer.pixels.google.linker.settings),gtag("js",new Date),gtag("set","developer_id.dNDI5Yz",!0),wpmDataLayer?.shop?.page_type&&"order_received_page"===wpmDataLayer.shop.page_type&&wpmDataLayer?.order?.google?.enhanced_conversion_data&&gtag("set","user_data",wpmDataLayer.order.google.enhanced_conversion_data),jQuery.isEmptyObject(wpmDataLayer?.pixels?.google?.ads?.conversion_ids)||(e.googleConfigConditionsMet({type:"marketing"})?e.fireGtagGoogleAds():e.consent.logSuppressedPixel("marketing","Google Ads")),wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id&&(e.googleConfigConditionsMet({type:"statistics"})?e.fireGtagGoogleAnalyticsGA4():e.consent.logSuppressedPixel("statistics","GA4")),wpmDataLayer.pixels.google.state="ready"}catch(e){console.error(e)}})).catch(((e,t,a)=>{pmw.console.error("The URL "+getGoogleTrackingUrl()+" could not be loaded. Error: "+a),wpmDataLayer.pixels.google.state="error"})))},e.gtagLoaded=()=>new Promise(((e,t)=>{void 0===wpmDataLayer?.pixels?.google?.state&&t();let a=0;!function r(){return"ready"===wpmDataLayer?.pixels?.google?.state?e():a>=5e3?(pmw.console.error("Google gtag failed to load. Probably a third party script is blocking it."),t()):(a+=200,void setTimeout(r,200))}()})),e.updateGoogleConsentMode=({statistics:t=e.consent.categories.get().statistics,marketing:a=e.consent.categories.get().marketing,preferences:r=e.consent.categories.get().preferences,necessary:o=e.consent.categories.get().necessary})=>{try{if(!window.gtag)return;let e={analytics_storage:t?"granted":"denied",ad_storage:a?"granted":"denied",ad_user_data:a?"granted":"denied",ad_personalization:a?"granted":"denied",functionality_storage:r?"granted":"denied",personalization_storage:r?"granted":"denied",security_storage:o?"granted":"denied",source:"pmw"};gtag("consent","update",e),pmw.console.log("Google Consent Mode updated",e)}catch(e){console.error(e)}}}(window.wpm=window.wpm||{},jQuery)},352:(e,t,a)=>{a(346),a(465)},465:()=>{jQuery(document).on("pmw:load-pixels",(function(){void 0===wpmDataLayer?.pixels?.google?.state&&(wpm.googleConfigConditionsMet({log:!0})?wpm.loadGoogle():wpm.consent.logSuppressedPixel("statistics","Google Analytics / Google Ads"))})),document.addEventListener("pmw:consent:update",(({detail:e})=>{const t=e;wpm.updateGoogleConsentMode(t)}),!0)},489:()=>{wp.hooks.addAction("experimental__woocommerce_blocks-cart-add-item","pixel-manager-for-woocommerce",(e=>{wpm.addProductToCart(e.product.id,1)})),wp.hooks.addAction("experimental__woocommerce_blocks-cart-set-item-quantity","pixel-manager-for-woocommerce",(e=>{e.quantity>e.product.quantity&&wpm.addProductToCart(e.product.id,e.quantity-e.product.quantity),e.quantity<e.product.quantity&&wpm.removeProductFromCart(e.product.id,e.product.quantity-e.quantity)})),wp.hooks.addAction("experimental__woocommerce_blocks-cart-remove-item","pixel-manager-for-woocommerce",(e=>{wpm.removeProductFromCart(e.product.id,e.quantity)}));const e=function(e,t){let a;return function(...r){clearTimeout(a),a=setTimeout((()=>e.apply(this,r)),t)}}((e=>{e.storeCart.shippingAddress&&["first_name","last_name","address_1","city","postcode","country"].every((t=>e.storeCart.shippingAddress[t]))&&jQuery(document).trigger("pmw:add-shipping-info",e)}),2e3);wp.hooks.addAction("experimental__woocommerce_blocks-checkout-set-shipping-address","pixel-manager-for-woocommerce",(t=>{e(t)})),wp.hooks.addAction("experimental__woocommerce_blocks-checkout-set-active-payment-method","pixel-manager-for-woocommerce",(e=>{let t={paymentType:{text:e.value}};jQuery(document).trigger("pmw:add-payment-info",t)})),wp.hooks.addAction("experimental__woocommerce_blocks-checkout-submit","pixel-manager-for-woocommerce",(()=>{jQuery(document).trigger("pmw:place-order",{})}))},503:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={localhost:{ipv4:["*********/8"],ipv6:["::1/128"]},private:{ipv4:["10.0.0.0/8","**********/12","***********/16"],ipv6:["fe80::/10","fc00::/7"]},reserved:{ipv4:["0.0.0.0/8","**********/10","***********/16","*********/24","*********/24","***********/24","**********/15","************/24","***********/24","*********/4","240.0.0.0/4","***************/32"],ipv6:["::/128","64:ff9b::/96","100::/64","2001::/32","2001:10::/28","2001:20::/28","2001:db8::/32","2002::/16","ff00::/8"]}}},547:()=>{!function(e){e.getLibraryVersion=()=>"1.49.1",e.checkLibraryVersion=()=>{e.getLibraryVersion()!==wpmDataLayer?.version?.number&&console.error(`Pixel Manager: The library version ${e.getLibraryVersion()} and wpmDataLayer.version.number ${wpmDataLayer.version.number} do not match. Delete the server-side cache and try again.`)}}(window.wpm=window.wpm||{},jQuery)},606:()=>{jQuery(document).on("pmw:view-item-list",((e,t)=>{try{if(jQuery.isEmptyObject(wpmDataLayer?.pixels?.google?.ads?.conversion_ids))return;if(!wpm.googleConfigConditionsMet({type:"marketing"}))return;if(wpmDataLayer?.shop?.variations_output&&t.is_variable&&!1===wpmDataLayer.pixels.google.ads.dynamic_remarketing.send_events_with_parent_ids)return;if(!t)return;let e={send_to:wpm.getGoogleAdsConversionIdentifiers(),items:[{id:t.dyn_r_ids[wpmDataLayer.pixels.google.ads.dynamic_remarketing.id_type],google_business_vertical:wpmDataLayer.pixels.google.ads.google_business_vertical}]};wpmDataLayer?.user?.id?.raw&&(e.user_id=wpmDataLayer.user.id.raw),wpm.gtagLoaded().then((()=>{gtag("event","view_item_list",e),pmw.console.log("Google Ads: view_item_list event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:add-to-cart",((e,t)=>{try{if(jQuery.isEmptyObject(wpmDataLayer?.pixels?.google?.ads?.conversion_ids))return;if(!wpm.googleConfigConditionsMet({type:"marketing"}))return;let e={send_to:wpm.getGoogleAdsConversionIdentifiers(),value:t.quantity*t.price,items:[{id:t.dyn_r_ids[wpmDataLayer.pixels.google.ads.dynamic_remarketing.id_type],quantity:t.quantity,price:t.price,google_business_vertical:wpmDataLayer.pixels.google.ads.google_business_vertical}]};wpmDataLayer?.user?.id?.raw&&(e.user_id=wpmDataLayer.user.id.raw),wpm.gtagLoaded().then((()=>{gtag("event","add_to_cart",e),pmw.console.log("Google Ads: add_to_cart event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:view-item",((e,t=null)=>{try{if(jQuery.isEmptyObject(wpmDataLayer?.pixels?.google?.ads?.conversion_ids))return;if(!wpm.googleConfigConditionsMet({type:"marketing"}))return;let e={send_to:wpm.getGoogleAdsConversionIdentifiers()};t&&(e.value=(t.quantity?t.quantity:1)*t.price,e.items=[{id:t.dyn_r_ids[wpmDataLayer.pixels.google.ads.dynamic_remarketing.id_type],quantity:t.quantity?t.quantity:1,price:t.price,google_business_vertical:wpmDataLayer.pixels.google.ads.google_business_vertical}]),wpmDataLayer?.user?.id?.raw&&(e.user_id=wpmDataLayer.user.id.raw),wpm.gtagLoaded().then((()=>{gtag("event","view_item",e),pmw.console.log("Google Ads: view_item event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:search",(()=>{try{if(jQuery.isEmptyObject(wpmDataLayer?.pixels?.google?.ads?.conversion_ids))return;if(!wpm.googleConfigConditionsMet({type:"marketing"}))return;let e=[];Object.values(wpmDataLayer.products).forEach((t=>{wpmDataLayer?.shop?.variations_output&&t.is_variable&&!1===wpmDataLayer.pixels.google.ads.dynamic_remarketing.send_events_with_parent_ids||e.push({id:t.dyn_r_ids[wpmDataLayer.pixels.google.ads.dynamic_remarketing.id_type],google_business_vertical:wpmDataLayer.pixels.google.ads.google_business_vertical})}));let t={send_to:wpm.getGoogleAdsConversionIdentifiers(),items:e};wpmDataLayer?.user?.id?.raw&&(t.user_id=wpmDataLayer.user.id.raw),wpm.gtagLoaded().then((()=>{gtag("event","view_search_results",t),pmw.console.log("Google Ads: view_search_results event sent",t)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:login",(()=>{try{if(jQuery.isEmptyObject(wpmDataLayer?.pixels?.google?.ads?.conversion_ids))return;if(!wpm.googleConfigConditionsMet({type:"marketing"}))return;let e={send_to:wpm.getGoogleAdsConversionIdentifiers()};wpmDataLayer?.user?.id?.raw&&(e.user_id=wpmDataLayer.user.id.raw),wpm.gtagLoaded().then((()=>{gtag("event","login",e),pmw.console.log("Google Ads: login event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:view-order-received-page",(()=>{try{if(jQuery.isEmptyObject(wpm.getGoogleAdsConversionIdentifiersWithLabel()))return;if(!wpm.googleConfigConditionsMet({type:"marketing"}))return;let e={send_to:wpm.getGoogleAdsConversionIdentifiers(),value:wpmDataLayer.order.value.marketing,items:wpm.getGoogleAdsRegularOrderItems()};wpm.gtagLoaded().then((()=>{gtag("event","purchase",e),pmw.console.log("Google Ads: purchase event sent (for remarketing lists)",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:view-order-received-page",(()=>{try{if(jQuery.isEmptyObject(wpm.getGoogleAdsConversionIdentifiersWithLabel()))return;if(!wpm.googleConfigConditionsMet({type:"marketing"}))return;let e={send_to:wpm.getGoogleAdsConversionIdentifiersWithLabel(),transaction_id:wpmDataLayer.order.number,value:wpmDataLayer.order.value.marketing,currency:wpmDataLayer.order.currency,new_customer:wpmDataLayer.order.new_customer};wpmDataLayer?.order?.value?.ltv?.marketing&&(e.customer_lifetime_value=wpmDataLayer.order.value.ltv.marketing),wpmDataLayer?.user?.id?.raw&&(e.user_id=wpmDataLayer.user.id.raw),wpmDataLayer?.order?.aw_merchant_id&&(e.discount=wpmDataLayer.order.discount,e.aw_merchant_id=wpmDataLayer.order.aw_merchant_id,e.aw_feed_country=wpmDataLayer.order.aw_feed_country,e.aw_feed_language=wpmDataLayer.order.aw_feed_language,e.items=wpm.getGoogleAdsRegularOrderItems()),wpmDataLayer?.order?.google?.ads?.custom_variables&&Object.entries(wpmDataLayer.order.google.ads.custom_variables).forEach((([t,a])=>{e[t]=a})),wpm.gtagLoaded().then((()=>{gtag("event","purchase",e),pmw.console.log("Google Ads: conversion event sent",e)}))}catch(e){console.error(e)}}))},609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isIP=t.isIPv6=t.isIPv4=void 0;var a="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",r="("+a+"[.]){3}"+a,o=new RegExp("^"+r+"$"),n="(?:[0-9a-fA-F]{1,4})",i=new RegExp("^((?:"+n+":){7}(?:"+n+"|:)|(?:"+n+":){6}(?:"+r+"|:"+n+"|:)|(?:"+n+":){5}(?::"+r+"|(:"+n+"){1,2}|:)|(?:"+n+":){4}(?:(:"+n+"){0,1}:"+r+"|(:"+n+"){1,3}|:)|(?:"+n+":){3}(?:(:"+n+"){0,2}:"+r+"|(:"+n+"){1,4}|:)|(?:"+n+":){2}(?:(:"+n+"){0,3}:"+r+"|(:"+n+"){1,5}|:)|(?:"+n+":){1}(?:(:"+n+"){0,4}:"+r+"|(:"+n+"){1,6}|:)|(?::((?::"+n+"){0,5}:"+r+"|(?::"+n+"){1,7}|:)))(%[0-9a-zA-Z]{1,})?$");function s(e){return o.test(e)}function c(e){return i.test(e)}t.isIPv4=s,t.isIPv6=c,t.isIP=function(e){return s(e)?4:c(e)?6:0}},616:(e,t,a)=>{!function(e){const{isRange:t,isIP:r,isV4:o,isV6:n,version:i}=a(723),s={cloudflare:{any:"https://www.cloudflare.com/cdn-cgi/trace"},icanhazip:{4:"https://ipv4.icanhazip.com",6:"https://ipv6.icanhazip.com",any:"https://icanhazip.com"},ipinfo:{any:"https://ipinfo.io/ip"},myexternalip:{any:"https://myexternalip.com/raw"},ipify:{4:"https://api4.ipify.org",6:"https://api6.ipify.org",any:"https://api64.ipify.org"}};e.getGeoInfo={cloudflare:async()=>{const e=await fetch("https://www.cloudflare.com/cdn-cgi/trace");if(!e.ok)throw new Error(`The response from Cloudflare was not OK. Status: ${e.status}`);let t=await e.text();if(!t)throw new Error("No data was returned from Cloudflare");if(t=t.trim().split("\n").reduce(((e,t)=>(e[(t=t.split("="))[0]]=t[1],e)),{}),!t.ip||!t.loc)throw new Error("The JSON data is missing the ip or loc key from Cloudflare");if(c(t.loc))throw new Error("We have to validate the region, and this service is not returning the region.");return{ip:t.ip,countryCode:t.loc,service:"cloudflare"}},geojs:async()=>{const e=await fetch("https://get.geojs.io/v1/ip/geo.json");if(!e.ok)throw new Error(`The response from geojs was not OK. Status: ${e.status}`);let t=await e.json();if(!t)throw new Error("No JSON data was returned from geojs");if(!t.ip||!t.country_code||!t.region)throw new Error("The JSON data is missing the ip, country_code, or region key from geojs");return{ip:t.ip,countryCode:t.country_code,region:t.region,service:"geojs"}},ipinfo:async()=>{const e=await fetch("https://ipinfo.io/json");if(!e.ok)throw new Error(`The response from ipinfo was not OK from ipinfo. Status: ${e.status}`);let t=await e.json();if(!t)throw new Error("No JSON data was returned from ipinfo");if(!t.ip||!t.country||!t.region)throw new Error("The JSON data is missing the ip, country, or region key from ipinfo");return{ip:t.ip,countryCode:t.country,region:t.region,service:"ipinfo"}},ipapi:async()=>{const e=await fetch("https://ipapi.co/json");if(!e.ok)throw new Error(`The response from ipapi was not OK. Status: ${e.status}`);let t=await e.json();if(!t)throw new Error("No JSON data was returned from ipapi");if(!t.ip||!t.country||!t.region)throw new Error("The JSON data is missing the ip, country, or region key from ipapi");return{ip:t.ip,countryCode:t.country,region:t.region,service:"ipapi"}}};const c=e=>{if("US "!==e)return!1;return["US-CA","US-VA"].some((e=>wpmDataLayer?.general?.consent_management.restricted_regions.includes(e)))};e.isIpAllowed=async()=>{if(e.retrieveData("ipAllowed"))return!!JSON.parse(e.retrieveData("ipAllowed"));const t=await e.getIp();return d(t)?(e.storeData("ipAllowed",!1),!1):(e.storeData("ipAllowed",!0),!0)},e.getIp=async(t="any",a=3)=>{const r=["any","4","6"];if(!r.includes(t))return console.error("The version argument must be one of the following: "+r.join(", ")),null;if("any"===t&&e.retrieveData("ip"))return e.retrieveData("ip");const o=e.retrieveData(`ipv${t}`);if(undefined!==o)return o;pmw.console.log("Testing the IP address of the browser because one ore more server-to-server APIs are enabled and require the browser IP. It may trigger connection request errors in the console while testing the IP address. This is normal and expected behavior.");try{const r=await e.getIpFromAnyService(t,a);return"any"===t&&e.storeData("ip",r),(4===i(r)||"4"===t&&null===r)&&e.storeData("ipv4",r),(6===i(r)||"6"===t&&null===r)&&e.storeData("ipv6",r),r}catch(e){return null}},e.getBrowserGeoFromExternalService=async()=>{const t=performance.now();let a=[];Object.keys(e.getGeoInfo).forEach((t=>{a.push(e.getGeoInfo[t]())}));try{const e=await Promise.any(a);return e.countryCode=e.countryCode.toUpperCase(),e.region&&"California"===e.region&&(e.regionCode="US-CA"),e.region&&"Virginia"===e.region&&(e.regionCode="US-VA"),pmw.console.log("Retrieved the browser geo info from an external service: ",e," which took ",(performance.now()-t).toFixed(2)+" ms"),e}catch(e){return pmw.console.error("error: ",e),null}};e.getBrowserGeo=async()=>{let t={};if(e.retrieveData("geoCountryCode")&&(t.countryCode=e.retrieveData("geoCountryCode")),e.retrieveData("geoRegionCode")&&(t.regionCode=e.retrieveData("geoRegionCode")),e.retrieveData("geoRegion")&&(t.region=e.retrieveData("geoRegion")),t.countryCode)return pmw.console.log("Retrieved the browser geo location from the cache: ",t),t;let r=await e.getBrowserGeoFromExternalService();if(!r){const e=(()=>{const e=a(179).getLocationByTimeZone();return e.countryCode?e:null})();r={},e&&pmw.console.log("Retrieved the browser geo location from the timezone: ",e),e.countryCode&&(r.countryCode=e.countryCode),e.regionCode&&(r.regionCode=e.regionCode)}return r?.ip&&e.storeData("ip",r.ip),r?.countryCode&&e.storeData("geoCountryCode",r.countryCode),r?.regionCode&&e.storeData("geoRegionCode",r.regionCode),r?.region&&e.storeData("geoRegion",r.region),r?.countryCode?r:null},e.getIpFromAnyService=async(t="any",a=3)=>{const r=performance.now(),o=Object.keys(s).filter((e=>s[e][t])).slice(0,a).reduce(((e,t)=>(e[t]=s[t],e)),{});let n=[];if(Object.keys(o).forEach((a=>{o[a][t]&&n.push(e.getIpFromService(a,t))})),!(n.length>0))return pmw.console.log("No promises to resolve"),null;try{const e=await Promise.any(n),t=(performance.now()-r).toFixed(2);return pmw.console.log(`Retrieved the IP address ${e.ip} from ${e.service} which took ${t} ms`),e.ip}catch(e){return pmw.console.log(`Could not retrieve an IP with version ${t} from any of the services.`),null}},e.getIpFromService=async(e,t="any")=>{try{let a={ip:null,service:e,ipVersion:t};if(!s[e][t])return Promise.reject(new Error(`The IP version ${t} is not available for the service ${e}`));pmw.console.log(`Fetching IP from ${e} using IP version ${t}`);const o=await fetch(s[e][t]);if(!o.ok)return Promise.reject(new Error(`The response from ${e} was not OK. Status: ${o.status}`));let n=await o.text();return"cloudflare"===e?(n=n.trim().split("\n").reduce(((e,t)=>(e[(t=t.split("="))[0]]=t[1],e)),{}),a.ip=n.ip):a.ip=n.trim(),r(a.ip)?a:Promise.reject(new Error(`The returned value from ${e} is not an IP address`))}catch(e){return Promise.reject(e)}};e.isIpNotAllowed=async()=>!1===await e.isIpAllowed();const d=e=>{const{isInSubnet:t}=a(265);for(const a of l())if(p(a))try{if(t(e,a))return!0}catch(t){pmw.console.error("error: ",t),pmw.console.log("IP: "+e)}else if(e===a)return!0;return!1},p=e=>t(e),l=()=>{let e=[];return Array.isArray(wpmDataLayer.general.server_2_server.ip_exclude_list)&&(e=wpmDataLayer.general.server_2_server.ip_exclude_list),[].concat(["2001:4860:4801:10::/64","2001:4860:4801:11::/64","2001:4860:4801:12::/64","2001:4860:4801:13::/64","2001:4860:4801:14::/64","2001:4860:4801:15::/64","2001:4860:4801:16::/64","2001:4860:4801:17::/64","2001:4860:4801:18::/64","2001:4860:4801:19::/64","2001:4860:4801:1a::/64","2001:4860:4801:1b::/64","2001:4860:4801:1c::/64","2001:4860:4801:1d::/64","2001:4860:4801:1e::/64","2001:4860:4801:20::/64","2001:4860:4801:21::/64","2001:4860:4801:22::/64","2001:4860:4801:23::/64","2001:4860:4801:24::/64","2001:4860:4801:25::/64","2001:4860:4801:26::/64","2001:4860:4801:27::/64","2001:4860:4801:28::/64","2001:4860:4801:29::/64","2001:4860:4801:2::/64","2001:4860:4801:2a::/64","2001:4860:4801:2b::/64","2001:4860:4801:2c::/64","2001:4860:4801:2d::/64","2001:4860:4801:2e::/64","2001:4860:4801:2f::/64","2001:4860:4801:31::/64","2001:4860:4801:32::/64","2001:4860:4801:33::/64","2001:4860:4801:34::/64","2001:4860:4801:35::/64","2001:4860:4801:36::/64","2001:4860:4801:37::/64","2001:4860:4801:38::/64","2001:4860:4801:39::/64","2001:4860:4801:3a::/64","2001:4860:4801:3b::/64","2001:4860:4801:3c::/64","2001:4860:4801:3d::/64","2001:4860:4801:3e::/64","2001:4860:4801:40::/64","2001:4860:4801:41::/64","2001:4860:4801:42::/64","2001:4860:4801:43::/64","2001:4860:4801:44::/64","2001:4860:4801:45::/64","2001:4860:4801:46::/64","2001:4860:4801:47::/64","2001:4860:4801:48::/64","2001:4860:4801:49::/64","2001:4860:4801:4a::/64","2001:4860:4801:50::/64","2001:4860:4801:51::/64","2001:4860:4801:53::/64","2001:4860:4801:54::/64","2001:4860:4801:55::/64","2001:4860:4801:60::/64","2001:4860:4801:61::/64","2001:4860:4801:62::/64","2001:4860:4801:63::/64","2001:4860:4801:64::/64","2001:4860:4801:65::/64","2001:4860:4801:66::/64","2001:4860:4801:67::/64","2001:4860:4801:68::/64","2001:4860:4801:69::/64","2001:4860:4801:6a::/64","2001:4860:4801:6b::/64","2001:4860:4801:6c::/64","2001:4860:4801:6d::/64","2001:4860:4801:6e::/64","2001:4860:4801:6f::/64","2001:4860:4801:70::/64","2001:4860:4801:71::/64","2001:4860:4801:72::/64","2001:4860:4801:73::/64","2001:4860:4801:74::/64","2001:4860:4801:75::/64","2001:4860:4801:76::/64","2001:4860:4801:77::/64","2001:4860:4801:78::/64","2001:4860:4801:79::/64","2001:4860:4801:80::/64","2001:4860:4801:81::/64","2001:4860:4801:82::/64","2001:4860:4801:83::/64","2001:4860:4801:84::/64","2001:4860:4801:85::/64","2001:4860:4801:86::/64","2001:4860:4801:87::/64","2001:4860:4801:88::/64","2001:4860:4801:90::/64","2001:4860:4801:91::/64","2001:4860:4801:92::/64","2001:4860:4801:93::/64","2001:4860:4801:c::/64","2001:4860:4801:f::/64","192.178.5.0/27","34.100.182.96/28","34.101.50.144/28","34.118.254.0/28","34.118.66.0/28","34.126.178.96/28","34.146.150.144/28","34.147.110.144/28","34.151.74.144/28","34.152.50.64/28","34.154.114.144/28","34.155.98.32/28","34.165.18.176/28","34.175.160.64/28","34.176.130.16/28","34.22.85.0/27","34.64.82.64/28","34.65.242.112/28","34.80.50.80/28","34.88.194.0/28","34.89.10.80/28","34.89.198.80/28","34.96.162.48/28","35.247.243.240/28","66.249.64.0/27","66.249.64.128/27","66.249.64.160/27","66.249.64.224/27","66.249.64.32/27","66.249.64.64/27","66.249.64.96/27","66.249.65.0/27","66.249.65.160/27","66.249.65.192/27","66.249.65.224/27","66.249.65.32/27","66.249.65.64/27","66.249.65.96/27","66.249.66.0/27","66.249.66.160/27","66.249.66.192/27","66.249.66.32/27","66.249.66.64/27","66.249.66.96/27","66.249.68.0/27","66.249.68.32/27","66.249.68.64/27","66.249.69.0/27","66.249.69.128/27","66.249.69.160/27","66.249.69.192/27","66.249.69.224/27","66.249.69.32/27","66.249.69.64/27","66.249.69.96/27","66.249.70.0/27","66.249.70.128/27","66.249.70.160/27","66.249.70.192/27","66.249.70.224/27","66.249.70.32/27","66.249.70.64/27","66.249.70.96/27","66.249.71.0/27","66.249.71.128/27","66.249.71.160/27","66.249.71.192/27","66.249.71.224/27","66.249.71.32/27","66.249.71.64/27","66.249.71.96/27","66.249.72.0/27","66.249.72.128/27","66.249.72.160/27","66.249.72.192/27","66.249.72.224/27","66.249.72.32/27","66.249.72.64/27","66.249.72.96/27","66.249.73.0/27","66.249.73.128/27","66.249.73.160/27","66.249.73.192/27","66.249.73.224/27","66.249.73.32/27","66.249.73.64/27","66.249.73.96/27","66.249.74.0/27","66.249.74.128/27","66.249.74.32/27","66.249.74.64/27","66.249.74.96/27","66.249.75.0/27","66.249.75.128/27","66.249.75.160/27","66.249.75.192/27","66.249.75.224/27","66.249.75.32/27","66.249.75.64/27","66.249.75.96/27","66.249.76.0/27","66.249.76.128/27","66.249.76.160/27","66.249.76.192/27","66.249.76.224/27","66.249.76.32/27","66.249.76.64/27","66.249.76.96/27","66.249.77.0/27","66.249.77.128/27","66.249.77.160/27","66.249.77.192/27","66.249.77.224/27","66.249.77.32/27","66.249.77.64/27","66.249.77.96/27","66.249.78.0/27","66.249.78.32/27","66.249.79.0/27","66.249.79.128/27","66.249.79.160/27","66.249.79.192/27","66.249.79.224/27","66.249.79.32/27","66.249.79.64/27","66.249.79.96/27","2001:4860:4801:2008::/64","2001:4860:4801:200c::/64","2001:4860:4801:200d::/64","2001:4860:4801:2010::/64","2001:4860:4801:2011::/64","2001:4860:4801:2012::/64","2001:4860:4801:2013::/64","2001:4860:4801:2014::/64","2001:4860:4801:2015::/64","2001:4860:4801:2016::/64","2001:4860:4801:2017::/64","2001:4860:4801:2018::/64","2001:4860:4801:2019::/64","2001:4860:4801:201a::/64","2001:4860:4801:201b::/64","2001:4860:4801:201c::/64","2001:4860:4801:201d::/64","2001:4860:4801:201e::/64","2001:4860:4801:2020::/64","2001:4860:4801:2021::/64","2001:4860:4801:2022::/64","2001:4860:4801:2023::/64","2001:4860:4801:2024::/64","2001:4860:4801:2025::/64","2001:4860:4801:2026::/64","2001:4860:4801:2027::/64","2001:4860:4801:2028::/64","2001:4860:4801:2029::/64","2001:4860:4801:202a::/64","2001:4860:4801:202b::/64","2001:4860:4801:202c::/64","2001:4860:4801:202d::/64","2001:4860:4801:202e::/64","2001:4860:4801:202f::/64","2001:4860:4801:2031::/64","2001:4860:4801:2032::/64","2001:4860:4801:2033::/64","2001:4860:4801:2034::/64","2001:4860:4801:2035::/64","2001:4860:4801:2036::/64","2001:4860:4801:2037::/64","2001:4860:4801:2038::/64","2001:4860:4801:2039::/64","2001:4860:4801:203a::/64","2001:4860:4801:203b::/64","2001:4860:4801:203c::/64","2001:4860:4801:203d::/64","2001:4860:4801:203e::/64","2001:4860:4801:2040::/64","2001:4860:4801:2041::/64","2001:4860:4801:2042::/64","2001:4860:4801:2043::/64","2001:4860:4801:2044::/64","2001:4860:4801:2045::/64","2001:4860:4801:2046::/64","2001:4860:4801:2047::/64","2001:4860:4801:2048::/64","2001:4860:4801:2049::/64","2001:4860:4801:204a::/64","2001:4860:4801:2050::/64","2001:4860:4801:2051::/64","2001:4860:4801:2052::/64","2001:4860:4801:2053::/64","2001:4860:4801:2054::/64","2001:4860:4801:2060::/64","2001:4860:4801:2061::/64","2001:4860:4801:2062::/64","2001:4860:4801:2063::/64","2001:4860:4801:2064::/64","2001:4860:4801:2065::/64","2001:4860:4801:2066::/64","2001:4860:4801:2067::/64","2001:4860:4801:2068::/64","2001:4860:4801:2069::/64","2001:4860:4801:206a::/64","2001:4860:4801:206b::/64","2001:4860:4801:206c::/64","2001:4860:4801:206d::/64","2001:4860:4801:206e::/64","2001:4860:4801:206f::/64","2001:4860:4801:2070::/64","2001:4860:4801:2071::/64","2001:4860:4801:2072::/64","2001:4860:4801:2073::/64","2001:4860:4801:2074::/64","2001:4860:4801:2075::/64","2001:4860:4801:2076::/64","2001:4860:4801:2077::/64","2001:4860:4801:2078::/64","2001:4860:4801:2079::/64","2001:4860:4801:2080::/64","2001:4860:4801:2081::/64","2001:4860:4801:2082::/64","2001:4860:4801:2083::/64","2001:4860:4801:2084::/64","2001:4860:4801:2085::/64","2001:4860:4801:2086::/64","2001:4860:4801:2087::/64","2001:4860:4801:2088::/64","2001:4860:4801:2090::/64","2001:4860:4801:2091::/64","2001:4860:4801:2092::/64","2001:4860:4801:2093::/64","192.178.17.0/27","209.85.238.0/27","209.85.238.128/27","209.85.238.160/27","209.85.238.192/27","209.85.238.224/27","209.85.238.32/27","209.85.238.64/27","209.85.238.96/27","66.249.87.0/27","66.249.87.128/27","66.249.87.160/27","66.249.87.192/27","66.249.87.224/27","66.249.87.32/27","66.249.87.64/27","66.249.87.96/27","66.249.89.0/27","66.249.89.128/27","66.249.89.160/27","66.249.89.224/27","66.249.89.32/27","66.249.89.64/27","66.249.89.96/27","66.249.90.0/27","66.249.90.128/27","66.249.90.160/27","66.249.90.192/27","66.249.90.224/27","66.249.90.32/27","66.249.90.96/27","66.249.91.0/27","66.249.91.128/27","66.249.91.160/27","66.249.91.192/27","66.249.91.224/27","66.249.91.32/27","66.249.91.64/27","66.249.91.96/27","66.249.92.0/27","66.249.92.128/27","66.249.92.160/27","66.249.92.192/27","66.249.92.32/27","66.249.92.96/27","72.14.199.0/27","72.14.199.128/27","72.14.199.160/27","72.14.199.192/27","72.14.199.224/27","72.14.199.32/27","72.14.199.64/27","72.14.199.96/27","74.125.148.0/27","74.125.148.128/27","74.125.148.160/27","74.125.148.192/27","74.125.148.224/27","74.125.148.32/27","74.125.148.64/27","74.125.148.96/27","74.125.149.0/27","74.125.149.128/27","74.125.149.160/27","74.125.149.192/27","74.125.149.224/27","74.125.149.32/27","74.125.149.64/27","74.125.149.96/27","74.125.150.0/27","74.125.150.32/27","74.125.150.64/27","74.125.151.0/27","74.125.151.128/27","74.125.151.160/27","74.125.151.192/27","74.125.151.224/27","74.125.151.32/27","74.125.151.64/27","74.125.151.96/27","74.125.216.0/27","74.125.216.128/27","74.125.216.160/27","74.125.216.192/27","74.125.216.224/27","74.125.216.32/27","74.125.216.64/27","74.125.216.96/27","74.125.217.0/27","74.125.217.128/27","74.125.217.32/27","74.125.217.64/27","74.125.217.96/27","74.125.218.0/27","74.125.218.128/27","74.125.218.160/27","74.125.218.192/27","74.125.218.224/27","74.125.218.32/27","74.125.218.64/27","74.125.218.96/27","74.125.219.0/27","74.125.219.32/27"],["69.63.176.0/20","66.220.144.0/20","66.220.144.0/21","69.63.184.0/21","69.63.176.0/21","74.119.76.0/22","69.171.255.0/24","173.252.64.0/18","69.171.224.0/19","69.171.224.0/20","103.4.96.0/22","173.252.64.0/19","31.13.64.0/18","31.13.24.0/21","66.220.152.0/21","69.171.239.0/24","69.171.240.0/20","31.13.64.0/19","31.13.64.0/24","31.13.65.0/24","31.13.67.0/24","31.13.68.0/24","31.13.69.0/24","31.13.70.0/24","31.13.71.0/24","31.13.72.0/24","31.13.73.0/24","31.13.74.0/24","31.13.75.0/24","31.13.76.0/24","31.13.77.0/24","31.13.96.0/19","31.13.66.0/24","173.252.96.0/19","69.63.178.0/24","31.13.78.0/24","31.13.79.0/24","31.13.80.0/24","31.13.82.0/24","31.13.83.0/24","31.13.84.0/24","31.13.85.0/24","31.13.86.0/24","31.13.87.0/24","31.13.88.0/24","31.13.89.0/24","31.13.91.0/24","31.13.92.0/24","31.13.93.0/24","31.13.94.0/24","31.13.95.0/24","31.13.81.0/24","179.60.192.0/22","179.60.192.0/24","179.60.193.0/24","179.60.194.0/24","179.60.195.0/24","185.60.216.0/22","45.64.40.0/22","185.60.216.0/24","185.60.217.0/24","185.60.218.0/24","185.60.219.0/24","129.134.0.0/16","157.240.0.0/16","157.240.8.0/24","157.240.0.0/24","157.240.1.0/24","157.240.2.0/24","157.240.3.0/24","157.240.5.0/24","157.240.6.0/24","157.240.7.0/24","157.240.9.0/24","157.240.10.0/24","157.240.16.0/24","157.240.19.0/24","157.240.11.0/24","157.240.12.0/24","157.240.13.0/24","157.240.14.0/24","157.240.15.0/24","157.240.17.0/24","157.240.18.0/24","157.240.20.0/24","157.240.21.0/24","157.240.22.0/24","157.240.23.0/24","157.240.0.0/17","69.171.250.0/24","204.15.20.0/22","157.240.192.0/24","157.240.198.0/24","102.132.96.0/20","102.132.96.0/24","102.132.97.0/24","157.240.26.0/24","157.240.27.0/24","157.240.28.0/24","157.240.29.0/24","157.240.30.0/24","129.134.28.0/24","129.134.29.0/24","157.240.208.0/24","157.240.193.0/24","157.240.194.0/24","157.240.195.0/24","157.240.197.0/24","157.240.196.0/24","157.240.200.0/24","157.240.201.0/24","157.240.203.0/24","157.240.204.0/24","157.240.205.0/24","157.240.206.0/24","157.240.207.0/24","157.240.209.0/24","157.240.210.0/24","157.240.211.0/24","157.240.212.0/24","157.240.213.0/24","157.240.214.0/24","157.240.215.0/24","157.240.216.0/24","157.240.222.0/24","129.134.30.0/24","129.134.31.0/24","129.134.30.0/23","129.134.25.0/24","129.134.26.0/24","129.134.27.0/24","102.132.99.0/24","102.132.101.0/24","129.134.64.0/24","129.134.65.0/24","129.134.66.0/24","129.134.67.0/24","157.240.219.0/24","157.240.217.0/24","157.240.218.0/24","157.240.199.0/24","129.134.127.0/24","157.240.223.0/24","157.240.192.0/18","157.240.221.0/24","157.240.220.0/24","173.252.88.0/21","129.134.68.0/24","129.134.69.0/24","129.134.70.0/24","157.240.24.0/24","157.240.25.0/24","102.132.100.0/24","157.240.31.0/24","157.240.224.0/24","129.134.71.0/24","157.240.225.0/24","157.240.226.0/24","157.240.227.0/24","129.134.0.0/17","129.134.72.0/24","129.134.73.0/24","129.134.74.0/24","185.89.218.0/24","185.89.219.0/24","185.89.218.0/23","157.240.228.0/24","157.240.229.0/24","129.134.76.0/24","129.134.75.0/24","157.240.239.0/24","157.240.240.0/24","157.240.241.0/24","157.240.231.0/24","157.240.232.0/24","157.240.233.0/24","157.240.234.0/24","157.240.235.0/24","157.240.236.0/24","129.134.77.0/24","129.134.78.0/24","129.134.79.0/24","157.240.237.0/24","157.240.238.0/24","157.240.242.0/24","157.240.243.0/24","129.134.112.0/24","157.240.100.0/24","157.240.98.0/24","157.240.96.0/24","157.240.99.0/24","157.240.101.0/24","129.134.113.0/24","129.134.114.0/24","157.240.97.0/24","129.134.115.0/24","157.240.244.0/24","157.240.245.0/24","157.240.246.0/24","157.240.247.0/24","157.240.248.0/24","157.240.249.0/24","157.240.250.0/24","163.70.128.0/17","163.77.128.0/17","157.240.251.0/24","157.240.252.0/24","157.240.253.0/24","147.75.208.0/20","157.240.254.0/24","185.89.219.0/24","185.89.218.0/24","185.89.218.0/23","185.89.216.0/22","147.75.208.0/20","204.15.20.0/22","69.63.176.0/20","69.63.176.0/21","69.63.184.0/21","66.220.144.0/20","69.63.176.0/20","2620:0:1c00::/40","2a03:2880::/32","2a03:2880:fffe::/48","2a03:2880:ffff::/48","2620:0:1cff::/48","2a03:2880:f001::/48","2a03:2880:f003::/48","2a03:2880:f004::/48","2a03:2880:f005::/48","2a03:2880:f006::/48","2a03:2880:f007::/48","2a03:2880:f008::/48","2a03:2880:f00a::/48","2a03:2880:f00c::/48","2a03:2880:f00d::/48","2a03:2880:f00e::/48","2a03:2880:f00f::/48","2a03:2880:f010::/48","2a03:2880:f011::/48","2a03:2880:f012::/48","2a03:2880:f013::/48","2a03:2880:f016::/48","2a03:2880:f017::/48","2a03:2880:f019::/48","2a03:2880:f01b::/48","2a03:2880:f01c::/48","2a03:2880:f01f::/48","2a03:2880:1000::/36","2a03:2880:2000::/36","2a03:2880:3000::/36","2a03:2880:4000::/36","2a03:2880:5000::/36","2a03:2880:6000::/36","2a03:2880:7000::/36","2a03:2880:f021::/48","2a03:2880:f023::/48","2a03:2880:f024::/48","2a03:2880:f027::/48","2a03:2880:f028::/48","2a03:2880:f029::/48","2a03:2880:f02b::/48","2a03:2880:f02c::/48","2a03:2880:f02d::/48","2a03:2880:f02e::/48","2a03:2880:f02f::/48","2a03:2880:f030::/48","2a03:2880:f031::/48","2a03:2880:f032::/48","2a03:2880:f033::/48","2a03:2880:f034::/48","2a03:2880:f035::/48","2a03:2880:f036::/48","2a03:2880:f037::/48","2a03:2880:f038::/48","2a03:2880:f03a::/48","2a03:2880:f03b::/48","2a03:2880:f03d::/48","2a03:2880:f03e::/48","2a03:2880:f03f::/48","2401:db00::/32","2a03:2880::/36","2a03:2880:f101::/48","2a03:2880:f201::/48","2a03:2880:f103::/48","2a03:2880:f203::/48","2a03:2880:f104::/48","2a03:2880:f204::/48","2a03:2880:f107::/48","2a03:2880:f207::/48","2a03:2880:f108::/48","2a03:2880:f208::/48","2a03:2880:f10a::/48","2a03:2880:f20a::/48","2a03:2880:f10d::/48","2a03:2880:f20d::/48","2a03:2880:f10e::/48","2a03:2880:f20e::/48","2a03:2880:f10f::/48","2a03:2880:f20f::/48","2a03:2880:f110::/48","2a03:2880:f210::/48","2a03:2880:f111::/48","2a03:2880:f211::/48","2a03:2880:f112::/48","2a03:2880:f212::/48","2a03:2880:f116::/48","2a03:2880:f216::/48","2a03:2880:f117::/48","2a03:2880:f217::/48","2a03:2880:f119::/48","2a03:2880:f219::/48","2a03:2880:f11f::/48","2a03:2880:f21f::/48","2a03:2880:f121::/48","2a03:2880:f221::/48","2a03:2880:f123::/48","2a03:2880:f223::/48","2a03:2880:f10c::/48","2a03:2880:f20c::/48","2a03:2880:f105::/48","2a03:2880:f205::/48","2a03:2880:f106::/48","2a03:2880:f206::/48","2a03:2880:f11b::/48","2a03:2880:f21b::/48","2a03:2880:f113::/48","2a03:2880:f213::/48","2a03:2880:f11c::/48","2a03:2880:f21c::/48","2a03:2880:f128::/48","2a03:2880:f228::/48","2a03:2880:f02a::/48","2a03:2880:f12a::/48","2a03:2880:f22a::/48","2a03:2880:f12f::/48","2a03:2880:f22f::/48","2a03:2880:f124::/48","2a03:2880:f127::/48","2a03:2880:f129::/48","2a03:2880:f12b::/48","2a03:2880:f12c::/48","2a03:2880:f12d::/48","2a03:2880:f12e::/48","2a03:2880:f130::/48","2a03:2880:f131::/48","2a03:2880:f132::/48","2a03:2880:f133::/48","2a03:2880:f134::/48","2a03:2880:f135::/48","2a03:2880:f136::/48","2a03:2880:f137::/48","2a03:2880:f138::/48","2a03:2880:f13a::/48","2a03:2880:f13b::/48","2a03:2880:f13d::/48","2a03:2880:f13e::/48","2a03:2880:f13f::/48","2a03:2880:f224::/48","2a03:2880:f227::/48","2a03:2880:f229::/48","2a03:2880:f22b::/48","2a03:2880:f22c::/48","2a03:2880:f22d::/48","2a03:2880:f22e::/48","2a03:2880:f230::/48","2a03:2880:f231::/48","2a03:2880:f232::/48","2a03:2880:f233::/48","2a03:2880:f234::/48","2a03:2880:f235::/48","2a03:2880:f236::/48","2a03:2880:f237::/48","2a03:2880:f238::/48","2a03:2880:f23a::/48","2a03:2880:f23b::/48","2a03:2880:f23d::/48","2a03:2880:f23e::/48","2a03:2880:f23f::/48","2a03:2880:f0ff::/48","2a03:2880:f1ff::/48","2a03:2880:f2ff::/48","2a03:2880:f044::/48","2a03:2880:f144::/48","2a03:2880:f244::/48","2a03:2880:f042::/48","2a03:2880:f043::/48","2a03:2880:f045::/48","2a03:2880:f046::/48","2a03:2880:f047::/48","2a03:2880:f048::/48","2a03:2880:f04a::/48","2a03:2880:f04c::/48","2a03:2880:f04b::/48","2a03:2880:f04d::/48","2a03:2880:f259::/48","2a03:2880:f258::/48","2a03:2880:f257::/48","2a03:2880:f256::/48","2a03:2880:f255::/48","2a03:2880:f254::/48","2a03:2880:f253::/48","2a03:2880:f252::/48","2a03:2880:f250::/48","2a03:2880:f24f::/48","2a03:2880:f24d::/48","2a03:2880:f24e::/48","2a03:2880:f24c::/48","2a03:2880:f24b::/48","2a03:2880:f24a::/48","2a03:2880:f248::/48","2a03:2880:f247::/48","2a03:2880:f246::/48","2a03:2880:f245::/48","2a03:2880:f243::/48","2a03:2880:f242::/48","2a03:2880:f241::/48","2a03:2880:f240::/48","2a03:2880:f159::/48","2a03:2880:f158::/48","2a03:2880:f157::/48","2a03:2880:f156::/48","2a03:2880:f155::/48","2a03:2880:f154::/48","2a03:2880:f153::/48","2a03:2880:f152::/48","2a03:2880:f150::/48","2a03:2880:f14f::/48","2a03:2880:f14e::/48","2a03:2880:f14d::/48","2a03:2880:f14c::/48","2a03:2880:f14b::/48","2a03:2880:f14a::/48","2a03:2880:f148::/48","2a03:2880:f147::/48","2a03:2880:f146::/48","2a03:2880:f145::/48","2a03:2880:f143::/48","2a03:2880:f142::/48","2a03:2880:f141::/48","2a03:2880:f140::/48","2a03:2880:f059::/48","2a03:2880:f058::/48","2a03:2880:f057::/48","2a03:2880:f056::/48","2a03:2880:f055::/48","2a03:2880:f054::/48","2a03:2880:f053::/48","2a03:2880:f052::/48","2a03:2880:f050::/48","2a03:2880:f04f::/48","2a03:2880:f04e::/48","2a03:2880:ff0b::/48","2a03:2880:ff0c::/48","2a03:2880:f040::/48","2a03:2880:f041::/48","2a03:2880:f0fc::/48","2a03:2880:f0fd::/48","2a03:2880:f0fc::/47","2a03:2880:f1fc::/48","2a03:2880:f1fd::/48","2a03:2880:f1fc::/47","2a03:2880:ff08::/48","2a03:2880:ff09::/48","2a03:2880:ff0a::/48","2a03:2880:f05e::/48","2a03:2880:f15e::/48","2a03:2880:f25e::/48","2620:0:1cfa::/48","2a03:2880:f05b::/48","2a03:2880:f05a::/48","2a03:2880:f25a::/48","2a03:2880:f15c::/48","2a03:2880:f000::/36","2a03:2880:f05d::/48","2a03:2880:f25c::/48","2a03:2880:f05c::/48","2a03:2880:f260::/48","2a03:2880:f060::/48","2a03:2880:f160::/48","2a03:2880:f15d::/48","2a03:2880:f25b::/48","2a03:2880:f25d::/48","2a03:2880:f15b::/48","2a03:2880:f15a::/48","2a03:2880:f161::/48","2a03:2880:f061::/48","2a03:2880:f261::/48","2a03:2881::/32","2a03:2881::/48","2a03:2881:1::/48","2a03:2881:2::/48","2a03:2881:3::/48","2a03:2880:f162::/48","2a03:2880:f262::/48","2a03:2881:4000::/48","2a03:2881:4003::/48","2a03:2881:4001::/48","2a03:2881:4002::/48","2a03:2880:f065::/48","2a03:2880:f163::/48","2a03:2880:f066::/48","2a03:2880:f263::/48","2a03:2880:f264::/48","2a03:2880:f164::/48","2a03:2880:f067::/48","2a03:2880:f165::/48","2a03:2880:f265::/48","2a03:2880:f068::/48","2a03:2881:4004::/48","2a03:2880:f06a::/48","2a03:2880:f266::/48","2a03:2880:f166::/48","2a03:2880:f267::/48","2a03:2880:f06b::/48","2a03:2880:f167::/48","2a03:2881:4006::/48","2a03:2881:7::/48","2a03:2881:9::/48","2a03:2881:8::/48","2a03:2881:4::/48","2a03:2881:6::/48","2a03:2881:5::/48","2a03:2881:a::/48","2a03:2880:f268::/48","2a03:2880:f06d::/48","2a03:2880:f168::/48","2a03:2881:b::/48","2a03:2881:c::/48","2a03:2881:4007::/48","2a03:2880:f269::/48","2a03:2880:f169::/48","2a03:2880:f06f::/48","2a03:2880:f26a::/48","2a03:2880:f16a::/48","2a03:2880:f070::/48","2a03:2881:d::/48","2a03:2881:e::/48","2a03:2880:f071::/48","2a03:2880:f16b::/48","2a03:2880:f26b::/48","2a03:2881:4008::/48","2a03:2881:10::/48","2a03:2881:f::/48","2a03:2881:11::/48","2a03:2880:f26c::/48","2a03:2880:f16c::/48","2a03:2880:f073::/48","2a03:2880:f16d::/48","2a03:2880:f074::/48","2a03:2880:f26d::/48","2a03:2881:4009::/48","2a03:2880:f26e::/48","2a03:2880:f16e::/48","2a03:2880:f076::/48","2a03:2880:f16f::/48","2a03:2880:f26f::/48","2a03:2880:f077::/48","2a03:2881:12::/48","2a03:2881:13::/48","2a03:2881:17::/48","2a03:2881:15::/48","2a03:2881:18::/48","2a03:2881:14::/48","2a03:2881:16::/48","2a03:2881:19::/48","2a03:2881:4005::/48","2a03:2880:f078::/48","2a03:2880:f170::/48","2a03:2880:f270::/48","2a03:2881:400a::/48","2a03:2881:400c::/48","2a03:2881:400b::/48","2a03:2881:400d::/48","2a03:2881:1a::/48","2a03:2881:1c::/48","2a03:2881:1b::/48","2a03:2880:f271::/48","2a03:2880:f07d::/48","2a03:2880:f171::/48","2a03:2880:f07e::/48","2a03:2880:f172::/48","2a03:2880:f272::/48","2a03:2880:f080::/48","2a03:2880:f173::/48","2a03:2880:f273::/48","2a03:2880:f081::/48","2a03:2880:f174::/48","2a03:2880:f274::/48","2a03:2880:f175::/48","2a03:2880:f275::/48","2a03:2880:f082::/48","2a03:2880:f176::/48","2a03:2880:f276::/48","2a03:2880:f083::/48","2a03:2880:f277::/48","2a03:2880:f084::/48","2a03:2880:f177::/48","2a03:2881:1e::/48","2a03:2880:f085::/48","2a03:2880:f178::/48","2a03:2880:f278::/48","2a03:2880:f179::/48","2a03:2880:f086::/48","2a03:2880:f279::/48","2a03:2880:f17a::/48","2a03:2880:f08a::/48","2a03:2880:f27a::/48","2a03:2881:48::/45","2a10:f781:10:cee0::/64","2a03:83e0::/32"],["199.16.156.0/22","199.59.148.0/22","192.133.76.0/22"],["157.55.39.0/24","207.46.13.0/24","40.77.167.0/24","13.66.139.0/24","13.66.144.0/24","52.167.144.0/24","13.67.10.16/28","13.69.66.240/28","13.71.172.224/28","139.217.52.0/28","191.233.204.224/28","20.36.108.32/28","20.43.120.16/28","40.79.131.208/28","40.79.186.176/28","52.231.148.0/28","20.79.107.240/28","51.105.67.0/28","20.125.163.80/28","40.77.188.0/22","65.55.210.0/24","199.30.24.0/23","40.77.202.0/24","40.77.139.0/25","20.74.197.0/28"],["54.236.1.1/32","54.236.1.2/31","54.236.1.4/30","54.236.1.8/29","54.236.1.16/28","54.236.1.32/27","54.236.1.64/26","54.236.1.128/25"],["18.203.61.76","18.203.176.135","52.17.197.221"],["204.236.235.245","75.101.186.145"],["18.207.141.103","52.1.113.12"],["180.76.15.0/24","119.63.196.0/24","115.239.212.0/24","119.63.199.0/24","122.81.208.0/22","123.125.71.0/24","180.76.4.0/24","180.76.5.0/24","180.76.6.0/24","185.10.104.0/24","220.181.108.0/24","220.181.51.0/24","111.13.102.0/24","123.125.67.144/29","123.125.67.152/31","61.135.169.0/24","123.125.68.68/30","123.125.68.72/29","123.125.68.80/28","123.125.68.96/30","202.46.48.0/20","220.181.38.0/24","123.125.68.80/30","123.125.68.84/31","123.125.68.0/24"],["20.191.45.212","40.88.21.235","40.76.173.151","40.76.163.7","20.185.79.47","52.142.26.175","20.185.79.15","52.142.24.149","40.76.162.208","40.76.163.23","40.76.162.191","40.76.162.247"],["5.255.250.0/24","37.9.87.0/24","67.195.37.0/24","67.195.50.0/24","67.195.110.0/24","67.195.111.0/24","67.195.112.0/23","67.195.114.0/24","67.195.115.0/24","68.180.224.0/21","72.30.132.0/24","72.30.142.0/24","72.30.161.0/24","72.30.196.0/24","72.30.198.0/24","74.6.254.0/24","74.6.8.0/24","74.6.13.0/24","74.6.17.0/24","74.6.18.0/24","74.6.22.0/24","74.6.27.0/24","74.6.168.0/24","77.88.5.0/24","77.88.47.0/24","93.158.161.0/24","98.137.72.0/24","98.137.206.0/24","98.137.207.0/24","98.139.168.0/24","114.111.95.0/24","124.83.159.0/24","124.83.179.0/24","124.83.223.0/24","141.8.144.0/24","183.79.63.0/24","183.79.92.0/24","203.216.255.0/24","211.14.11.0/24"],["100.43.90.0/24","37.9.115.0/24","37.140.165.0/24","77.88.22.0/25","77.88.29.0/24","77.88.31.0/24","77.88.59.0/24","84.201.146.0/24","84.201.148.0/24","84.201.149.0/24","87.250.243.0/24","87.250.253.0/24","93.158.147.0/24","93.158.148.0/24","93.158.151.0/24","93.158.153.0/32","95.108.128.0/24","95.108.138.0/24","95.108.150.0/23","95.108.158.0/24","95.108.156.0/24","95.108.188.128/25","95.108.234.0/24","95.108.248.0/24","100.43.80.0/24","130.193.62.0/24","141.8.153.0/24","178.154.165.0/24","178.154.166.128/25","178.154.173.29","178.154.200.158","178.154.202.0/24","178.154.205.0/24","178.154.239.0/24","178.154.243.0/24","37.9.84.253","199.21.99.99","178.154.162.29","178.154.203.251","178.154.211.250","178.154.171.0/24","178.154.200.0/24","178.154.244.0/24","178.154.246.0/24","95.108.181.0/24","95.108.246.252","5.45.254.0/24","5.255.253.0/24","37.140.141.0/24","37.140.188.0/24","100.43.81.0/24","100.43.85.0/24","100.43.91.0/24","199.21.99.0/24","2a02:6b8:b000::/32","2a02:6b8:b010::/32","2a02:6b8:b011::/32","2a02:6b8:c0e::/32"],["220.181.125.0/24","*************/27","*************/28","*************","*************","*************"],["**************/29","**************/28"],e)}}(window.wpm=window.wpm||{},jQuery)},618:function(e,t,a){"use strict";var r=this&&this.__spreadArrays||function(){for(var e=0,t=0,a=arguments.length;t<a;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<a;t++)for(var n=arguments[t],i=0,s=n.length;i<s;i++,o++)r[o]=n[i];return r};Object.defineProperty(t,"__esModule",{value:!0}),t.isSpecial=t.isReserved=t.isLocalhost=t.isPrivate=t.createChecker=t.isInSubnet=void 0;var o=a(609),n=a(503);function i(e){if(!o.isIPv4(e))throw new Error("not a valid IPv4 address: "+e);var t=e.split(".");return(parseInt(t[0],10)<<24)+(parseInt(t[1],10)<<16)+(parseInt(t[2],10)<<8)+parseInt(t[3],10)>>>0}function s(e){if(Array.isArray(e)){var t=e.map((function(e){return c(e)}));return function(e){var a=i(e);return t.some((function(e){return e(a)}))}}var a=c(e);return function(e){var t=i(e);return a(t)}}function c(e){var t=e.split("/"),a=t[0],r=t[1],o=parseInt(r,10);if(!a||!Number.isInteger(o))throw new Error("not a valid IPv4 subnet: "+e);if(o<0||o>32)throw new Error("not a valid IPv4 prefix length: "+o+" (from "+e+")");var n=i(a);return function(e){return 0===o||n>>32-o===e>>32-o}}t.isInSubnet=function(e,t){return s(t)(e)},t.createChecker=s;var d={};t.isPrivate=function(e){return"private"in d==!1&&(d.private=s(n.default.private.ipv4)),d.private(e)},t.isLocalhost=function(e){return"localhost"in d==!1&&(d.localhost=s(n.default.localhost.ipv4)),d.localhost(e)},t.isReserved=function(e){return"reserved"in d==!1&&(d.reserved=s(n.default.reserved.ipv4)),d.reserved(e)},t.isSpecial=function(e){return"special"in d==!1&&(d.special=s(r(n.default.private.ipv4,n.default.localhost.ipv4,n.default.reserved.ipv4))),d.special(e)}},624:()=>{!function(e){e.getGA4OrderItems=function(){let e=[];return Object.values(wpmDataLayer.order.items).forEach((t=>{let a;a={quantity:t.quantity,price:t.price,item_name:t.name,currency:wpmDataLayer.order.currency,item_category:wpmDataLayer.products[t.id].category.join("/")},t?.custom_parameters&&Object.keys(t.custom_parameters).forEach((e=>{a[e]=t.custom_parameters[e]})),0!==t.variation_id?(a.item_id=String(wpmDataLayer.products[t.variation_id].dyn_r_ids[wpmDataLayer.pixels.google.analytics.id_type]),a.item_variant=wpmDataLayer.products[t.variation_id].variant_description,a.item_brand=wpmDataLayer.products[t.variation_id].brand):(a.item_id=String(wpmDataLayer.products[t.id].dyn_r_ids[wpmDataLayer.pixels.google.analytics.id_type]),a.item_brand=wpmDataLayer.products[t.id].brand),e.push(a)})),e},e.ga4AddFormattedCategories=(e,t)=>{if(t=Array.from(new Set(t)),Array.isArray(t)&&t.length){e.item_category=t[0];let a=t.length>5?5:t.length;for(let r=1;r<a;r++)e["item_category"+(r+1)]=t[r]}return e},e.getCartItemsGa4=()=>{let t=[];return Object.values(wpmDataLayer.cart).forEach((a=>{t.push(e.ga4GetFullProductItemData(a))})),t},e.ga4GetBasicProductItemData=e=>({item_id:e.dyn_r_ids[wpmDataLayer.pixels.google.analytics.id_type],item_name:e.name,item_brand:e.brand,item_variant:e.variant,price:e.price,currency:wpmDataLayer.shop.currency,quantity:e.quantity}),e.ga4AddListNameToProduct=(e,t=null)=>(e.item_list_name=wpmDataLayer.shop.list_name,e.item_list_id=wpmDataLayer.shop.list_id,t&&(e.index=t),e),e.ga4GetFullProductItemData=t=>{let a;return a=e.ga4GetBasicProductItemData(t),a=e.ga4AddListNameToProduct(a,t.position),a=e.ga4AddFormattedCategories(a,t.category),a}}(window.wpm=window.wpm||{},jQuery)},644:()=>{!function(e){let t;e.fbeventsJsUrl=()=>{let e=new URLSearchParams(window.location.search);return e.has("fbevents-version")?`https://connect.facebook.net/en_US/fbevents.js?v=${e.get("fbevents-version")}`:wpmDataLayer?.pixels?.facebook?.fbevents_js_url},e.loadFacebookPixel=()=>{try{!function(e,t,a,r,o,n,i){e.fbq||(o=e.fbq=function(){o.callMethod?o.callMethod.apply(o,arguments):o.queue.push(arguments)},e._fbq||(e._fbq=o),o.push=o,o.loaded=!0,o.version="2.0",o.queue=[],(n=t.createElement(a)).async=!0,n.src=r,(i=t.getElementsByTagName(a)[0]).parentNode.insertBefore(n,i))}(window,document,"script",e.fbeventsJsUrl());let t={};e.isFbpSet()&&e.isFbAdvancedMatchingEnabled()&&(t={...e.getUserIdentifiersForFb()}),fbq("init",wpmDataLayer.pixels.facebook.pixel_id,t),wpmDataLayer?.pixels?.facebook?.mobile_bridge_app_id&&fbq("set","mobileBridge",wpmDataLayer.pixels.facebook.pixel_id,wpmDataLayer?.pixels?.facebook?.mobile_bridge_app_id),wpmDataLayer.pixels.facebook.loaded=!0}catch(e){console.error(e)}},e.getUserIdentifiersForFb=()=>{let e={};return wpmDataLayer?.user?.id?.raw&&(e.external_id=wpmDataLayer.user.id.raw),wpmDataLayer?.order?.user_id&&(e.external_id=wpmDataLayer.order.user_id),wpmDataLayer?.user?.email?.facebook&&(e.em=wpmDataLayer.user.email.facebook),wpmDataLayer?.order?.billing_email_hashed&&(e.em=wpmDataLayer.order.billing_email_hashed),wpmDataLayer?.user?.first_name?.facebook&&(e.fn=wpmDataLayer.user.first_name.facebook),wpmDataLayer?.order?.billing_first_name&&(e.fn=wpmDataLayer.order.billing_first_name.toLowerCase()),wpmDataLayer?.user?.last_name?.facebook&&(e.ln=wpmDataLayer.user.last_name.facebook),wpmDataLayer?.order?.billing_last_name&&(e.ln=wpmDataLayer.order.billing_last_name.toLowerCase()),wpmDataLayer?.user?.phone?.facebook&&(e.ph=wpmDataLayer.user.phone.facebook),wpmDataLayer?.order?.billing_phone&&(e.ph=wpmDataLayer.order.billing_phone.replace("+","")),wpmDataLayer?.user?.city?.facebook&&(e.ct=wpmDataLayer.user.city.facebook),wpmDataLayer?.order?.billing_city&&(e.ct=wpmDataLayer.order.billing_city.toLowerCase().replace(/ /g,"")),wpmDataLayer?.user?.state?.facebook&&(e.st=wpmDataLayer.user.state.facebook),wpmDataLayer?.order?.billing_state&&(e.st=wpmDataLayer.order.billing_state.toLowerCase().replace(/[a-zA-Z]{2}-/,"")),wpmDataLayer?.user?.postcode?.facebook&&(e.zp=wpmDataLayer.user.postcode.facebook),wpmDataLayer?.order?.billing_postcode&&(e.zp=wpmDataLayer.order.billing_postcode),wpmDataLayer?.user?.country?.facebook&&(e.country=wpmDataLayer.user.country.facebook),wpmDataLayer?.order?.billing_country&&(e.country=wpmDataLayer.order.billing_country.toLowerCase()),e},e.getFbRandomEventId=()=>(Math.random()+1).toString(36).substring(2),e.getFbUserData=()=>(t={...t,...e.getFbUserDataFromBrowser()},t),e.isFbAdvancedMatchingEnabled=()=>!!wpmDataLayer?.pixels?.facebook?.advanced_matching,e.setFbUserData=()=>{t=e.getFbUserDataFromBrowser()},e.getFbUserDataFromBrowser=()=>{let t={},a=e.getCookie("_fbp");e.isValidFbp(a)&&(t.fbp=a);let r=e.getCookie("_fbc")||e.retrieveData("fbclid");return e.isValidFbc(r)&&(t.fbc=r),e.isFbAdvancedMatchingEnabled()&&(wpmDataLayer?.user?.email?.facebook&&(t.em=wpmDataLayer.user.email.facebook),wpmDataLayer?.user?.phone?.facebook&&(t.ph=wpmDataLayer.user.phone.facebook),wpmDataLayer?.user?.first_name?.facebook&&(t.fn=wpmDataLayer.user.first_name.facebook),wpmDataLayer?.user?.last_name?.facebook&&(t.ln=wpmDataLayer.user.last_name.facebook),wpmDataLayer?.user?.city?.facebook&&(t.ct=wpmDataLayer.user.city.facebook),wpmDataLayer?.user?.state?.facebook&&(t.st=wpmDataLayer.user.state.facebook),wpmDataLayer?.user?.postcode?.facebook&&(t.zp=wpmDataLayer.user.postcode.facebook),wpmDataLayer?.user?.country?.facebook&&(t.country=wpmDataLayer.user.country.facebook),wpmDataLayer?.user?.id?.raw&&(t.external_id=wpmDataLayer.user.id.raw)),navigator.userAgent&&(t.client_user_agent=navigator.userAgent),e.retrieveData("ipv6")&&(t.client_ip_address=e.retrieveData("ipv6")),t},e.isFbpSet=()=>!!e.getCookie("_fbp"),e.isValidFbp=e=>new RegExp(/^fb\.[0-2]\.\d{13}\.\d{8,20}$/).test(e),e.isValidFbc=e=>new RegExp(/^fb\.[0-2]\.\d{13}\.[\da-zA-Z_-]{8,}/).test(e),e.fbGetProductDataForCapiEvent=e=>({content_type:"product",content_name:e.name,content_ids:[e.dyn_r_ids[wpmDataLayer.pixels.facebook.dynamic_remarketing.id_type]],value:parseFloat(e.quantity*e.price),currency:e.currency}),e.facebookContentIds=()=>{let e=[];return Object.values(wpmDataLayer.order.items).forEach((t=>{wpmDataLayer?.shop?.variations_output&&0!==t.variation_id?e.push(String(wpmDataLayer.products[t.variation_id].dyn_r_ids[wpmDataLayer.pixels.facebook.dynamic_remarketing.id_type])):e.push(String(wpmDataLayer.products[t.id].dyn_r_ids[wpmDataLayer.pixels.facebook.dynamic_remarketing.id_type]))})),e},e.trackCustomFacebookEvent=(t,a={})=>{try{if(!wpmDataLayer?.pixels?.facebook?.loaded)return;let r=e.getFbRandomEventId();fbq("trackCustom",t,a,{eventID:r}),pmw.console.log("Facebook Pixel: Custom event sent",t,a);let o={facebook:{event_name:t,event_id:r,user_data:e.getFbUserData(),event_source_url:window.location.href}};Object.keys(a).length&&(o.facebook.custom_data=a),e.sendEventPayloadToServer(o)}catch(e){console.error(e)}},e.fbGetContentIdsFromCart=()=>{let e=[];for(const t in wpmDataLayer.cart)e.push(wpmDataLayer.products[t].dyn_r_ids[wpmDataLayer.pixels.facebook.dynamic_remarketing.id_type]);return e},e.canFireFbq=()=>!!wpmDataLayer?.pixels?.facebook?.loaded&&!!e.consent.categories.get().marketing,e.canNotFireFbq=()=>!e.canFireFbq()}(window.wpm=window.wpm||{},jQuery)},666:(e,t,a)=>{a(624),a(843)},722:()=>{const e=wpm.prepareSelectors([".add_to_cart_button:not(.product_type_variable)",".ajax_add_to_cart",".single_add_to_cart_button"],"addToCart");jQuery(e).on("click adding_to_cart",((e,t)=>{try{let t=e?.target?.dataset?.quantity?e.target.dataset.quantity:null,a=e?.target?.dataset?.product_id?e.target.dataset.product_id:e?.target?.value?e.target.value:null;if("product"===wpmDataLayer.shop.page_type){if(void 0!==jQuery(e.currentTarget).attr("href")&&jQuery(e.currentTarget).attr("href").includes("add-to-cart"))return a||(a=jQuery(e.currentTarget).data("product_id")),void wpm.addProductToCart(a,1);if("simple"===wpmDataLayer.shop.product_type)return null===t&&(t=Number(jQuery(".input-text.qty").val())?Number(jQuery(".input-text.qty").val()):1),a||(a=jQuery(e.currentTarget).val()),a||(a=jQuery(".product").attr("id").replace("product-","")),void wpm.addProductToCart(a,t);if(["variable","variable-subscription"].indexOf(wpmDataLayer.shop.product_type)>=0)return null===t&&(t=Number(jQuery(".input-text.qty").val())?Number(jQuery(".input-text.qty").val()):1),a||(a=jQuery("[name='variation_id']").val()),void wpm.addProductToCart(a,t);if("grouped"===wpmDataLayer.shop.product_type)return void jQuery(".woocommerce-grouped-product-list-item").each(((e,r)=>{t=Number(jQuery(r).find(".input-text.qty").val())?Number(jQuery(r).find(".input-text.qty").val()):1;let o=jQuery(r).attr("class");a=wpm.getPostIdFromString(o),wpm.addProductToCart(a,t)}));if("bundle"===wpmDataLayer.shop.product_type)return null===t&&(t=Number(jQuery(".input-text.qty").val())?Number(jQuery(".input-text.qty").val()):1),a||(a=jQuery("input[name=add-to-cart]").val()),void wpm.addProductToCart(a,t);null===t&&(t=Number(jQuery(".input-text.qty").val())?Number(jQuery(".input-text.qty").val()):1),wpm.addProductToCart(a,t)}else null===t&&(t=1),a||(a=jQuery(e.currentTarget).data("product_id")?jQuery(e.currentTarget).data("product_id"):null),a||(a=jQuery(e.currentTarget).val()?jQuery(e.currentTarget).val():null),a&&t&&wpm.addProductToCart(a,t)}catch(e){console.error(e)}})),jQuery("a:not(.add_to_cart_button, .ajax_add_to_cart, .single_add_to_cart_button)").one("click",(e=>{try{if(jQuery(e.target).closest("a").attr("href")){let t=new URL(jQuery(e.currentTarget).attr("href"),window.location.origin);if(t.searchParams.has("add-to-cart")){let e=t.searchParams.get("add-to-cart");wpm.addProductToCart(e,1)}}}catch(e){console.error(e)}})),jQuery(".woocommerce-LoopProduct-link, .wc-block-grid__product, .product, .product-small, .type-product").on("click",(e=>{try{let t=jQuery(e.currentTarget).nextAll(".wpmProductId:first").data("id");if(t){if(t=wpm.getIdBasedOndVariationsOutputSetting(t),!t)throw Error("Wasn't able to retrieve a productId");if(wpmDataLayer.products&&wpmDataLayer.products[t]){let e=wpm.getProductDetailsFormattedForEvent(t);jQuery(document).trigger("pmw:select-item",e)}}}catch(e){console.error(e)}})),jQuery("#billing_email").on("input",(e=>{wpm.isEmail(jQuery(e.currentTarget).val())&&(wpm.fireCheckoutProgress(2),wpm.emailSelected=!0)})),jQuery("form.checkout").on("checkout_place_order_success",(()=>{!1===wpm.emailSelected&&wpm.fireCheckoutProgress(2),!1===wpm.paymentMethodSelected&&(wpm.fireCheckoutProgress(3),wpm.fireCheckoutOption(3,jQuery("input[name='payment_method']:checked").val())),wpm.fireCheckoutProgress(4),jQuery(document).trigger("pmw:place-order",{})})),jQuery(document).on("click","[name='update_cart']",(()=>{try{jQuery(".cart_item").each(((e,t)=>{const a=wpm.getProductIdByCartItemElement(t);if(!a)return void console.error("Pixel Manager: Wasn't able to retrieve a productId");const r=wpm.getProductQuantityByCartItemElement(t);r?0===r?wpm.removeProductFromCart(a):r<wpmDataLayer.cart[a].quantity?wpm.removeProductFromCart(a,wpmDataLayer.cart[a].quantity-r):r>wpmDataLayer.cart[a].quantity&&wpm.addProductToCart(a,r-wpmDataLayer.cart[a].quantity):console.error("Pixel Manager: Wasn't able to retrieve a quantity")}))}catch(e){console.error(e),wpm.getCartItemsFromBackend()}})),jQuery(".add_to_wishlist,.wl-add-to").on("click",(e=>{try{let t;if(jQuery(e.currentTarget).data("productid")?t=jQuery(e.currentTarget).data("productid"):jQuery(e.currentTarget).data("product-id")&&(t=jQuery(e.currentTarget).data("product-id")),!t)throw Error("Wasn't able to retrieve a productId");let a=wpm.getProductDetailsFormattedForEvent(t);jQuery(document).trigger("pmw:add-to-wishlist",a)}catch(e){console.error(e)}})),wpmDataLayer?.general?.lazy_load_pmw||wpm.registerShowVariationEventListener()},723:(e,t,a)=>{"use strict";a.r(t),a.d(t,{displayIP:()=>g,inRange:()=>m,isIP:()=>s,isRange:()=>l,isV4:()=>d,isV6:()=>p,searchIP:()=>u,storeIP:()=>u,version:()=>c});var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==a.g?a.g:"undefined"!=typeof self?self:{};function o(e,t){return e(t={exports:{}},t.exports),t.exports}var n=o((function(e){(function(){var t,a,r,o,n,i,s,c;a={},null!==e&&e.exports?e.exports=a:this.ipaddr=a,s=function(e,t,a,r){var o,n;if(e.length!==t.length)throw new Error("ipaddr: cannot match CIDR for objects with different lengths");for(o=0;r>0;){if((n=a-r)<0&&(n=0),e[o]>>n!==t[o]>>n)return!1;r-=a,o+=1}return!0},a.subnetMatch=function(e,t,a){var r,o,n,i,s;for(n in null==a&&(a="unicast"),t)for(!(i=t[n])[0]||i[0]instanceof Array||(i=[i]),r=0,o=i.length;r<o;r++)if(s=i[r],e.kind()===s[0].kind()&&e.match.apply(e,s))return n;return a},a.IPv4=function(){function e(e){var t,a,r;if(4!==e.length)throw new Error("ipaddr: ipv4 octet count should be 4");for(t=0,a=e.length;t<a;t++)if(!(0<=(r=e[t])&&r<=255))throw new Error("ipaddr: ipv4 octet should fit in 8 bits");this.octets=e}return e.prototype.kind=function(){return"ipv4"},e.prototype.toString=function(){return this.octets.join(".")},e.prototype.toNormalizedString=function(){return this.toString()},e.prototype.toByteArray=function(){return this.octets.slice(0)},e.prototype.match=function(e,t){var a;if(void 0===t&&(e=(a=e)[0],t=a[1]),"ipv4"!==e.kind())throw new Error("ipaddr: cannot match ipv4 address with non-ipv4 one");return s(this.octets,e.octets,8,t)},e.prototype.SpecialRanges={unspecified:[[new e([0,0,0,0]),8]],broadcast:[[new e([255,255,255,255]),32]],multicast:[[new e([224,0,0,0]),4]],linkLocal:[[new e([169,254,0,0]),16]],loopback:[[new e([127,0,0,0]),8]],carrierGradeNat:[[new e([100,64,0,0]),10]],private:[[new e([10,0,0,0]),8],[new e([172,16,0,0]),12],[new e([192,168,0,0]),16]],reserved:[[new e([192,0,0,0]),24],[new e([192,0,2,0]),24],[new e([192,88,99,0]),24],[new e([198,51,100,0]),24],[new e([203,0,113,0]),24],[new e([240,0,0,0]),4]]},e.prototype.range=function(){return a.subnetMatch(this,this.SpecialRanges)},e.prototype.toIPv4MappedAddress=function(){return a.IPv6.parse("::ffff:"+this.toString())},e.prototype.prefixLengthFromSubnetMask=function(){var e,t,a,r,o,n,i;for(i={0:8,128:7,192:6,224:5,240:4,248:3,252:2,254:1,255:0},e=0,o=!1,t=a=3;a>=0;t=a+=-1){if(!((r=this.octets[t])in i))return null;if(n=i[r],o&&0!==n)return null;8!==n&&(o=!0),e+=n}return 32-e},e}(),r="(0?\\d+|0x[a-f0-9]+)",o={fourOctet:new RegExp("^"+r+"\\."+r+"\\."+r+"\\."+r+"$","i"),longValue:new RegExp("^"+r+"$","i")},a.IPv4.parser=function(e){var t,a,r,n,i;if(a=function(e){return"0"===e[0]&&"x"!==e[1]?parseInt(e,8):parseInt(e)},t=e.match(o.fourOctet))return function(){var e,o,n,i;for(i=[],e=0,o=(n=t.slice(1,6)).length;e<o;e++)r=n[e],i.push(a(r));return i}();if(t=e.match(o.longValue)){if((i=a(t[1]))>4294967295||i<0)throw new Error("ipaddr: address outside defined range");return function(){var e,t;for(t=[],n=e=0;e<=24;n=e+=8)t.push(i>>n&255);return t}().reverse()}return null},a.IPv6=function(){function e(e,t){var a,r,o,n,i,s;if(16===e.length)for(this.parts=[],a=r=0;r<=14;a=r+=2)this.parts.push(e[a]<<8|e[a+1]);else{if(8!==e.length)throw new Error("ipaddr: ipv6 part count should be 8 or 16");this.parts=e}for(o=0,n=(s=this.parts).length;o<n;o++)if(!(0<=(i=s[o])&&i<=65535))throw new Error("ipaddr: ipv6 part should fit in 16 bits");t&&(this.zoneId=t)}return e.prototype.kind=function(){return"ipv6"},e.prototype.toString=function(){return this.toNormalizedString().replace(/((^|:)(0(:|$))+)/,"::")},e.prototype.toRFC5952String=function(){var e,t,a,r,o;for(r=/((^|:)(0(:|$)){2,})/g,o=this.toNormalizedString(),e=0,t=-1;a=r.exec(o);)a[0].length>t&&(e=a.index,t=a[0].length);return t<0?o:o.substring(0,e)+"::"+o.substring(e+t)},e.prototype.toByteArray=function(){var e,t,a,r,o;for(e=[],t=0,a=(o=this.parts).length;t<a;t++)r=o[t],e.push(r>>8),e.push(255&r);return e},e.prototype.toNormalizedString=function(){var e,t,a;return e=function(){var e,a,r,o;for(o=[],e=0,a=(r=this.parts).length;e<a;e++)t=r[e],o.push(t.toString(16));return o}.call(this).join(":"),a="",this.zoneId&&(a="%"+this.zoneId),e+a},e.prototype.toFixedLengthString=function(){var e,t,a;return e=function(){var e,a,r,o;for(o=[],e=0,a=(r=this.parts).length;e<a;e++)t=r[e],o.push(t.toString(16).padStart(4,"0"));return o}.call(this).join(":"),a="",this.zoneId&&(a="%"+this.zoneId),e+a},e.prototype.match=function(e,t){var a;if(void 0===t&&(e=(a=e)[0],t=a[1]),"ipv6"!==e.kind())throw new Error("ipaddr: cannot match ipv6 address with non-ipv6 one");return s(this.parts,e.parts,16,t)},e.prototype.SpecialRanges={unspecified:[new e([0,0,0,0,0,0,0,0]),128],linkLocal:[new e([65152,0,0,0,0,0,0,0]),10],multicast:[new e([65280,0,0,0,0,0,0,0]),8],loopback:[new e([0,0,0,0,0,0,0,1]),128],uniqueLocal:[new e([64512,0,0,0,0,0,0,0]),7],ipv4Mapped:[new e([0,0,0,0,0,65535,0,0]),96],rfc6145:[new e([0,0,0,0,65535,0,0,0]),96],rfc6052:[new e([100,65435,0,0,0,0,0,0]),96],"6to4":[new e([8194,0,0,0,0,0,0,0]),16],teredo:[new e([8193,0,0,0,0,0,0,0]),32],reserved:[[new e([8193,3512,0,0,0,0,0,0]),32]]},e.prototype.range=function(){return a.subnetMatch(this,this.SpecialRanges)},e.prototype.isIPv4MappedAddress=function(){return"ipv4Mapped"===this.range()},e.prototype.toIPv4Address=function(){var e,t,r;if(!this.isIPv4MappedAddress())throw new Error("ipaddr: trying to convert a generic ipv6 address to ipv4");return e=(r=this.parts.slice(-2))[0],t=r[1],new a.IPv4([e>>8,255&e,t>>8,255&t])},e.prototype.prefixLengthFromSubnetMask=function(){var e,t,a,r,o,n,i;for(i={0:16,32768:15,49152:14,57344:13,61440:12,63488:11,64512:10,65024:9,65280:8,65408:7,65472:6,65504:5,65520:4,65528:3,65532:2,65534:1,65535:0},e=0,o=!1,t=a=7;a>=0;t=a+=-1){if(!((r=this.parts[t])in i))return null;if(n=i[r],o&&0!==n)return null;16!==n&&(o=!0),e+=n}return 128-e},e}(),n="(?:[0-9a-f]+::?)+",c="%[0-9a-z]{1,}",i={zoneIndex:new RegExp(c,"i"),native:new RegExp("^(::)?("+n+")?([0-9a-f]+)?(::)?("+c+")?$","i"),transitional:new RegExp("^((?:"+n+")|(?:::)(?:"+n+")?)"+r+"\\."+r+"\\."+r+"\\."+r+"("+c+")?$","i")},t=function(e,t){var a,r,o,n,s,c;if(e.indexOf("::")!==e.lastIndexOf("::"))return null;for((c=(e.match(i.zoneIndex)||[])[0])&&(c=c.substring(1),e=e.replace(/%.+$/,"")),a=0,r=-1;(r=e.indexOf(":",r+1))>=0;)a++;if("::"===e.substr(0,2)&&a--,"::"===e.substr(-2,2)&&a--,a>t)return null;for(s=t-a,n=":";s--;)n+="0:";return":"===(e=e.replace("::",n))[0]&&(e=e.slice(1)),":"===e[e.length-1]&&(e=e.slice(0,-1)),{parts:t=function(){var t,a,r,n;for(n=[],t=0,a=(r=e.split(":")).length;t<a;t++)o=r[t],n.push(parseInt(o,16));return n}(),zoneId:c}},a.IPv6.parser=function(e){var a,r,o,n,s,c,d;if(i.native.test(e))return t(e,8);if((n=e.match(i.transitional))&&(d=n[6]||"",(a=t(n[1].slice(0,-1)+d,6)).parts)){for(r=0,o=(c=[parseInt(n[2]),parseInt(n[3]),parseInt(n[4]),parseInt(n[5])]).length;r<o;r++)if(!(0<=(s=c[r])&&s<=255))return null;return a.parts.push(c[0]<<8|c[1]),a.parts.push(c[2]<<8|c[3]),{parts:a.parts,zoneId:a.zoneId}}return null},a.IPv4.isIPv4=a.IPv6.isIPv6=function(e){return null!==this.parser(e)},a.IPv4.isValid=function(e){try{return new this(this.parser(e)),!0}catch(e){return!1}},a.IPv4.isValidFourPartDecimal=function(e){return!(!a.IPv4.isValid(e)||!e.match(/^(0|[1-9]\d*)(\.(0|[1-9]\d*)){3}$/))},a.IPv6.isValid=function(e){var t;if("string"==typeof e&&-1===e.indexOf(":"))return!1;try{return new this((t=this.parser(e)).parts,t.zoneId),!0}catch(e){return!1}},a.IPv4.parse=function(e){var t;if(null===(t=this.parser(e)))throw new Error("ipaddr: string is not formatted like ip address");return new this(t)},a.IPv6.parse=function(e){var t;if(null===(t=this.parser(e)).parts)throw new Error("ipaddr: string is not formatted like ip address");return new this(t.parts,t.zoneId)},a.IPv4.parseCIDR=function(e){var t,a,r;if((a=e.match(/^(.+)\/(\d+)$/))&&(t=parseInt(a[2]))>=0&&t<=32)return r=[this.parse(a[1]),t],Object.defineProperty(r,"toString",{value:function(){return this.join("/")}}),r;throw new Error("ipaddr: string is not formatted like an IPv4 CIDR range")},a.IPv4.subnetMaskFromPrefixLength=function(e){var t,a,r;if((e=parseInt(e))<0||e>32)throw new Error("ipaddr: invalid IPv4 prefix length");for(r=[0,0,0,0],a=0,t=Math.floor(e/8);a<t;)r[a]=255,a++;return t<4&&(r[t]=Math.pow(2,e%8)-1<<8-e%8),new this(r)},a.IPv4.broadcastAddressFromCIDR=function(e){var t,a,r,o,n;try{for(r=(t=this.parseCIDR(e))[0].toByteArray(),n=this.subnetMaskFromPrefixLength(t[1]).toByteArray(),o=[],a=0;a<4;)o.push(parseInt(r[a],10)|255^parseInt(n[a],10)),a++;return new this(o)}catch(e){throw new Error("ipaddr: the address does not have IPv4 CIDR format")}},a.IPv4.networkAddressFromCIDR=function(e){var t,a,r,o,n;try{for(r=(t=this.parseCIDR(e))[0].toByteArray(),n=this.subnetMaskFromPrefixLength(t[1]).toByteArray(),o=[],a=0;a<4;)o.push(parseInt(r[a],10)&parseInt(n[a],10)),a++;return new this(o)}catch(e){throw new Error("ipaddr: the address does not have IPv4 CIDR format")}},a.IPv6.parseCIDR=function(e){var t,a,r;if((a=e.match(/^(.+)\/(\d+)$/))&&(t=parseInt(a[2]))>=0&&t<=128)return r=[this.parse(a[1]),t],Object.defineProperty(r,"toString",{value:function(){return this.join("/")}}),r;throw new Error("ipaddr: string is not formatted like an IPv6 CIDR range")},a.isValid=function(e){return a.IPv6.isValid(e)||a.IPv4.isValid(e)},a.parse=function(e){if(a.IPv6.isValid(e))return a.IPv6.parse(e);if(a.IPv4.isValid(e))return a.IPv4.parse(e);throw new Error("ipaddr: the address has neither IPv6 nor IPv4 format")},a.parseCIDR=function(e){try{return a.IPv6.parseCIDR(e)}catch(t){try{return a.IPv4.parseCIDR(e)}catch(e){throw new Error("ipaddr: the address has neither IPv6 nor IPv4 CIDR format")}}},a.fromByteArray=function(e){var t;if(4===(t=e.length))return new a.IPv4(e);if(16===t)return new a.IPv6(e);throw new Error("ipaddr: the binary input is neither an IPv6 nor IPv4 address")},a.process=function(e){var t;return"ipv6"===(t=this.parse(e)).kind()&&t.isIPv4MappedAddress()?t.toIPv4Address():t}}).call(r)})),i=o((function(e,t){const a=function(e){if(!o(e))throw new Error("Invalid address: "+e);const t=(e=e.toLowerCase()).split(/\:\:/g);if(t.length>2)throw new Error("Invalid address: "+e);let a=[];if(1==t.length){if(a=e.split(/\:/g),8!==a.length)throw new Error("Invalid address: "+e)}else if(2==t.length){const e=t[0],r=t[1],o=e.split(/\:/g),n=r.split(/\:/g);for(let e in o)a[e]=o[e];for(let e=n.length;e>0;--e)a[7-(n.length-e)]=n[e-1]}for(let e=0;e<8;++e)void 0===a[e]&&(a[e]="0000"),a[e]=n(a[e],"0",4);return a.join(":")},r=function(e){if(!o(e))throw new Error("Invalid address: "+e);const t=(e=(e=(e=(e=(e=(e=a(e)).replace(/0000/g,"g")).replace(/\:000/g,":")).replace(/\:00/g,":")).replace(/\:0/g,":")).replace(/g/g,"0")).split(/\:/g);let r=!1,n=-1,i=0,s=-1,c=0;for(let e=0;e<8;++e){const a=t[e];let o="0"===a;o&&!r&&(s=e),!o&&r&&(c=e-s),c>1&&c>i&&(n=s,i=c),r="0"===a}return r&&(c=8-s),c>1&&c>i&&(n=s,i=c),n>=0&&i>1&&t.splice(n,i,"g"),e=(e=(e=(e=(e=t.join(":")).replace(/\:g\:/g,"::")).replace(/\:g/g,"::")).replace(/g\:/g,"::")).replace(/g/g,"::")},o=function(e){return/^[a-f0-9\\:]+$/gi.test(e)},n=function(e,t,a){const r=t.repeat(a);return e.length<r.length&&(e=r.substring(0,r.length-e.length)+e),e},i=function(e){return parseInt(e,2).toString(16)},s=function(e){const t=a(e).split(":");let r="";for(const e of t)r+=n(parseInt(e,16).toString(2),"0",16);return r},c=function(e){const t=[];for(let a=0;a<8;++a){const r=e.substr(16*a,16),o=n(i(r),"0",4);t.push(o)}return t.join(":")},d=function(e,t,a,i,d){if(!o(e))throw new Error("Invalid address: "+e);if(i*=1,a=(a*=1)||128,(t*=1)<1||a<1||t>128||a>128||t>a)throw new Error("Invalid masks.");const p=[],l=s(e).substr(0,t),m="0".repeat(128-a),u=Math.pow(2,a-t);for(let e=0;e<u&&!(i&&e>=i);++e){const o=n(e.toString(2),"0",a-t),i=c(l+o+m);d?p.push(r(i)):p.push(i)}return p},p=function(e,t,a,n){if(!o(e))throw new Error("Invalid address: "+e);if(a=(a*=1)||128,(t*=1)<1||a<1||t>128||a>128||t>a)throw new Error("Invalid masks.");const i=s(e).substr(0,t),d="0".repeat(128-a),p=i+"0".repeat(a-t)+d,l=i+"1".repeat(a-t)+d;return n?{start:r(c(p)),end:r(c(l)),size:Math.pow(2,a-t)}:{start:c(p),end:c(l),size:Math.pow(2,a-t)}},l=function(e,t,a,n,i){if(!o(e))throw new Error("Invalid address: "+e);if(a=(a*=1)||128,n=(n*=1)||1,(t*=1)<1||a<1||t>128||a>128||t>a)throw new Error("Invalid masks.");const d=[],p=s(e).substr(0,t),l="0".repeat(128-a),m=Math.pow(2,a-t);for(let e=0;e<m&&e<n;++e){let e="";for(let r=0;r<a-t;++r)e+=Math.floor(2*Math.random());const o=c(p+e+l);i?d.push(r(o)):d.push(o)}return d},m=function(e,t){if(!o(e))throw new Error("Invalid address: "+e);if((t*=1)<1||t>128||Math.floor(t/4)!=t/4)throw new Error("Invalid masks.");return a(e).replace(/:/g,"").split("").reverse().slice(0,(128-t)/4).join(".")};t.normalize=a,t.abbreviate=r,t.divideSubnet=d,t.range=p,t.randomSubnet=l,t.ptr=m}));i.normalize,i.abbreviate,i.divideSubnet,i.range,i.randomSubnet,i.ptr;function s(e){return n.isValid(e)}function c(e){try{var t=n.parse(e).kind();return"ipv4"===t?4:"ipv6"===t?6:0}catch(e){return 0}}function d(e){return 4===c(e)}function p(e){return 6===c(e)}function l(e){try{n.parseCIDR(e);return!0}catch(e){return!1}}function m(e,t){if("string"!=typeof t){if(t&&"object"==typeof t){for(var a in t)if(!0===m(e,t[a]))return!0;return!1}return!1}if(-1===t.indexOf("/"))return e=p(e)?i.normalize(e):e,s(t=p(t)?i.normalize(t):t)&&e===t;try{var r=t.split("/"),o=n.parse(e),c=n.parse(r[0]);return o.match(c,r[1])}catch(e){return!1}}function u(e){try{var t=n.parse(e),a=t.kind();return"ipv4"===a?e:"ipv6"===a?t.isIPv4MappedAddress()?t.toIPv4Address().toString():i.abbreviate(e):null}catch(e){return null}}function g(e){try{var t=n.parse(e),a=t.kind();return"ipv4"===a?e:"ipv6"===a?t.isIPv4MappedAddress()?t.toIPv4Address().toString():i.normalize(e):""}catch(e){return""}}},728:()=>{jQuery(document).on("pmw:load-pixels",(function(){wpmDataLayer?.pixels?.hotjar?.site_id&&!wpmDataLayer?.pixels?.hotjar?.loaded&&wpm.consent.canPixelBeFired("statistics","Hotjar")&&!wpmDataLayer?.pixels?.hotjar?.loaded&&wpm.load_hotjar_pixel()}))},729:(e,t,a)=>{a(821),a(606)},767:(e,t,a)=>{a(644),a(28)},783:()=>{!function(e){const t="restEndpointAvailable",a="pmw/v1/test/",r=0,o=10;e.emailSelected=!1,e.paymentMethodSelected=!1,e.isBelowRestErrorThreshold=()=>e.retrieveData(r)<=o,e.isRestEndpointAvailable=async()=>e.retrieveData(t)?e.retrieveData(t):await e.testEndpoint(),e.testEndpoint=async(r=e.root+a,o=t)=>{try{const t=await fetch(r,{method:"POST",mode:"cors",cache:"no-cache",keepalive:!0,redirect:"error"});if(!t.ok)return e.storeData(o,!1),pmw.console.error("Error testing the endpoint. Status: "+t.status+" | response: ",t),!1;const a=await t.json();return a.success?(e.storeData(o,!0),!0):(e.storeData(o,!1),pmw.console.error("Error testing the endpoint. Response JSON: "+JSON.stringify(a)),!1)}catch(t){return e.storeData(o,!1),pmw.console.error("Error testing the endpoint. Error: "+t),!1}},e.isWpmRestEndpointAvailable=(a=t)=>!!e.retrieveData(a),e.writeOrderIdToStorage=(t,a,r="thankyou_page")=>{let o=e.retrieveData("orderIds",!0)||[];o.push(t),e.storeData("orderIds",o,!0),"function"==typeof e.storeOrderIdOnServer&&e.storeOrderIdOnServer({orderId:t,orderKey:a,source:r})},e.isOrderIdStored=t=>{if(!wpmDataLayer.shop.order_duplication_prevention)return pmw.console.log("order duplication prevention is off"),!1;return(e.retrieveData("orderIds",!0)||[]).includes(t)},e.isEmail=e=>new RegExp('^(([^<>()\\[\\]\\\\.,;:\\s@"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@"]+)*)|(".+"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$').test(e),e.removeProductFromCart=async(t,a=null)=>{try{if(!t)throw Error("Wasn't able to retrieve a productId");if(!(t=e.getIdBasedOndVariationsOutputSetting(t)))throw Error("Wasn't able to retrieve a productId");let r;r=null==a?wpmDataLayer.cart[t].quantity:a,wpmDataLayer.cart[t]||await e.getProductsFromBackend([t]);let o=e.getProductDetailsFormattedForEvent(t,r);jQuery(document).trigger("pmw:remove-from-cart",o),null==a||wpmDataLayer.cart[t].quantity===a?delete wpmDataLayer.cart[t]:wpmDataLayer.cart[t].quantity=wpmDataLayer.cart[t].quantity-r,e.storeCartData(wpmDataLayer.cart)}catch(e){pmw.console.error("error: ",e)}},e.getIdBasedOndVariationsOutputSetting=e=>{try{return wpmDataLayer?.shop?.variations_output?e:wpmDataLayer.products[e].is_variation?wpmDataLayer.products[e].parent_id:e}catch(e){console.error(e)}},e.prepareSelectors=(e,t)=>(wpmDataLayer?.shop?.selectors?.[t]&&(e=e.concat(wpmDataLayer.shop.selectors[t]),(e=[...new Set(e)]).indexOf("document")>=0&&e.splice(e.indexOf("document"),1),e.indexOf("body")>=0&&(e=["body"])),e.join(", ")),e.addProductToCart=async(t,a)=>{a=Number(a);try{if(!t)throw Error("Wasn't able to retrieve a productId");if(!(t=e.getIdBasedOndVariationsOutputSetting(t)))throw Error("Wasn't able to retrieve a productId");wpmDataLayer?.products[t]||await e.getProductsFromBackend([t]);let r=e.getProductDetailsFormattedForEvent(t,a);jQuery(document).trigger("pmw:add-to-cart",r),wpmDataLayer?.cart[t]?wpmDataLayer.cart[t].quantity=wpmDataLayer.cart[t].quantity+a:("cart"in wpmDataLayer||(wpmDataLayer.cart={}),wpmDataLayer.cart[t]=e.getProductDetailsFormattedForEvent(t,a)),e.storeCartData(wpmDataLayer.cart)}catch(t){console.error(t),e.getCartItemsFromBackend()}},e.initCart=()=>{e.doesWooCommerceCartExist()||0===Object.keys(e.retrieveCartData()).length?e.retrieveCartData()?e.saveCartObjectToDataLayer(e.retrieveCartData()):(e.storeCartData({}),e.getCartItemsFromBackend()):e.storeCartData({})},e.getCartItemsFromBackend=()=>{try{fetch(e.ajax_url,{method:"POST",cache:"no-cache",body:new URLSearchParams({action:"pmw_get_cart_items"}),keepalive:!0,redirect:"error"}).then((e=>{if(e.ok)return e.json();throw Error("Error getting cart items from backend")})).then((t=>{if(!t.success)throw Error("Error getting cart items from backend");t.data.cart||(t.data.cart={}),e.saveCartObjectToDataLayer(t.data.cart),e.storeCartData(t.data.cart)}))}catch(e){console.error(e)}},e.getProductsFromBackend=async t=>{if(wpmDataLayer?.products&&(t=t.filter((e=>!(e in wpmDataLayer.products)))),t&&0!==t.length&&wpmDataLayer.general.page_id&&Array.isArray(t)&&0!==t.length){try{let a={action:"pmw_get_product_ids",page_id:wpmDataLayer.general.page_id,page_type:wpmDataLayer.shop.page_type,product_ids:t};const r=await e.isRestEndpointAvailable(),o=r?e.root+"pmw/v1/products/":e.ajax_url,n={method:"POST",cache:"no-cache",body:r?JSON.stringify(a):new URLSearchParams(a)};r&&(n.headers={"Content-Type":"application/json"});let i=await fetch(o,n);if(!i.ok)throw new Error("Error getting products from backend. Status: "+i.status+" Status text: "+i.statusText);let s=await i.json();if(!s.success)throw new Error("Error getting products from backend: "+s.data);wpmDataLayer.products=Object.assign({},wpmDataLayer.products,s.data)}catch(e){console.error(e)}return!0}},e.saveCartObjectToDataLayer=e=>{wpmDataLayer.cart=e,wpmDataLayer.products=Object.assign({},wpmDataLayer.products,e)},e.triggerViewItemEventPrep=async t=>{wpmDataLayer?.products[t]||await e.getProductsFromBackend([t]),wpmDataLayer?.products[t]?e.triggerViewItemEvent(t):(e.triggerViewItemEventNoProduct(),pmw.console.log(`Product with ID ${t} not found in the data layer and couldn't be retrieved from the backend. Triggering view-item event without product data.`))},e.triggerViewItemEvent=t=>{let a=e.getProductDetailsFormattedForEvent(t);a&&jQuery(document).trigger("pmw:view-item",a)},e.triggerViewItemEventNoProduct=()=>{jQuery(document).trigger("pmw:view-item")},e.fireCheckoutOption=(e,t=null,a=null)=>{let r={step:e,checkout_option:t,value:a};jQuery(document).trigger("pmw:checkout-option",r)},e.fireCheckoutProgress=e=>{let t={step:e};jQuery(document).trigger("pmw:checkout-progress",t)},e.getPostIdFromString=e=>{try{return e.match(/(post-)(\d+)/)[2]}catch(e){console.error(e)}},e.triggerViewItemList=t=>{if(!t)throw Error("Wasn't able to retrieve a productId");if(!(t=e.getIdBasedOndVariationsOutputSetting(t)))throw Error("Wasn't able to retrieve a productId");jQuery(document).trigger("pmw:view-item-list",e.getProductDataForViewItemEvent(t))},e.getProductDataForViewItemEvent=t=>{if(!t)throw Error("Wasn't able to retrieve a productId");try{if(wpmDataLayer.products[t])return e.getProductDetailsFormattedForEvent(t)}catch(e){console.error(e)}},e.getMainProductIdFromProductPage=()=>{try{return["simple","variable","grouped","composite","bundle"].indexOf(wpmDataLayer.shop.product_type)>=0&&jQuery(".wpmProductId:first").data("id")}catch(e){console.error(e)}},e.viewItemListTriggerTestMode=e=>{jQuery(e).css({position:"relative"}),jQuery(e).append('<div id="viewItemListTriggerOverlay"></div>'),jQuery(e).find("#viewItemListTriggerOverlay").css({"z-index":"10",display:"block",position:"absolute",height:"100%",top:"0",left:"0",right:"0",opacity:wpmDataLayer.shop.view_item_list_trigger.opacity,"background-color":wpmDataLayer.shop.view_item_list_trigger.background_color})},e.getSearchTermFromUrl=()=>{try{return new URLSearchParams(window.location.search).get("s")}catch(e){console.error(e)}};let n,i={};e.observerCallback=(t,a)=>{t.forEach((t=>{try{let r,o=jQuery(t.target).data("ioid");if(r=jQuery(t.target).next(".wpmProductId").length?jQuery(t.target).next(".wpmProductId").data("id"):jQuery(t.target).find(".wpmProductId").data("id"),!r)throw Error("wpmProductId element not found");t.isIntersecting?i[o]=setTimeout((()=>{e.triggerViewItemList(r),wpmDataLayer.shop.view_item_list_trigger.test_mode&&e.viewItemListTriggerTestMode(t.target),!1===wpmDataLayer.shop.view_item_list_trigger.repeat&&a.unobserve(t.target)}),wpmDataLayer.shop.view_item_list_trigger.timeout):(clearTimeout(i[o]),wpmDataLayer.shop.view_item_list_trigger.test_mode&&jQuery(t.target).find("#viewItemListTriggerOverlay").remove())}catch(e){console.error(e)}}))};let s=0;let c={nested:[],flat:[]};const d=e=>["product","type-product","product-item-inner",...c.nested].some((t=>jQuery(e).closest("."+t).length)),p=e=>["wc-block-grid__product","product","product-small","woocommerce-LoopProduct-link",...c.flat].some((t=>jQuery(e).prev().hasClass(t))),l=()=>(c=(()=>{let e={nested:[],flat:[]};try{const t=(e,t)=>jQuery(e).parents().length===jQuery(t).parents().length,a=(e,t)=>jQuery(e).parent().is(jQuery(t).parent())?{node:jQuery(e).parent(),type:"flat"}:{node:jQuery(e).parents().has(jQuery(t).parents()).first(),type:"nested"},r=e=>jQuery(e).find(".wpmProductId").length,o=(e,t)=>e.filter((e=>t.includes(e)))[0]||null,n=document.querySelectorAll(".wpmProductId");if(1===n.length)return e;for(let i=0;i<n.length-1;i++){if(!t(n[i],n[i+1])){i++;continue}let s=a(n[i],n[i+1]);if("nested"===s.type){let t=s.node.children().first().attr("class"),a=t?t.split(" "):[],r=s.node.children().first().next().attr("class");const n=o(a,r?r.split(" "):[]);e.nested.push(n)}else if("flat"===s.type){if(!s.node.children().first().attr("class"))continue;let t=s.node.children().first().attr("class").split(" ")[0];e.flat.push(t)}i=i+r(s.node)-1}return e.nested=[...new Set(e.nested)],e.flat=[...new Set(e.flat)],e}catch(t){return console.error(t),e}})(),jQuery(".wpmProductId").map((function(e,t){return d(t)?jQuery(t).parent():p(t)?jQuery(this).prev():jQuery(t).closest(".product").length?jQuery(t).closest(".product"):void 0})));e.startIntersectionObserverToWatch=()=>{try{e.urlHasParameter("vildemomode")&&(wpmDataLayer.shop.view_item_list_trigger.test_mode=!0),n=new IntersectionObserver(e.observerCallback,{root:null,threshold:wpmDataLayer.shop.view_item_list_trigger.threshold}),l().each(((e,t)=>{jQuery(t[0]).data("ioid",s++),n.observe(t[0])}))}catch(e){console.error(e)}},e.startProductsMutationObserverToWatch=()=>{try{let e=jQuery(".wpmProductId:eq(0)").parents().has(jQuery(".wpmProductId:eq(1)").parents()).first();e.length&&m.observe(e[0],{attributes:!0,childList:!0,characterData:!0})}catch(e){console.error(e)}};const m=new MutationObserver((e=>{e.forEach((e=>{let t=e.addedNodes;if(null!==t){jQuery(t).each(((e,t)=>{(d(t)||p(t))&&u(t)&&(jQuery(t).data("ioid",s++),n.observe(t))}))}}))}));let u=e=>!(!jQuery(e).find(".wpmProductId").length&&!jQuery(e).siblings(".wpmProductId").length);e.setCookie=(e,t="",a=null)=>{if(a){let r=new Date;r.setTime(r.getTime()+24*a*60*60*1e3);let o="expires="+r.toUTCString();document.cookie=e+"="+t+";"+o+";path=/"}else document.cookie=e+"="+t+";path=/"},e.getCookie=e=>{let t=e+"=",a=decodeURIComponent(document.cookie).split(";");for(let e=0;e<a.length;e++){let r=a[e];for(;" "==r.charAt(0);)r=r.substring(1);if(0==r.indexOf(t))return r.substring(t.length,r.length)}return""},e.getCookieThatContainsRegex=e=>{let t=document.cookie.split(";");for(let a=0;a<t.length;a++){let r=t[a].trim();if(e.test(r))return r}return!1},e.deleteCookie=t=>{e.setCookie(t,"",-1)},e.storeData=(t,a,r=!1,o=!1)=>{const n=o?t:"";let i=r?e.getPersistentData:e.getSessionData,s=r?e.setPersistentData:e.setSessionData,c=i(n);o?c=a:c[t]=a,s(c,n)},e.retrieveData=(t,a=!1,r=!1)=>{const o=r?t:"";let n=a?e.getPersistentData(o):e.getSessionData(o);return r?n:n[t]},e.deleteDataKey=(t,a=!1,r=!1)=>{const o=r?t:"";let n=a?e.getPersistentData:e.getSessionData,i=a?e.setPersistentData:e.setSessionData,s=n(o);if(r)try{a&&window.localStorage&&window.localStorage.removeItem(e.getPersistentDataKey(o)),!a&&window.sessionStorage&&window.sessionStorage.removeItem(e.getSessionDataKey(o))}catch(e){console.error(e)}else delete s[t],i(s,o)},e.storeCartData=t=>{e.storeData("cart",t,!1,!0)},e.retrieveCartData=()=>e.retrieveData("cart",!1,!0),e.getSessionDataKey=(e="")=>{const t="_pmw_session_data";return e?t+"_"+e:t},e.getPersistentDataKey=e=>{const t="_pmw_persistent_data";return e?t+"_"+e:t},e.getSessionData=(t="")=>{if(window.sessionStorage){let a=window.sessionStorage.getItem(e.getSessionDataKey(t));return null!==a?JSON.parse(a):{}}{let a=e.getCookie(e.getSessionDataKey(t));return""!==a?JSON.parse(a):{}}},e.setSessionData=(t,a="")=>{window.sessionStorage?window.sessionStorage.setItem(e.getSessionDataKey(a),JSON.stringify(t)):e.setCookie(e.getSessionDataKey(a),JSON.stringify(t))},e.getPersistentData=t=>{if(window.localStorage){let a=window.localStorage.getItem(e.getPersistentDataKey(t));return null!==a?JSON.parse(a):{}}{let a=e.getCookie(e.getPersistentDataKey(t));return""!==a?JSON.parse(a):{}}},e.setPersistentData=(t,a="")=>{window.localStorage?window.localStorage.setItem(e.getPersistentDataKey(a),JSON.stringify(t)):e.setCookie(e.getPersistentDataKey(a),JSON.stringify(t),400)},e.storeOrderIdOnServer=async t=>{try{let a;await e.isRestEndpointAvailable()?a=await fetch(e.root+"pmw/v1/pixels-fired/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({order_id:t.orderId,order_key:t.orderKey,source:t.source}),keepalive:!0,cache:"no-cache",redirect:"error"}):(pmw.console.log("REST API not available, falling back to AJAX"),a=await fetch(e.ajax_url,{method:"POST",body:new URLSearchParams({action:"pmw_purchase_pixels_fired",order_id:t.orderId,order_key:t.orderKey,source:t.source}),keepalive:!0,redirect:"error"}));const r=await a.json();r.success?pmw.console.log("",r.data):pmw.console.error("",r.data)}catch(e){console.error(e)}},e.getProductIdByCartItemElement=t=>{const a=jQuery(t).find(".product-remove").find("a").attr("href");if(a)return e.getProductIdByCartItemKeyUrl(new URL(a));const r=jQuery(t).find("[data-product_id]").first().attr("data-product_id");return r||null},e.getProductQuantityByCartItemElement=e=>jQuery(e).find(".qty").val()||null,e.getProductIdByCartItemKeyUrl=e=>{let t,a=new URLSearchParams(e.search).get("remove_item");return t=0===wpmDataLayer.cart_item_keys[a].variation_id?wpmDataLayer.cart_item_keys[a].product_id:wpmDataLayer.cart_item_keys[a].variation_id,t},e.getAddToCartLinkProductIds=()=>jQuery("a").map((function(){let e=jQuery(this).attr("href");if(e&&e.includes("?add-to-cart=")){let t=e.match(/(add-to-cart=)(\d+)/);if(t)return t[2]}})).get(),e.getProductDetailsFormattedForEvent=(e,t=1)=>{if(!wpmDataLayer.products[e])return null;let a={id:e.toString(),dyn_r_ids:wpmDataLayer.products[e].dyn_r_ids,name:wpmDataLayer.products[e].name,list_name:wpmDataLayer.shop.list_name,brand:wpmDataLayer.products[e].brand,category:wpmDataLayer.products[e].category,variant:wpmDataLayer.products[e].variant,list_position:wpmDataLayer.products[e].position,quantity:t,price:wpmDataLayer.products[e].price,currency:wpmDataLayer.shop.currency,is_variable:wpmDataLayer.products[e].is_variable,is_variation:wpmDataLayer.products[e].is_variation,parent_id:wpmDataLayer.products[e].parent_id};return a.is_variation&&(a.parent_id_dyn_r_ids=wpmDataLayer.products[e].parent_id_dyn_r_ids),a},e.getClidFromBrowser=(t="gclid")=>{let a;if(a={gclid:"_gcl_aw",dclid:"_gcl_dc"},e.getCookie(a[t])){return e.getCookie(a[t]).match(/(GCL.[\d]*.)(.*)/)[2]}return""},e.getUserAgent=()=>navigator.userAgent,e.getViewPort=()=>({width:Math.max(document.documentElement.clientWidth||0,window.innerWidth||0),height:Math.max(document.documentElement.clientHeight||0,window.innerHeight||0)}),e.consoleLogVersion=()=>{console.log(wpmDataLayer.version)},e.loadScriptAndCacheIt=(e,t)=>{let a={dataType:"script",cache:!0,url:e};return new Promise(((r,o)=>{jQuery.ajax(a).done((()=>{pmw.console.log("Successfully loaded primary script: "+e),r()})).fail((()=>{if(t){pmw.console.log("Loading fallback script: "+t);let e={dataType:"script",cache:!0,url:t};jQuery.ajax(e).done((()=>{pmw.console.log("Successfully loaded fallback script: "+t),r()})).fail((()=>o(new Error("Both primary and fallback scripts failed to load."))))}else o(new Error("Primary script failed to load, and no fallback URL provided."))}))}))},e.getOrderItemPrice=e=>(e.total+e.total_tax)/e.quantity,e.hasLoginEventFired=()=>{let t=e.getSessionData();return t?.loginEventFired},e.setLoginEventFired=()=>{let t=e.getSessionData();t.loginEventFired=!0,e.setSessionData(t)},e.pageLoaded=async()=>new Promise((e=>{!function t(){if("complete"===document.readyState)return e();setTimeout(t,50)}()})),e.pageReady=()=>new Promise((e=>{!function t(){if("interactive"===document.readyState||"complete"===document.readyState)return e();setTimeout(t,50)}()})),e.isMiniCartActive=()=>(window.sessionStorage&&Object.keys(window.sessionStorage).forEach((e=>{if(e.includes("wc_fragments"))return!0})),!1),e.doesWooCommerceCartExist=()=>document.cookie.includes("woocommerce_items_in_cart"),e.urlHasParameter=e=>new URLSearchParams(window.location.search).has(e),e.getUrlParameter=e=>new URLSearchParams(window.location.search).get(e),e.hashAsync=(e,t)=>crypto.subtle.digest(e,new TextEncoder("utf-8").encode(t)).then((e=>Array.prototype.map.call(new Uint8Array(e),(e=>("00"+e.toString(16)).slice(-2))).join(""))),e.getCartValue=()=>{let e=0;if(wpmDataLayer?.cart)for(const t in wpmDataLayer.cart){let a=wpmDataLayer.cart[t];e+=a.quantity*a.price}return e},e.doesUrlContainPatterns=e=>{for(const t of e)if(new RegExp(t).test(window.location.href))return!0;return!1},e.excludeDomainFromTracking=()=>{let e=["appspot.com","translate.google.com"];return wpmDataLayer?.general?.exclude_domains&&(e=[...e,...wpmDataLayer.general.exclude_domains]),!!e.some((e=>window.location.href.includes(e)))&&(console.debug("Pixel Manager: Aborted due to excluded domain"),!0)},e.getRandomEventId=(e=16)=>{let t="";for(;t.length<e;)t+=(Math.random()+1).toString(36).substring(2);return t.substring(0,e)},e.pmwConsoleMessage=()=>{let e="Pixel Manager for WooCommerce: ";e+=wpmDataLayer.version.pro?"pro":"free",e+=" | distro: "+wpmDataLayer.version.distro,"fms"===wpmDataLayer.version.distro&&wpmDataLayer.version.pro&&(e+=" | active license: "+(wpmDataLayer.version.eligible_for_updates?"yes":"no")),e+=" | version: "+wpmDataLayer.version.number,!0===wpmDataLayer.version.show?console.log(e):pmw.console.log(e)},e.canLoadPremiumFeatures=()=>"fms"===wpmDataLayer.version.distro&&wpmDataLayer.version.pro&&wpmDataLayer.version.eligible_for_updates||"wcm"===wpmDataLayer.version.distro;let g=!1;e.triggerPmwDomReadyEvent=()=>{g||("complete"!==document.readyState?(jQuery((()=>{w()})),document.addEventListener("DOMContentLoaded",(()=>{w()}),!0)):w())};const w=()=>{g||(document.dispatchEvent(new Event("pmw:dom:ready")),g=!0)};e.getEmailFromTarget=t=>{if(t.href){let a=t.href.replace("mailto:","");if(a.indexOf("?")>-1&&(a=a.split("?")[0]),a=a.replace(/\s/g,""),a&&e.isEmail(a))return a}return""},e.sendEventPayloadToServer=t=>{"function"==typeof e.sendEventPayloadToServerPremium&&e.sendEventPayloadToServerPremium(t)},e.waitForPixelsAndTriggerPageView=async()=>new Promise((e=>{if(!wpmDataLayer?.pixels)return pmw.console.error("wpmDataLayer or wpmDataLayer.pixels is not defined, triggering page view event immediately."),document.dispatchEvent(new Event("pmw:page-view")),void e();let t=wpmDataLayer?.general?.server_2_server?.pageview_event_s2s?.pixels||[];if(t=t.filter((e=>wpmDataLayer.pixels[e])),0===t.length)return pmw.console.log("No specific pixels to wait for, triggering page view event."),document.dispatchEvent(new Event("pmw:page-view")),void e();const a=()=>{t.every((e=>!wpmDataLayer.pixels[e]||wpmDataLayer.pixels[e].loaded))?(document.dispatchEvent(new Event("pmw:page-view")),e()):setTimeout(a,100)};a()})),e.isWooCommerceActive=()=>!!wpmDataLayer.shop,e.getProductIdForSpecificPixel=(e,t)=>String(e.dyn_r_ids[wpmDataLayer.pixels[t].dynamic_remarketing.id_type]),e.isInWooCommerceSesssion=()=>{if(wpmDataLayer?.shop?.session_active)return!0;let t=new RegExp("woocommerce_cart_hash");return!!e.getCookieThatContainsRegex(t)},e.isNotInWooCommerceSesssion=()=>!e.isInWooCommerceSesssion(),e.makeFullUrl=e=>(e=e.trim(),/^(http:\/\/|https:\/\/)/.test(e)?e:"https://"+(e=e.replace(/^\/+/,""))),e.registerShowVariationEventListener=()=>{let t=null;jQuery(".single_variation_wrap").on("show_variation",((a,r)=>{try{let a=e.getIdBasedOndVariationsOutputSetting(r.variation_id);if(!a)throw Error("Wasn't able to retrieve a productId");t!==r.variation_id&&(e.triggerViewItemEventPrep(a),t=r.variation_id)}catch(e){console.error(e)}}))},e.initializeCommandQueue=()=>{const e=window._pmwq=window._pmwq||[];e.forEach((function(e){"function"==typeof e&&e()})),e.push=function(e){"function"==typeof e&&e()}}}(window.wpm=window.wpm||{},jQuery)},821:()=>{!function(e){e.getGoogleAdsConversionIdentifiersWithLabel=()=>{let e=[];return wpmDataLayer?.pixels?.google?.ads?.conversion_ids&&Object.entries(wpmDataLayer.pixels.google.ads.conversion_ids).forEach((([t,a])=>{a&&e.push(t+"/"+a)})),e},e.getGoogleAdsConversionIdentifiers=()=>{let e=[];return Object.keys(wpmDataLayer.pixels.google.ads.conversion_ids).forEach((t=>{e.push(t)})),e},e.getGoogleAdsRegularOrderItems=()=>{let e=[];return Object.values(wpmDataLayer.order.items).forEach((t=>{let a;a={quantity:t.quantity,price:t.price,google_business_vertical:wpmDataLayer.pixels.google.ads.google_business_vertical},wpmDataLayer?.shop?.variations_output&&0!==t.variation_id?(a.id=String(wpmDataLayer.products[t.variation_id].dyn_r_ids[wpmDataLayer.pixels.google.ads.dynamic_remarketing.id_type]),e.push(a)):(a.id=String(wpmDataLayer.products[t.id].dyn_r_ids[wpmDataLayer.pixels.google.ads.dynamic_remarketing.id_type]),e.push(a))})),e}}(window.wpm=window.wpm||{},jQuery)},843:()=>{jQuery(document).on("pmw:view-item-list",((e,t)=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;const e={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,items:[wpm.ga4GetFullProductItemData(t)],item_list_name:wpmDataLayer.shop.list_name,item_list_id:wpmDataLayer.shop.list_id};wpm.gtagLoaded().then((()=>{gtag("event","view_item_list",e),pmw.console.log("Google Analytics: view_item_list event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:select-item",((e,t)=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;const e={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,items:[wpm.ga4GetFullProductItemData(t)]};wpm.gtagLoaded().then((()=>{gtag("event","select_item",e),pmw.console.log("Google Analytics: select_item event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:add-to-cart",((e,t)=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;const e={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,currency:wpmDataLayer.shop.currency,value:t.price*t.quantity,items:[wpm.ga4GetFullProductItemData(t)]};wpm.gtagLoaded().then((()=>{gtag("event","add_to_cart",e),pmw.console.log("Google Analytics: add_to_cart event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:view-item",((e,t=null)=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;let e={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id};t&&(e.currency=wpmDataLayer.shop.currency,e.items=[wpm.ga4GetFullProductItemData(t)]),wpm.gtagLoaded().then((()=>{gtag("event","view_item",e),pmw.console.log("Google Analytics: view_item event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:add-to-wishlist",((e,t)=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;const e={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,currency:wpmDataLayer.shop.currency,value:t.price*t.quantity,items:[wpm.ga4GetFullProductItemData(t)]};wpm.gtagLoaded().then((()=>{gtag("event","add_to_wishlist",e),pmw.console.log("Google Analytics: add_to_wishlist event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:remove-from-cart",((e,t)=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;const e={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,currency:wpmDataLayer.shop.currency,value:t.price*t.quantity,items:[wpm.ga4GetFullProductItemData(t)]};wpm.gtagLoaded().then((()=>{gtag("event","remove_from_cart",e),pmw.console.log("Google Analytics: remove_from_cart event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:begin-checkout",(()=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;let e={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,currency:wpmDataLayer.shop.currency,value:wpm.getCartValue(),items:wpm.getCartItemsGa4()};wpm.gtagLoaded().then((()=>{gtag("event","begin_checkout",e),pmw.console.log("Google Analytics: begin_checkout event sent",e)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:add-shipping-info",((e,t)=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;const e="add_shipping_info";let a={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,currency:wpmDataLayer.shop.currency,value:wpm.getCartValue(),items:wpm.getCartItemsGa4()};t?.shippingTier?.text&&(a.shipping_tier=t.shippingTier.text),wpm.gtagLoaded().then((()=>{gtag("event",e,a),pmw.console.log(`Google Analytics: ${e} event sent`,a)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:add-payment-info",((e,t)=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;const e="add_payment_info";let a={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,currency:wpmDataLayer.shop.currency,value:wpm.getCartValue(),items:wpm.getCartItemsGa4()};t?.paymentType?.text&&(a.payment_type=t.paymentType.text),wpm.gtagLoaded().then((()=>{gtag("event",e,a),pmw.console.log(`Google Analytics: ${e} event sent`,a)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:view-cart",(()=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;if(jQuery.isEmptyObject(wpmDataLayer.cart))return;let e=[],t=null;Object.values(wpmDataLayer.cart).forEach((a=>{e.push(wpm.ga4GetFullProductItemData(a)),t+=a.quantity*a.price}));const a={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,currency:wpmDataLayer.shop.currency,value:t.toFixed(2),items:e};wpm.gtagLoaded().then((()=>{gtag("event","view_cart",a),pmw.console.log("Google Analytics: view_cart event sent",a)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:search",(()=>{try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;let e=[];Object.values(wpmDataLayer.products).forEach((t=>{e.push(wpm.ga4GetFullProductItemData(t))}));const t={send_to:wpmDataLayer.pixels.google.analytics.ga4.measurement_id,search_term:wpm.getSearchTermFromUrl(),items:e};wpm.gtagLoaded().then((()=>{gtag("event","view_search_results",t),pmw.console.log("Google Analytics: view_search_results event sent",t)}))}catch(e){console.error(e)}})),jQuery(document).on("pmw:view-order-received-page",(function(){try{if(!wpmDataLayer?.pixels?.google?.analytics?.ga4?.measurement_id)return;if(wpmDataLayer?.pixels?.google?.analytics?.ga4?.mp_active)return;if(!wpm.googleConfigConditionsMet({type:"statistics"}))return;const e={send_to:[wpmDataLayer.pixels.google.analytics.ga4.measurement_id],transaction_id:wpmDataLayer.order.number,affiliation:wpmDataLayer.order.affiliation,currency:wpmDataLayer.order.currency,value:wpmDataLayer.order.value.total,discount:wpmDataLayer.order.discount,tax:wpmDataLayer.order.tax,shipping:wpmDataLayer.order.shipping,coupon:wpmDataLayer.order.coupon,items:wpm.getGA4OrderItems()};wpmDataLayer?.order?.custom_parameters&&Object.keys(wpmDataLayer.order.custom_parameters).forEach((t=>{e[t]=wpmDataLayer.order.custom_parameters[t]})),wpm.gtagLoaded().then((function(){gtag("event","purchase",e),pmw.console.log("Google Analytics: purchase event sent",e)}))}catch(e){console.error(e)}}))},857:(e,t,a)=>{"use strict";a.r(t),a.d(t,{acceptAll:()=>r,getConsentStateFor:()=>d,processExternalGcmConsentUpdate_experimental:()=>c,revokeAll:()=>o,updateSelectively:()=>n});const r=({duration:e=null}={})=>{const t={statistics:!0,marketing:!0,preferences:!0,necessary:!0,duration:e};s(t),i(),document.dispatchEvent(new CustomEvent("pmw:consent:update",{detail:t}))},o=({duration:e=null}={})=>{const t={statistics:!1,marketing:!1,preferences:!1,necessary:!0,duration:e};s(t),document.dispatchEvent(new CustomEvent("pmw:consent:update",{detail:t}))},n=({statistics:e=wpm.consent.categories.get().statistics,marketing:t=wpm.consent.categories.get().marketing,preferences:a=wpm.consent.categories.get().preferences,necessary:r=wpm.consent.categories.get().necessary,duration:o=null})=>{void 0!==e&&void 0!==t&&void 0!==a&&void 0!==r||console.log("pmw.consent.api.updateSelectively: It is recommended to pass all consent types. - statistics, marketing, preferences");let n={statistics:e,marketing:t,preferences:a,necessary:r,duration:o};s(n),i(),document.dispatchEvent(new CustomEvent("pmw:consent:update",{detail:n}))},i=()=>{document.dispatchEvent(new Event("pmw:load-pixels"))},s=e=>{wpm.consent.categories.set(e),(({statistics:e,marketing:t,preferences:a,necessary:r,duration:o=null})=>{const n={statistics:e,marketing:t,preferences:a,necessary:r};null!==o?wpm.setCookie("pmw_cookie_consent",JSON.stringify(n),o):wpm.storeData("pmw_cookie_consent",n,!0)})(e),pmw.console.log("Updated consent state",e)},c=e=>{if("consent"!==e[0])return;if("update"!==e[1])return;if("pmw"===e[2]?.source)return;pmw.console.log("processExternalGcmConsentUpdate",e);let t=wpm.consent.categories.get(),a={statistics:void 0!==e[2].analytics_storage?"granted"===e[2].analytics_storage:t.statistics,marketing:void 0!==e[2].ad_storage?"granted"===e[2].ad_storage:t.marketing,preferences:(void 0!==e[2].functionality_storage?"granted"===e[2].functionality_storage:t.preferences)||(void 0!==e[2].personalization_storage?"granted"===e[2].personalization_storage:t.preferences),necessary:void 0!==e[2].security_storage?"granted"===e[2].security_storage:t.necessary};s(a),i()},d=e=>wpm.consent.categories.get()[e]},931:(e,t,a)=>{a(666)}},a={};function r(e){var o=a[e];if(void 0!==o)return o.exports;var n=a[e]={exports:{}};return t[e].call(n.exports,n,n.exports,r),n.exports}r.d=(e,t)=>{for(var a in t)r.o(t,a)&&!r.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";(async()=>{r(263),r(547),await wpm.jQueryExists(),await wpm.wpmDataLayerFullyLoaded(),r(189),wpm.pmwConsoleMessage(),await wpm.consent.load(),jQuery((()=>{r(722)})),r(196),r(282),wpm.loadWcHooksFunctions(),wpm.excludeDomainFromTracking()||(r(62),r(767),r(155),(()=>{pmw.consentAcceptAll=e=>{console.error("The function pmw.consentAcceptAll is deprecated since version 1.41.1 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw.consent.api.acceptAll()"),pmw.consent.api.acceptAll(e)},pmw.consentRevokeAll=e=>{console.error("The function pmw.consentRevokeAll is deprecated since version 1.41.1 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw.consent.api.revokeAll()"),pmw.consent.api.revokeAll(e)},pmw.consentAdjustSelectively=t=>{console.error("The function pmw.consentAdjustSelectively is deprecated since version 1.41.1 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw.consent.api.adjustSelectively()"),t=e(t),pmw.consent.api.updateSelectively(t)};const e=e=>(e.analytics&&(e.statistics=e.analytics,delete e.analytics),e.ads&&(e.marketing=e.ads,delete e.ads),e);jQuery(document).on("wpmBeginCheckout",(()=>{console.error("The event wpmBeginCheckout is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:begin-checkout"),jQuery(document).trigger("pmw:begin-checkout")})),jQuery(document).on("wpmAddToCart",((e,t)=>{console.error("The event wpmAddToCart is deprecated since version 1.42.8 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:add-to-cart"),jQuery(document).trigger("pmw:add-to-cart",t)})),jQuery(document).on("wpmOrderReceivedPage",(()=>{console.error("The event wpmOrderReceivedPage is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:view-order-received-page"),jQuery(document).trigger("pmw:view-order-received-page")})),jQuery(document).on("wpmLogin",(()=>{console.error("The event wpmLogin is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:login"),jQuery(document).trigger("pmw:login")})),jQuery(document).on("wpmAddToWishlist",((e,t)=>{console.error("The event wpmAddToWishlist is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:add-to-wishlist"),jQuery(document).trigger("pmw:add-to-wishlist",t)})),jQuery(document).on("pmwEvent:Search",(()=>{console.error("The event pmwEvent:Search is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:search"),jQuery(document).trigger("pmw:search")})),jQuery(document).on("wpmViewItem",((e,t)=>{console.error("The event wpmViewItem is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:view-item"),jQuery(document).trigger("pmw:view-item",t)})),jQuery(document).on("wpmViewItemList",((e,t)=>{console.error("The event wpmViewItemList is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:view-item-list"),jQuery(document).trigger("pmw:view-item-list",t)})),jQuery(document).on("wpmSelectItem",((e,t)=>{console.error("The event wpmSelectItem is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:select-item"),jQuery(document).trigger("pmw:select-item",t)})),jQuery(document).on("wpmRemoveFromCart",((e,t)=>{console.error("The event wpmRemoveFromCart is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:remove-from-cart"),jQuery(document).trigger("pmw:remove-from-cart",t)})),jQuery(document).on("wpmViewCart",(()=>{console.error("The event wpmViewCart is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:view-cart"),jQuery(document).trigger("pmw:view-cart")})),jQuery(document).on("wpmCategory",((e,t)=>{console.error("The event wpmCategory is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:view-category"),jQuery(document).trigger("pmw:view-category",t)})),jQuery(document).on("wpmEverywhereElse",(()=>{console.error("The event wpmEverywhereElse is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:everywhere-else"),jQuery(document).trigger("pmw:everywhere-else")})),jQuery(document).on("wpmPlaceOrder",(()=>{console.error("The event wpmPlaceOrder is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:place-order"),jQuery(document).trigger("pmw:place-order")})),jQuery(document).on("wpmFireCheckoutOption",((e,t)=>{console.error("The event wpmFireCheckoutOption is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:fire-checkout-option"),jQuery(document).trigger("pmw:fire-checkout-option",t)})),jQuery(document).on("wpmFireCheckoutProgress",((e,t)=>{console.error("The event wpmFireCheckoutProgress is deprecated since version 1.42.9 of the Pixel Manager and will be removed in the future. It has been replaced by: pmw:fire-checkout-progress"),jQuery(document).trigger("pmw:fire-checkout-progress",t)}))})(),document.dispatchEvent(new Event("pmw:load-pixels")),document.dispatchEvent(new Event("wpmLoad")),await wpm.pageLoaded(),document.dispatchEvent(new Event("pmw:ready")),wpm.triggerPmwDomReadyEvent(),wpm.initializeCommandQueue(),wpm.checkLibraryVersion())})()})()})();
//# sourceMappingURL=wpm-public.p1.min.js.map