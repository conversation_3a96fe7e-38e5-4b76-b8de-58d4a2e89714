<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    38039 => 'Kyivstar',
    38050 => 'Vodafone',
    38063 => 'lifecell',
    38066 => 'Vodafone',
    38067 => 'Kyivstar',
    38068 => 'Kyivstar',
    38073 => 'lifecell',
    38075 => 'Vodafone',
    38077 => 'Kyivstar',
    38079 => 'J&Y',
    38091 => 'TriMob',
    38092 => 'PEOPLEnet',
    38093 => 'lifecell',
    38094 => 'Intertelecom',
    38095 => 'Vodafone',
    38096 => 'Kyivstar',
    38097 => 'Kyivstar',
    38098 => 'Kyivstar',
    38099 => 'Vodafone',
];
