<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    35840 => 'Telia',
    35841 => 'DNA',
    35842 => 'Telia',
    3584320 => 'Cuuma',
    3584321 => 'Cuuma',
    3584322 => 'Benemen Oy',
    3584323 => 'Top Connect OU',
    3584324 => 'Nord Connect SIA',
    3584325 => 'NETTIA',
    3584326 => 'Lancelot',
    358436 => 'DNA',
    358438 => 'DNA',
    35844 => 'DNA',
    358450 => 'Telia',
    358451 => 'Elisa',
    358452 => 'Elisa',
    358453 => 'Elisa',
    3584540 => 'MobiWeb',
    3584541 => 'AinaCom',
    3584542 => 'Nokia',
    3584543 => 'Nokia',
    3584544 => 'Nokia',
    3584545 => 'Interactive Digital Media',
    3584546 => 'NextGen Mobile / CardBoardFish',
    3584547 => 'SMS Provider Corp',
    3584548 => 'Voxbone',
    3584549 => 'Beepsend',
    3584550 => 'Suomen Virveverkko',
    3584552 => 'Suomen Virveverkko',
    3584554 => 'Suomen Virveverkko',
    3584555 => 'Nokia Solutions and Networks',
    3584556 => 'Liikennevirasto',
    3584557 => 'Compatel',
    3584558 => 'Suomen Virveverkko',
    3584559 => 'MI',
    358456 => 'Elisa',
    3584570 => 'AMT',
    3584571 => 'Tismi',
    3584572 => 'Telavox AB',
    3584573 => 'AMT',
    3584574 => 'DNA',
    3584575 => 'AMT',
    3584576 => 'DNA',
    3584577 => 'DNA',
    3584578 => 'DNA',
    3584579 => 'DNA',
    358458 => 'Elisa',
    35846 => 'Elisa',
    35849 => 'Elisa',
    35850 => 'Elisa',
];
