<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    61400 => 'Telstra',
    61401 => 'Optus',
    61402 => 'Optus',
    61403 => 'Optus',
    61404 => 'Vodafone',
    61405 => 'Vodafone',
    61406 => 'Vodafone',
    61407 => 'Telstra',
    61408 => 'Telstra',
    61409 => 'Telstra',
    61410 => 'Vodafone',
    61411 => 'Optus',
    61412 => 'Optus',
    61413 => 'Optus',
    61414 => 'Vodafone',
    61415 => 'Vodafone',
    61416 => 'Vodafone',
    61417 => 'Telstra',
    61418 => 'Telstra',
    61419 => 'Telstra',
    61420 => 'Vodafone',
    6142000 => 'Rail Corporation NSW',
    6142001 => 'Rail Corporation NSW',
    6142002 => 'Dialogue Communications',
    6142003 => 'MessageBird',
    6142004 => 'Symbio Networks',
    6142010 => 'Pivotel Satellite',
    6142011 => 'Compatel Limited',
    6142012 => 'Soul Pattinson',
    61421 => 'Optus',
    61422 => 'Optus',
    61423 => 'Optus',
    61424 => 'Vodafone',
    61425 => 'Vodafone',
    61426 => 'Vodafone',
    61427 => 'Telstra',
    61428 => 'Telstra',
    61429 => 'Telstra',
    61430 => 'Vodafone',
    61431 => 'Optus',
    61432 => 'Optus',
    61433 => 'Vodafone',
    61434 => 'Optus',
    61435 => 'Optus',
    61436 => 'Telstra',
    61437 => 'Telstra',
    61438 => 'Telstra',
    61439 => 'Telstra',
    61440 => 'MessageBird',
    614400 => 'Aazad distribution discretionary trust',
    614444 => 'Telstra',
    614445 => 'Telstra',
    61447 => 'Telstra',
    61448 => 'Telstra',
    61449 => 'Vodafone',
    614493 => 'Soul Pattinson',
    61450 => 'Vodafone',
    61451 => 'Vodafone',
    61452 => 'Vodafone',
    61455 => 'Telstra',
    61456 => 'Telstra',
    61457 => 'Telstra',
    61458 => 'Telstra',
    61459 => 'Telstra',
    6146 => 'Telstra',
    61466 => 'Optus',
    61468 => 'Optus',
    61469 => 'Lycamobile',
    614700 => 'Lycamobile',
    614701 => 'Lycamobile',
    614702 => 'Lycamobile',
    614703 => 'Lycamobile',
    614704 => 'Lycamobile',
    614705 => 'Lycamobile',
    614706 => 'Lycamobile',
    614707 => 'Telstra',
    61472 => 'Telstra',
    61473 => 'Telstra',
    61474 => 'Telstra',
    61475 => 'Telstra',
    61476 => 'Telstra',
    61477 => 'Telstra',
    61478 => 'Optus',
    61479 => 'Optus',
    614800 => 'Pivotel Satellite',
    614801 => 'Telstra',
    614802 => 'Telstra',
    614803 => 'Telstra',
    614804 => 'Telstra',
    614805 => 'Telstra',
    614806 => 'Telstra',
    614807 => 'Telstra',
    614808 => 'Pivotel Satellite',
    61481 => 'Optus',
    61482 => 'Optus',
    614830 => 'Telstra',
    614831 => 'Telstra',
    614832 => 'Telstra',
    614833 => 'Telstra',
    614838 => 'Telstra',
    614839 => 'Optus',
    61484 => 'Telstra',
    61485 => 'TravelSIM',
    614858 => 'Pivotel Satellite',
    61486 => 'Telstra',
    61487 => 'Telstra',
    61488 => 'Telstra',
    614888 => 'My Number',
    614890 => 'Optus',
    614891 => 'Optus',
    614892 => 'Optus',
    614893 => 'Optus',
    6148984 => 'Victorian Rail Track',
    6148985 => 'MessageBird',
    614899 => 'Pivotel Satellite',
    6149 => 'Telstra',
];
