<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    35987 => 'Vivacom',
    35988 => 'A1',
    35989 => 'Telenor',
    359988 => 'Bob',
    359989 => 'A1',
    3599960 => 'A1',
    3599961 => 'A1',
    3599962 => 'A1',
    3599964 => 'Telenor',
    3599965 => 'Telenor',
    3599966 => 'Telenor',
    3599967 => 'Vivacom',
    3599968 => 'Vivacom',
    3599969 => 'Vivacom',
    3599990 => 'A1',
    3599991 => 'A1',
    3599992 => 'A1',
    3599993 => 'A1',
    3599994 => 'Telenor',
    3599995 => 'Telenor',
    3599996 => 'Vivacom',
    3599997 => 'Vivacom',
    3599998 => 'Vivacom',
    3599999 => 'Vivacom',
];
