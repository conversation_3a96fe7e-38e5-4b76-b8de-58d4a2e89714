<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    26732 => 'Mascom',
    26771 => 'Mascom',
    26772 => 'Orange',
    26773 => 'BTC Mobile',
    26774 => 'Mascom',
    267743 => 'Orange',
    267744 => 'Orange',
    267748 => 'Orange',
    267749 => 'BTC Mobile',
    267750 => 'Orange',
    267751 => 'Orange',
    267752 => 'Orange',
    267753 => 'Orange',
    267754 => 'Mascom',
    267755 => 'Mascom',
    267756 => 'Mascom',
    267757 => 'Orange',
    267758 => 'BTC Mobile',
    267759 => 'Mascom',
    267760 => 'Mascom',
    267761 => 'Mascom',
    267762 => 'Mascom',
    267763 => 'Orange',
    267764 => 'Orange',
    267765 => 'Orange',
    267766 => 'Mascom',
    267767 => 'Mascom',
    267768 => 'BTC Mobile',
    267769 => 'Orange',
    267770 => 'Mascom',
    267771 => 'Mascom',
    267772 => 'BTC Mobile',
    267773 => 'Orange',
    267774 => 'Orange',
    267775 => 'Orange',
    267776 => 'Mascom',
    267777 => 'Mascom',
    267778 => 'Mascom',
    267779 => 'Orange',
    26778 => 'Orange',
];
