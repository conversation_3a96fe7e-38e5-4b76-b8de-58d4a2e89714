<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    9982 => 'Beeline',
    9983 => 'HUMANS',
    99850 => 'Ucell',
    99861220 => 'MobiUZ',
    99861221 => 'MobiUZ',
    9986129 => 'MobiUZ',
    9986135 => 'MobiUZ',
    9986150 => 'MobiUZ',
    9986161 => 'MobiUZ',
    998617 => 'MobiUZ',
    998621 => 'MobiUZ',
    9986221 => 'MobiUZ',
    99862221 => 'MobiUZ',
    9986229 => 'MobiUZ',
    998625 => 'MobiUZ',
    998627 => 'MobiUZ',
    998651 => 'MobiUZ',
    99865227 => 'MobiUZ',
    9986529 => 'MobiUZ',
    9986530 => 'MobiUZ',
    9986559 => 'MobiUZ',
    998657 => 'MobiUZ',
    99866215 => 'MobiUZ',
    99866216 => 'MobiUZ',
    99866217 => 'MobiUZ',
    99866218 => 'MobiUZ',
    99866219 => 'MobiUZ',
    99866220 => 'MobiUZ',
    99866223 => 'MobiUZ',
    99866226 => 'MobiUZ',
    99866227 => 'MobiUZ',
    99866238 => 'MobiUZ',
    99866241 => 'MobiUZ',
    99866252 => 'MobiUZ',
    99866260 => 'MobiUZ',
    9986637 => 'MobiUZ',
    9986639 => 'MobiUZ',
    99866456 => 'MobiUZ',
    99866483 => 'MobiUZ',
    9986670 => 'MobiUZ',
    99866710 => 'MobiUZ',
    99866711 => 'MobiUZ',
    99866717 => 'MobiUZ',
    99866730 => 'MobiUZ',
    99866737 => 'MobiUZ',
    99866740 => 'MobiUZ',
    99866744 => 'MobiUZ',
    99866747 => 'MobiUZ',
    99866750 => 'MobiUZ',
    99866755 => 'MobiUZ',
    99866757 => 'MobiUZ',
    99866767 => 'MobiUZ',
    9986677 => 'MobiUZ',
    99866780 => 'MobiUZ',
    99866781 => 'MobiUZ',
    99866787 => 'MobiUZ',
    99866788 => 'MobiUZ',
    99866797 => 'MobiUZ',
    99866799 => 'MobiUZ',
    9986690 => 'MobiUZ',
    9986691 => 'MobiUZ',
    9986692 => 'MobiUZ',
    9986693 => 'MobiUZ',
    99867224 => 'MobiUZ',
    99867232 => 'MobiUZ',
    99867233 => 'MobiUZ',
    99867237 => 'MobiUZ',
    99867245 => 'MobiUZ',
    99867246 => 'MobiUZ',
    99867247 => 'MobiUZ',
    99867248 => 'MobiUZ',
    99867249 => 'MobiUZ',
    99867271 => 'MobiUZ',
    99867275 => 'MobiUZ',
    99867276 => 'MobiUZ',
    99867277 => 'MobiUZ',
    99867278 => 'MobiUZ',
    998675 => 'MobiUZ',
    9986770 => 'MobiUZ',
    9986773 => 'MobiUZ',
    99867790 => 'MobiUZ',
    99867797 => 'MobiUZ',
    998679 => 'MobiUZ',
    99870 => 'MobiUZ',
    99872227 => 'MobiUZ',
    99872229 => 'MobiUZ',
    9987229 => 'MobiUZ',
    9987232 => 'MobiUZ',
    9987236 => 'MobiUZ',
    9987257 => 'MobiUZ',
    998727 => 'MobiUZ',
    99873210 => 'MobiUZ',
    99873211 => 'MobiUZ',
    99873212 => 'MobiUZ',
    99873213 => 'MobiUZ',
    99873214 => 'MobiUZ',
    99873215 => 'MobiUZ',
    99873216 => 'MobiUZ',
    99873221 => 'MobiUZ',
    99873234 => 'MobiUZ',
    99873236 => 'MobiUZ',
    99873239 => 'MobiUZ',
    99873271 => 'MobiUZ',
    99873275 => 'MobiUZ',
    99873279 => 'MobiUZ',
    9987333 => 'MobiUZ',
    9987350 => 'MobiUZ',
    99873555 => 'MobiUZ',
    99873557 => 'MobiUZ',
    99873559 => 'MobiUZ',
    9987359 => 'MobiUZ',
    998737 => 'MobiUZ',
    998739 => 'MobiUZ',
    99874229 => 'MobiUZ',
    99874250 => 'MobiUZ',
    99874252 => 'MobiUZ',
    99874255 => 'MobiUZ',
    99874257 => 'MobiUZ',
    99874260 => 'MobiUZ',
    99874261 => 'MobiUZ',
    99874262 => 'MobiUZ',
    99874263 => 'MobiUZ',
    99874264 => 'MobiUZ',
    99874265 => 'MobiUZ',
    99874266 => 'MobiUZ',
    99874267 => 'MobiUZ',
    99874271 => 'MobiUZ',
    99874272 => 'MobiUZ',
    99874273 => 'MobiUZ',
    99874274 => 'MobiUZ',
    99874275 => 'MobiUZ',
    99874277 => 'MobiUZ',
    998745 => 'MobiUZ',
    9987470 => 'MobiUZ',
    99874710 => 'MobiUZ',
    99874712 => 'MobiUZ',
    99874714 => 'MobiUZ',
    99874715 => 'MobiUZ',
    99874718 => 'MobiUZ',
    99874719 => 'MobiUZ',
    99874720 => 'MobiUZ',
    99874721 => 'MobiUZ',
    99874722 => 'MobiUZ',
    99874727 => 'MobiUZ',
    99874730 => 'MobiUZ',
    99874731 => 'MobiUZ',
    99874733 => 'MobiUZ',
    99874737 => 'MobiUZ',
    99874740 => 'MobiUZ',
    99874747 => 'MobiUZ',
    99874750 => 'MobiUZ',
    99874751 => 'MobiUZ',
    99874760 => 'MobiUZ',
    99874767 => 'MobiUZ',
    99874775 => 'MobiUZ',
    99874776 => 'MobiUZ',
    99874777 => 'MobiUZ',
    99874778 => 'MobiUZ',
    99874779 => 'MobiUZ',
    99874790 => 'MobiUZ',
    99874797 => 'MobiUZ',
    99874799 => 'MobiUZ',
    998749 => 'MobiUZ',
    998751 => 'MobiUZ',
    9987520 => 'MobiUZ',
    99875222 => 'MobiUZ',
    99875229 => 'MobiUZ',
    9987524 => 'MobiUZ',
    9987529 => 'MobiUZ',
    998753 => 'MobiUZ',
    99875526 => 'MobiUZ',
    99875527 => 'MobiUZ',
    99875528 => 'MobiUZ',
    99875529 => 'MobiUZ',
    998757 => 'MobiUZ',
    99877 => 'Uzbektelecom',
    99879221 => 'MobiUZ',
    99879222 => 'MobiUZ',
    99879228 => 'MobiUZ',
    998793 => 'MobiUZ',
    99879570 => 'MobiUZ',
    99879572 => 'MobiUZ',
    99879575 => 'MobiUZ',
    99879576 => 'MobiUZ',
    99879579 => 'MobiUZ',
    998797 => 'MobiUZ',
    9988 => 'MobiUZ',
    99890 => 'Beeline',
    99891 => 'Beeline',
    99892 => 'MobiUZ',
    99893 => 'Ucell',
    99894 => 'Ucell',
    99895 => 'Uzbektelecom',
    99897 => 'MobiUZ',
    99898 => 'Perfectum',
    99899 => 'Uzbektelecom',
];
