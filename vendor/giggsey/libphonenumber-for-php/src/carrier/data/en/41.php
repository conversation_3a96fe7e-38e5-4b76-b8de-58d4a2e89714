<?php

/**
 * This file has been @generated by a phing task by {@link GeneratePhonePrefixData}.
 * See [README.md](README.md#generating-data) for more information.
 *
 * Pull requests changing data in these files will not be accepted. See the
 * [FAQ in the README](README.md#problems-with-invalid-numbers] on how to make
 * metadata changes.
 *
 * Do not modify this file directly!
 */

return  [
    4168 => 'Swisscom',
    4169 => 'Swisscom',
    4172 => 'Swisscom',
    4173 => 'Swisscom',
    417500 => 'Swisscom',
    417507 => 'Swisscom',
    417508 => 'Swisscom',
    417509 => 'Swisscom',
    417519 => 'Swisscom',
    41752 => 'Swisscom',
    41753 => 'Swisscom',
    41754 => 'Swisscom',
    417550 => 'Swisscom',
    417551 => 'Swisscom',
    417552 => 'Swisscom',
    417553 => 'Swisscom',
    417557 => 'Swisscom',
    41757 => 'Swisscom',
    417600 => 'Sunrise',
    417603 => 'Sunrise',
    417604 => 'Sunrise',
    417605 => 'Sunrise',
    41762 => 'Sunrise',
    41763 => 'Sunrise',
    41764 => 'Sunrise',
    41765 => 'Sunrise',
    41766 => 'Sunrise',
    41767 => 'Sunrise',
    41768 => 'Sunrise',
    41769 => 'Sunrise',
    41770 => 'Swisscom',
    417710 => 'Swisscom',
    417712 => 'Swisscom',
    417713 => 'Swisscom',
    417715 => 'Swisscom',
    41772 => 'Sunrise',
    417730 => 'Sunrise',
    4177310 => 'Sunrise',
    4177311 => 'Sunrise',
    4177312 => 'Sunrise',
    4177313 => 'Sunrise',
    4177314 => 'Sunrise',
    4177315 => 'Sunrise',
    4177316 => 'Sunrise',
    4177357 => 'In&Phone',
    41774 => 'Swisscom',
    417750 => 'Swisscom',
    417751 => 'Swisscom',
    417752 => 'Swisscom',
    417753 => 'Swisscom',
    417780 => 'BeeOne Communications',
    417781 => 'BeeOne Communications',
    417788 => 'Vectone Mobile Limited (Mundio)',
    417789 => 'Vectone Mobile Limited (Mundio)',
    41779 => 'Lycamobile',
    41780 => 'Salt',
    41781 => 'Salt',
    41782 => 'Salt',
    41783 => 'Salt',
    417840 => 'Sunrise',
    417841 => 'Sunrise',
    417842 => 'Sunrise',
    417844 => 'spusu',
    4178460 => 'Tismi',
    4178461 => 'Tismi',
    4178462 => 'Tismi',
    4178463 => 'Tismi',
    417847 => 'MTEL',
    4178480 => 'Nexphone',
    4178481 => 'Nexphone',
    4178482 => 'Nexphone',
    4178490 => 'Telecom26 AG',
    41785 => 'Salt',
    41786 => 'Salt',
    41787 => 'Salt',
    41788 => 'Salt',
    41789 => 'Salt',
    41790 => 'Swisscom',
    41791 => 'Swisscom',
    41792 => 'Swisscom',
    41793 => 'Swisscom',
    41794 => 'Swisscom',
    41795 => 'Swisscom',
    41796 => 'Swisscom',
    41797 => 'Swisscom',
    41798 => 'Swisscom',
    417990 => 'Swisscom',
    417991 => 'Swisscom',
    417992 => 'Swisscom',
    417993 => 'Swisscom',
    417994 => 'Swisscom',
    417995 => 'Swisscom',
    417996 => 'Swisscom',
    4179977 => 'Relario AG (Bebbicell)',
    4179978 => 'Relario AG (Bebbicell)',
    4179979 => 'Relario AG (Bebbicell)',
    417999 => 'Comfone AG',
];
